# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cupid_subprocess_service.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import service as _service
from google.protobuf import service_reflection
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='cupid_subprocess_service.proto',
  package='apsara.odps.cupid.protocol',
  serialized_pb=_b('\n\x1e\x63upid_subprocess_service.proto\x12\x1a\x61psara.odps.cupid.protocol\"\xb7\x01\n\nInputSplit\x12\x14\n\x0csplitIndexId\x18\x01 \x01(\r\x12\x16\n\x0esplitFileStart\x18\x02 \x01(\x04\x12\x14\n\x0csplitFileEnd\x18\x03 \x01(\x04\x12\x17\n\x0fschemaFileStart\x18\x04 \x01(\x04\x12\x15\n\rschemaFileEnd\x18\x05 \x01(\x04\x12\x0f\n\x07project\x18\x06 \x01(\t\x12\r\n\x05table\x18\x07 \x01(\t\x12\x15\n\rpartitionSpec\x18\x08 \x01(\t\"3\n\x0eInputSplitMeta\x12\x10\n\x08rowCount\x18\x01 \x01(\x04\x12\x0f\n\x07rawSize\x18\x02 \x01(\x04\"0\n\x14GetSplitsMetaRequest\x12\x18\n\x10inputTableHandle\x18\x01 \x01(\t\"\\\n\x15GetSplitsMetaResponse\x12\x43\n\x0finputSplitsMeta\x18\x01 \x03(\x0b\x32*.apsara.odps.cupid.protocol.InputSplitMeta\",\n\x10GetSplitsRequest\x12\x18\n\x10inputTableHandle\x18\x01 \x01(\t\"P\n\x11GetSplitsResponse\x12;\n\x0binputSplits\x18\x01 \x03(\x0b\x32&.apsara.odps.cupid.protocol.InputSplit\"r\n\x1aRegisterTableReaderRequest\x12\x18\n\x10inputTableHandle\x18\x01 \x01(\t\x12:\n\ninputSplit\x18\x02 \x01(\x0b\x32&.apsara.odps.cupid.protocol.InputSplit\"\\\n\x1bRegisterTableReaderResponse\x12\x0e\n\x06schema\x18\x01 \x01(\t\x12\x14\n\x0creadIterator\x18\x02 \x01(\t\x12\x17\n\x0fpartitionSchema\x18\x03 \x01(\t\"\xb5\x01\n\x1aRegisterTableWriterRequest\x12\x19\n\x11outputTableHandle\x18\x01 \x01(\t\x12\x13\n\x0bprojectName\x18\x02 \x01(\t\x12\x11\n\ttableName\x18\x03 \x01(\t\x12\x17\n\x0f\x61ttemptFileName\x18\x04 \x01(\t\x12\x10\n\x08partSpec\x18\x05 \x01(\t\x12\x0e\n\x06schema\x18\x06 \x01(\t\x12\x19\n\x11tableMetaFileName\x18\x07 \x01(\t\"@\n\x1bRegisterTableWriterResponse\x12!\n\x19subprocessWriteTableLabel\x18\x01 \x01(\t\"U\n\x10\x43ommitActionInfo\x12\x10\n\x08partSpec\x18\x01 \x01(\t\x12\x17\n\x0f\x61ttemptFileName\x18\x02 \x01(\t\x12\x16\n\x0e\x63ommitFileName\x18\x03 \x01(\t\"\xa5\x01\n\x17\x43ommitTableFilesRequest\x12\x19\n\x11outputTableHandle\x18\x01 \x01(\t\x12\x13\n\x0bprojectName\x18\x02 \x01(\t\x12\x11\n\ttableName\x18\x03 \x01(\t\x12G\n\x11\x63ommitActionInfos\x18\x04 \x03(\x0b\x32,.apsara.odps.cupid.protocol.CommitActionInfo\"\x1a\n\x18\x43ommitTableFilesResponse2\x89\x05\n\x16\x43upidSubProcessService\x12h\n\tGetSplits\x12,.apsara.odps.cupid.protocol.GetSplitsRequest\x1a-.apsara.odps.cupid.protocol.GetSplitsResponse\x12\x86\x01\n\x13RegisterTableReader\x12\x36.apsara.odps.cupid.protocol.RegisterTableReaderRequest\x1a\x37.apsara.odps.cupid.protocol.RegisterTableReaderResponse\x12\x86\x01\n\x13RegisterTableWriter\x12\x36.apsara.odps.cupid.protocol.RegisterTableWriterRequest\x1a\x37.apsara.odps.cupid.protocol.RegisterTableWriterResponse\x12}\n\x10\x43ommitTableFiles\x12\x33.apsara.odps.cupid.protocol.CommitTableFilesRequest\x1a\x34.apsara.odps.cupid.protocol.CommitTableFilesResponse\x12t\n\rGetSplitsMeta\x12\x30.apsara.odps.cupid.protocol.GetSplitsMetaRequest\x1a\x31.apsara.odps.cupid.protocol.GetSplitsMetaResponseB&B\x1b\x43upidSubProcessServiceProto\x80\x01\x01\x88\x01\x01\x90\x01\x01')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_INPUTSPLIT = _descriptor.Descriptor(
  name='InputSplit',
  full_name='apsara.odps.cupid.protocol.InputSplit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='splitIndexId', full_name='apsara.odps.cupid.protocol.InputSplit.splitIndexId', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitFileStart', full_name='apsara.odps.cupid.protocol.InputSplit.splitFileStart', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitFileEnd', full_name='apsara.odps.cupid.protocol.InputSplit.splitFileEnd', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schemaFileStart', full_name='apsara.odps.cupid.protocol.InputSplit.schemaFileStart', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schemaFileEnd', full_name='apsara.odps.cupid.protocol.InputSplit.schemaFileEnd', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='project', full_name='apsara.odps.cupid.protocol.InputSplit.project', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='table', full_name='apsara.odps.cupid.protocol.InputSplit.table', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partitionSpec', full_name='apsara.odps.cupid.protocol.InputSplit.partitionSpec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=63,
  serialized_end=246,
)


_INPUTSPLITMETA = _descriptor.Descriptor(
  name='InputSplitMeta',
  full_name='apsara.odps.cupid.protocol.InputSplitMeta',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rowCount', full_name='apsara.odps.cupid.protocol.InputSplitMeta.rowCount', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='rawSize', full_name='apsara.odps.cupid.protocol.InputSplitMeta.rawSize', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=248,
  serialized_end=299,
)


_GETSPLITSMETAREQUEST = _descriptor.Descriptor(
  name='GetSplitsMetaRequest',
  full_name='apsara.odps.cupid.protocol.GetSplitsMetaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputTableHandle', full_name='apsara.odps.cupid.protocol.GetSplitsMetaRequest.inputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=301,
  serialized_end=349,
)


_GETSPLITSMETARESPONSE = _descriptor.Descriptor(
  name='GetSplitsMetaResponse',
  full_name='apsara.odps.cupid.protocol.GetSplitsMetaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputSplitsMeta', full_name='apsara.odps.cupid.protocol.GetSplitsMetaResponse.inputSplitsMeta', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=351,
  serialized_end=443,
)


_GETSPLITSREQUEST = _descriptor.Descriptor(
  name='GetSplitsRequest',
  full_name='apsara.odps.cupid.protocol.GetSplitsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputTableHandle', full_name='apsara.odps.cupid.protocol.GetSplitsRequest.inputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=445,
  serialized_end=489,
)


_GETSPLITSRESPONSE = _descriptor.Descriptor(
  name='GetSplitsResponse',
  full_name='apsara.odps.cupid.protocol.GetSplitsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputSplits', full_name='apsara.odps.cupid.protocol.GetSplitsResponse.inputSplits', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=491,
  serialized_end=571,
)


_REGISTERTABLEREADERREQUEST = _descriptor.Descriptor(
  name='RegisterTableReaderRequest',
  full_name='apsara.odps.cupid.protocol.RegisterTableReaderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputTableHandle', full_name='apsara.odps.cupid.protocol.RegisterTableReaderRequest.inputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='inputSplit', full_name='apsara.odps.cupid.protocol.RegisterTableReaderRequest.inputSplit', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=573,
  serialized_end=687,
)


_REGISTERTABLEREADERRESPONSE = _descriptor.Descriptor(
  name='RegisterTableReaderResponse',
  full_name='apsara.odps.cupid.protocol.RegisterTableReaderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema', full_name='apsara.odps.cupid.protocol.RegisterTableReaderResponse.schema', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='readIterator', full_name='apsara.odps.cupid.protocol.RegisterTableReaderResponse.readIterator', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partitionSchema', full_name='apsara.odps.cupid.protocol.RegisterTableReaderResponse.partitionSchema', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=689,
  serialized_end=781,
)


_REGISTERTABLEWRITERREQUEST = _descriptor.Descriptor(
  name='RegisterTableWriterRequest',
  full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='outputTableHandle', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.outputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.projectName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.tableName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='attemptFileName', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.attemptFileName', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpec', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.partSpec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.schema', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableMetaFileName', full_name='apsara.odps.cupid.protocol.RegisterTableWriterRequest.tableMetaFileName', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=784,
  serialized_end=965,
)


_REGISTERTABLEWRITERRESPONSE = _descriptor.Descriptor(
  name='RegisterTableWriterResponse',
  full_name='apsara.odps.cupid.protocol.RegisterTableWriterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='subprocessWriteTableLabel', full_name='apsara.odps.cupid.protocol.RegisterTableWriterResponse.subprocessWriteTableLabel', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=967,
  serialized_end=1031,
)


_COMMITACTIONINFO = _descriptor.Descriptor(
  name='CommitActionInfo',
  full_name='apsara.odps.cupid.protocol.CommitActionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partSpec', full_name='apsara.odps.cupid.protocol.CommitActionInfo.partSpec', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='attemptFileName', full_name='apsara.odps.cupid.protocol.CommitActionInfo.attemptFileName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='commitFileName', full_name='apsara.odps.cupid.protocol.CommitActionInfo.commitFileName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1033,
  serialized_end=1118,
)


_COMMITTABLEFILESREQUEST = _descriptor.Descriptor(
  name='CommitTableFilesRequest',
  full_name='apsara.odps.cupid.protocol.CommitTableFilesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='outputTableHandle', full_name='apsara.odps.cupid.protocol.CommitTableFilesRequest.outputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.CommitTableFilesRequest.projectName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.CommitTableFilesRequest.tableName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='commitActionInfos', full_name='apsara.odps.cupid.protocol.CommitTableFilesRequest.commitActionInfos', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1121,
  serialized_end=1286,
)


_COMMITTABLEFILESRESPONSE = _descriptor.Descriptor(
  name='CommitTableFilesResponse',
  full_name='apsara.odps.cupid.protocol.CommitTableFilesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1288,
  serialized_end=1314,
)

_GETSPLITSMETARESPONSE.fields_by_name['inputSplitsMeta'].message_type = _INPUTSPLITMETA
_GETSPLITSRESPONSE.fields_by_name['inputSplits'].message_type = _INPUTSPLIT
_REGISTERTABLEREADERREQUEST.fields_by_name['inputSplit'].message_type = _INPUTSPLIT
_COMMITTABLEFILESREQUEST.fields_by_name['commitActionInfos'].message_type = _COMMITACTIONINFO
DESCRIPTOR.message_types_by_name['InputSplit'] = _INPUTSPLIT
DESCRIPTOR.message_types_by_name['InputSplitMeta'] = _INPUTSPLITMETA
DESCRIPTOR.message_types_by_name['GetSplitsMetaRequest'] = _GETSPLITSMETAREQUEST
DESCRIPTOR.message_types_by_name['GetSplitsMetaResponse'] = _GETSPLITSMETARESPONSE
DESCRIPTOR.message_types_by_name['GetSplitsRequest'] = _GETSPLITSREQUEST
DESCRIPTOR.message_types_by_name['GetSplitsResponse'] = _GETSPLITSRESPONSE
DESCRIPTOR.message_types_by_name['RegisterTableReaderRequest'] = _REGISTERTABLEREADERREQUEST
DESCRIPTOR.message_types_by_name['RegisterTableReaderResponse'] = _REGISTERTABLEREADERRESPONSE
DESCRIPTOR.message_types_by_name['RegisterTableWriterRequest'] = _REGISTERTABLEWRITERREQUEST
DESCRIPTOR.message_types_by_name['RegisterTableWriterResponse'] = _REGISTERTABLEWRITERRESPONSE
DESCRIPTOR.message_types_by_name['CommitActionInfo'] = _COMMITACTIONINFO
DESCRIPTOR.message_types_by_name['CommitTableFilesRequest'] = _COMMITTABLEFILESREQUEST
DESCRIPTOR.message_types_by_name['CommitTableFilesResponse'] = _COMMITTABLEFILESRESPONSE

InputSplit = _reflection.GeneratedProtocolMessageType('InputSplit', (_message.Message,), dict(
  DESCRIPTOR = _INPUTSPLIT,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.InputSplit)
  ))
_sym_db.RegisterMessage(InputSplit)

InputSplitMeta = _reflection.GeneratedProtocolMessageType('InputSplitMeta', (_message.Message,), dict(
  DESCRIPTOR = _INPUTSPLITMETA,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.InputSplitMeta)
  ))
_sym_db.RegisterMessage(InputSplitMeta)

GetSplitsMetaRequest = _reflection.GeneratedProtocolMessageType('GetSplitsMetaRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSPLITSMETAREQUEST,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetSplitsMetaRequest)
  ))
_sym_db.RegisterMessage(GetSplitsMetaRequest)

GetSplitsMetaResponse = _reflection.GeneratedProtocolMessageType('GetSplitsMetaResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSPLITSMETARESPONSE,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetSplitsMetaResponse)
  ))
_sym_db.RegisterMessage(GetSplitsMetaResponse)

GetSplitsRequest = _reflection.GeneratedProtocolMessageType('GetSplitsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSPLITSREQUEST,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetSplitsRequest)
  ))
_sym_db.RegisterMessage(GetSplitsRequest)

GetSplitsResponse = _reflection.GeneratedProtocolMessageType('GetSplitsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSPLITSRESPONSE,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetSplitsResponse)
  ))
_sym_db.RegisterMessage(GetSplitsResponse)

RegisterTableReaderRequest = _reflection.GeneratedProtocolMessageType('RegisterTableReaderRequest', (_message.Message,), dict(
  DESCRIPTOR = _REGISTERTABLEREADERREQUEST,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.RegisterTableReaderRequest)
  ))
_sym_db.RegisterMessage(RegisterTableReaderRequest)

RegisterTableReaderResponse = _reflection.GeneratedProtocolMessageType('RegisterTableReaderResponse', (_message.Message,), dict(
  DESCRIPTOR = _REGISTERTABLEREADERRESPONSE,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.RegisterTableReaderResponse)
  ))
_sym_db.RegisterMessage(RegisterTableReaderResponse)

RegisterTableWriterRequest = _reflection.GeneratedProtocolMessageType('RegisterTableWriterRequest', (_message.Message,), dict(
  DESCRIPTOR = _REGISTERTABLEWRITERREQUEST,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.RegisterTableWriterRequest)
  ))
_sym_db.RegisterMessage(RegisterTableWriterRequest)

RegisterTableWriterResponse = _reflection.GeneratedProtocolMessageType('RegisterTableWriterResponse', (_message.Message,), dict(
  DESCRIPTOR = _REGISTERTABLEWRITERRESPONSE,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.RegisterTableWriterResponse)
  ))
_sym_db.RegisterMessage(RegisterTableWriterResponse)

CommitActionInfo = _reflection.GeneratedProtocolMessageType('CommitActionInfo', (_message.Message,), dict(
  DESCRIPTOR = _COMMITACTIONINFO,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitActionInfo)
  ))
_sym_db.RegisterMessage(CommitActionInfo)

CommitTableFilesRequest = _reflection.GeneratedProtocolMessageType('CommitTableFilesRequest', (_message.Message,), dict(
  DESCRIPTOR = _COMMITTABLEFILESREQUEST,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitTableFilesRequest)
  ))
_sym_db.RegisterMessage(CommitTableFilesRequest)

CommitTableFilesResponse = _reflection.GeneratedProtocolMessageType('CommitTableFilesResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMITTABLEFILESRESPONSE,
  __module__ = 'cupid_subprocess_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitTableFilesResponse)
  ))
_sym_db.RegisterMessage(CommitTableFilesResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('B\033CupidSubProcessServiceProto\200\001\001\210\001\001\220\001\001'))

_CUPIDSUBPROCESSSERVICE = _descriptor.ServiceDescriptor(
  name='CupidSubProcessService',
  full_name='apsara.odps.cupid.protocol.CupidSubProcessService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=1317,
  serialized_end=1966,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetSplits',
    full_name='apsara.odps.cupid.protocol.CupidSubProcessService.GetSplits',
    index=0,
    containing_service=None,
    input_type=_GETSPLITSREQUEST,
    output_type=_GETSPLITSRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RegisterTableReader',
    full_name='apsara.odps.cupid.protocol.CupidSubProcessService.RegisterTableReader',
    index=1,
    containing_service=None,
    input_type=_REGISTERTABLEREADERREQUEST,
    output_type=_REGISTERTABLEREADERRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RegisterTableWriter',
    full_name='apsara.odps.cupid.protocol.CupidSubProcessService.RegisterTableWriter',
    index=2,
    containing_service=None,
    input_type=_REGISTERTABLEWRITERREQUEST,
    output_type=_REGISTERTABLEWRITERRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CommitTableFiles',
    full_name='apsara.odps.cupid.protocol.CupidSubProcessService.CommitTableFiles',
    index=3,
    containing_service=None,
    input_type=_COMMITTABLEFILESREQUEST,
    output_type=_COMMITTABLEFILESRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetSplitsMeta',
    full_name='apsara.odps.cupid.protocol.CupidSubProcessService.GetSplitsMeta',
    index=4,
    containing_service=None,
    input_type=_GETSPLITSMETAREQUEST,
    output_type=_GETSPLITSMETARESPONSE,
    options=None,
  ),
])

CupidSubProcessService = service_reflection.GeneratedServiceType('CupidSubProcessService', (_service.Service,), dict(
  DESCRIPTOR = _CUPIDSUBPROCESSSERVICE,
  __module__ = 'cupid_subprocess_service_pb2'
  ))

CupidSubProcessService_Stub = service_reflection.GeneratedServiceStubType('CupidSubProcessService_Stub', (CupidSubProcessService,), dict(
  DESCRIPTOR = _CUPIDSUBPROCESSSERVICE,
  __module__ = 'cupid_subprocess_service_pb2'
  ))


# @@protoc_insertion_point(module_scope)
