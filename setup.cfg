[flake8]
max-line-length = 88
select =
    E9,
    E101,
    E111,
    E117,
    E127,
    E201,
    E202,
    E223,
    E224,
    E225,
    E231,
    E242,
    E251,
    E273,
    E274,
    E275,
    E301,
    E302,
    E303,
    E304,
    E305,
    E401,
    E703,
    E901,
    E999,
    F7,
    F63,
    F82,
    F401,
    F811,
    F821,
    F822,
    F823,
    F841,
    W191,
    W291,
    W292,
    W293,
    W391,
    W601,
    W602,
    W603,
    W604,
    W605
exclude =
    __init__.py
    __pycache__
    .git/
    benchmarks/
    build/
    bin/
    cupid/
    dist/
    docs/
    env/
    examples/
    misc/
    odps/compat.py
    odps/df/*
    odps/lib/*
    odps/mars_extension/*
    odps/ml/*
    odps/udf/*
    odps_scripts/*
    *.pyi
    setup.py
