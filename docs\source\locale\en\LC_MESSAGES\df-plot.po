# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-05-17 10:30+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-plot.rst:4
msgid "绘图"
msgstr "Plotting"

#: ../../source/df-plot.rst:6
msgid ""
"PyODPS DataFrame提供了绘图的方法。如果要使用绘图，需要 **pandas** 和 **"
"matplotlib** 的安装。"
msgstr ""
"PyODPS DataFrame provides plotting. To enable plotting, install the "
"**pandas** and **matplotlib** libraries."

#: ../../source/df-plot.rst:9
msgid "接下来的例子都是在jupyter中运行。"
msgstr "The following examples run in Jupyter:"

#: ../../source/df-plot.rst:18
msgid ""
">>> from odps.df import DataFrame\n"
">>> iris = DataFrame(o.get_table('pyodps_iris'))\n"
">>> %matplotlib inline\n"
">>> iris.sepalwidth.plot()\n"
"<matplotlib.axes._subplots.AxesSubplot at 0x10c2b3510>"
msgstr ""

#: ../../source/df-plot.rst:20
msgid ".. image:: _static/df-plot-iris-sequence.png"
msgstr ""

#: ../../source/df-plot.rst:25
msgid ""
">>> iris.plot()\n"
"<matplotlib.axes._subplots.AxesSubplot at 0x10db7e690>"
msgstr ""

#: ../../source/df-plot.rst:27
msgid ".. image:: _static/df-plot-iris-collection.png"
msgstr ""

#: ../../source/df-plot.rst:32
msgid ""
">>> iris.groupby('name').sum().plot(kind='bar', x='name', stacked=True, "
"rot=30)\n"
"<matplotlib.axes._subplots.AxesSubplot at 0x10c5f2090>"
msgstr ""

#: ../../source/df-plot.rst:34
msgid ".. image:: _static/df-plot-iris-sum.png"
msgstr ""

#: ../../source/df-plot.rst:42
msgid ""
">>> iris.hist(sharex=True)\n"
"array([[<matplotlib.axes._subplots.AxesSubplot object at 0x10e013f90>,\n"
"        <matplotlib.axes._subplots.AxesSubplot object at 0x10e2d1c10>],\n"
"       [<matplotlib.axes._subplots.AxesSubplot object at 0x10e353f10>,\n"
"        <matplotlib.axes._subplots.AxesSubplot object at 0x10e3c4410>]], "
"dtype=object)"
msgstr ""

#: ../../source/df-plot.rst:44
msgid ".. image:: _static/df-plot-iris-hist.png"
msgstr ""

#: ../../source/df-plot.rst:45
msgid "参数\\ ``kind``\\ 表示了绘图的类型，支持的包括："
msgstr ""
"The \\ ``kind``\\ parameter specifies the plotting type, and supports the"
" following types:"

#: ../../source/df-plot.rst:48
msgid "kind"
msgstr ""

#: ../../source/df-plot.rst:48 ../../source/df-plot.rst:68
msgid "说明"
msgstr "Description"

#: ../../source/df-plot.rst:50
msgid "line"
msgstr ""

#: ../../source/df-plot.rst:50
msgid "线图"
msgstr "Line chart"

#: ../../source/df-plot.rst:51
msgid "bar"
msgstr ""

#: ../../source/df-plot.rst:51
msgid "竖向柱状图"
msgstr "Vertical bar chart"

#: ../../source/df-plot.rst:52
msgid "barh"
msgstr ""

#: ../../source/df-plot.rst:52
msgid "横向柱状图"
msgstr "Horizontal bar chart"

#: ../../source/df-plot.rst:53
msgid "hist"
msgstr ""

#: ../../source/df-plot.rst:53
msgid "直方图"
msgstr "Histogram"

#: ../../source/df-plot.rst:54
msgid "box"
msgstr ""

#: ../../source/df-plot.rst:54
msgid "boxplot"
msgstr "Boxplot"

#: ../../source/df-plot.rst:55
msgid "kde"
msgstr ""

#: ../../source/df-plot.rst:55
msgid "核密度估计"
msgstr "Kernel density estimation"

#: ../../source/df-plot.rst:56
msgid "density"
msgstr ""

#: ../../source/df-plot.rst:56
msgid "和kde相同"
msgstr "Same as kernel density estimation"

#: ../../source/df-plot.rst:57
msgid "area"
msgstr ""

#: ../../source/df-plot.rst:58
msgid "pie"
msgstr ""

#: ../../source/df-plot.rst:58
msgid "饼图"
msgstr "Pie chart"

#: ../../source/df-plot.rst:59
msgid "scatter"
msgstr ""

#: ../../source/df-plot.rst:59
msgid "散点图"
msgstr "Scatter chart"

#: ../../source/df-plot.rst:60
msgid "hexbin"
msgstr ""

#: ../../source/df-plot.rst:63
msgid ""
"详细参数可以参考Pandas文档：http://pandas.pydata.org/pandas-docs/stable/"
"generated/pandas.DataFrame.plot.html"
msgstr ""
"For more information, see pandas.DataFrame.plot: http://pandas.pydata.org"
"/pandas-docs/stable/generated/pandas.DataFrame.plot.html"

#: ../../source/df-plot.rst:65
msgid "除此之外，plot函数还增加了几个参数，方便进行绘图。"
msgstr "The plot function also provides the following parameters for plotting:"

#: ../../source/df-plot.rst:68
msgid "参数"
msgstr "Parameter"

#: ../../source/df-plot.rst:70
msgid "xlabel"
msgstr ""

#: ../../source/df-plot.rst:70
msgid "x轴名"
msgstr "X axis name"

#: ../../source/df-plot.rst:71
msgid "ylabel"
msgstr ""

#: ../../source/df-plot.rst:71
msgid "y轴名"
msgstr "Y axis name"

#: ../../source/df-plot.rst:72
msgid "xlabelsize"
msgstr ""

#: ../../source/df-plot.rst:72
msgid "x轴名大小"
msgstr "Size of x axis name"

#: ../../source/df-plot.rst:73
msgid "ylabelsize"
msgstr ""

#: ../../source/df-plot.rst:73
msgid "y轴名大小"
msgstr "Size of y axis name"

#: ../../source/df-plot.rst:74
msgid "labelsize"
msgstr ""

#: ../../source/df-plot.rst:74
msgid "轴名大小"
msgstr "Axis name size"

#: ../../source/df-plot.rst:75
msgid "title"
msgstr ""

#: ../../source/df-plot.rst:75
msgid "标题"
msgstr "Title"

#: ../../source/df-plot.rst:76
msgid "titlesize"
msgstr ""

#: ../../source/df-plot.rst:76
msgid "标题大小"
msgstr "Title size"

#: ../../source/df-plot.rst:77
msgid "annotate"
msgstr ""

#: ../../source/df-plot.rst:77
msgid "是否标记值"
msgstr "Annotation"

