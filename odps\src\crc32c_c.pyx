# -*- coding: utf-8 -*-
# Copyright 1999-2022 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from libc.stdint cimport *


cdef extern from "crc32.c":
    uint32_t crc32c(uint32_t crc, const void *buf, size_t length) nogil

cdef class Crc32c:
    def __cinit__(self):
        self._crc = 0

    cpdef uint32_t getvalue(self):
        return self._crc

    cpdef update(self, bytearray buf):
        cdef char* cstring = <char *>buf
        self._crc = crc32c(self._crc, <const void*>cstring, len(buf))

    cpdef reset(self):
        self._crc = 0
