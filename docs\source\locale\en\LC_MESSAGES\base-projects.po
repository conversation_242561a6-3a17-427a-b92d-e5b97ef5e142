# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-17 11:44+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-projects.rst:4
msgid "项目空间"
msgstr "Projects"

#: ../../source/base-projects.rst:6
msgid ""
"`项目空间 <https://help.aliyun.com/document_detail/27818.html>`_ 是ODPS的"
"基本组织单元， 有点类似于Database的概念。"
msgstr ""
"A `project <https://www.alibabacloud.com/help/en/doc-detail/27818.htm>`_ "
"is a basic organizational unit of MaxCompute, which is similar to a "
"database."

#: ../../source/base-projects.rst:9
msgid ""
"我们通过 ODPS 入口对象的 :meth:`~odps.ODPS.get_project` 来取到某个项目"
"空间。"
msgstr ""
"Call the member function of ODPS entry project, "
":meth:`~odps.ODPS.get_project`, to obtain a project, as shown in the "
"following code:"

#: ../../source/base-projects.rst:11
msgid ""
"project = o.get_project('my_project')  # 取到某个项目\n"
"project = o.get_project()              # 取到默认项目"
msgstr ""
"project = o.get_project('my_project')  # get project object by name\n"
"project = o.get_project()              # get default project"

#: ../../source/base-projects.rst:16
msgid "如果不提供参数，则取到默认项目空间。"
msgstr "If parameters are not input, the default project will be returned."

#: ../../source/base-projects.rst:18
msgid ":meth:`~odps.ODPS.exist_project` 方法能检验某个项目空间是否存在。"
msgstr ""
"You can call :meth:`~odps.ODPS.exist_project` to check whether a project "
"exists."

