# Copyright 1999-2024 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import sys

import pytest

from ...config import options
from ...tests.core import tn

if sys.version_info[0] >= 3:
    from .storage_api import StorageApiArrowClient
else:
    StorageApiArrowClient = None


_test_table_id = 0


@pytest.fixture
def storage_api_client(odps):
    global _test_table_id

    options.enable_schema = True

    test_table_name = tn("test_halo_common_table_" + str(_test_table_id))
    _test_table_id += 1
    odps.delete_table(test_table_name, if_exists=True)
    table = odps.create_table(
        test_table_name,
        ("a BIGINT, b BIGINT, c BIGINT, d BIGINT", "pt string"),
        if_not_exists=True,
    )
    try:
        yield StorageApiArrowClient(odps, table)
    finally:
        table.drop(async_=True)
        options.enable_schema = False
