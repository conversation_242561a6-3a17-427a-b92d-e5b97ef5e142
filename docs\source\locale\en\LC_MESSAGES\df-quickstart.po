# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-19 13:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-quickstart.rst:5
msgid "快速开始"
msgstr "Quick start"

#: ../../source/df-quickstart.rst:7
msgid ""
"在本例子中，我们拿 `movielens 100K <https://grouplens.org/datasets/"
"movielens/100k/>`_ 来做例子。现在我们已经有三张表了，分别是\\ ``pyodps_ml"
"_100k_movies``\\ （电影相关的数据），\\ ``pyodps_ml_100k_users``\\ （用户"
"相关的数据），\\ ``pyodps_ml_100k_ratings``\\ （评分有关的数据）。"
msgstr ""
"Here, `movielens 100K <https://grouplens.org/datasets/movielens/100k/>`_ "
"is used as an example. Assume that three tables already exist, which are "
"``pyodps_ml_100k_movies`` (movie-related data), ``pyodps_ml_100k_users`` "
"(user-related data), and ``pyodps_ml_100k_ratings`` (rating-related "
"data)."

#: ../../source/df-quickstart.rst:10
msgid "如果你的运行环境没有提供 ODPS 对象，你需要自己创建该对象："
msgstr "Create a MaxCompute object before starting the following steps:"

#: ../../source/df-quickstart.rst:12
msgid ""
">>> import os\n"
">>> from odps import ODPS\n"
">>> # 保证 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为用户 Access Key ID，"
"\n"
">>> # ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为用户 Access Key "
"Secret\n"
">>> # 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
">>> o = ODPS(\n"
"...     os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"...     os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"...     project='**your-project**',\n"
"...     endpoint='**your-endpoint**',\n"
"... )"
msgstr ""
">>> import os\n"
">>> from odps import ODPS\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to Access Key ID of user\n"
"# while environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to "
"Access Key Secret of user.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
">>> o = ODPS(\n"
"...     os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"...     os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"...     project='**your-project**',\n"
"...     endpoint='**your-endpoint**',\n"
"... )"

#: ../../source/df-quickstart.rst:26
msgid "创建一个DataFrame对象十分容易，只需传入Table对象即可。"
msgstr ""
"You only need to input a Table object to create a DataFrame object. For "
"example:"

#: ../../source/df-quickstart.rst:28
msgid ""
">>> from odps.df import DataFrame\n"
">>> users = DataFrame(o.get_table('pyodps_ml_100k_users'))"
msgstr ""

#: ../../source/df-quickstart.rst:33
msgid "我们可以通过dtypes属性来查看这个DataFrame有哪些字段，分别是什么类型"
msgstr ""
"View fields of DataFrame and the types of the fields through the dtypes "
"attribute, as shown in the following code: "

#: ../../source/df-quickstart.rst:35
msgid ""
">>> users.dtypes\n"
"odps.Schema {\n"
"  user_id             int64\n"
"  age                 int64\n"
"  sex                 string\n"
"  occupation          string\n"
"  zip_code            string\n"
"}"
msgstr ""

#: ../../source/df-quickstart.rst:47
msgid "通过head方法，我们能取前N条数据，这让我们能快速预览数据。"
msgstr ""
"You can use the head method to obtain the first N data records for easy "
"and quick data preview. For example:"

#: ../../source/df-quickstart.rst:49
msgid ""
">>> users.head(10)\n"
"   user_id  age  sex     occupation  zip_code\n"
"0        1   24    M     technician     85711\n"
"1        2   53    F          other     94043\n"
"2        3   23    M         writer     32067\n"
"3        4   24    M     technician     43537\n"
"4        5   33    F          other     15213\n"
"5        6   42    M      executive     98101\n"
"6        7   57    M  administrator     91344\n"
"7        8   36    M  administrator     05201\n"
"8        9   29    M        student     01002\n"
"9       10   53    M         lawyer     90703"
msgstr ""

#: ../../source/df-quickstart.rst:64
msgid "有时候，我们并不需要都看到所有字段，我们可以从中筛选出一部分。"
msgstr ""
"You can add a filter on the fields if you do not want to view all of "
"them. For example:"

#: ../../source/df-quickstart.rst:66
msgid ""
">>> users[['user_id', 'age']].head(5)\n"
"   user_id  age\n"
"0        1   24\n"
"1        2   53\n"
"2        3   23\n"
"3        4   24\n"
"4        5   33"
msgstr ""

#: ../../source/df-quickstart.rst:76
msgid "有时候我们只是排除个别字段。"
msgstr "You can also exclude several fields. For example:"

#: ../../source/df-quickstart.rst:78
msgid ""
">>> users.exclude('zip_code', 'age').head(5)\n"
"   user_id  sex  occupation\n"
"0        1    M  technician\n"
"1        2    F       other\n"
"2        3    M      writer\n"
"3        4    M  technician\n"
"4        5    F       other"
msgstr ""

#: ../../source/df-quickstart.rst:88
msgid ""
"又或者，排除掉一些字段的同时，得通过计算得到一些新的列，比如我想将sex为M"
"的置为True，否则为False，并取名叫sex\\_bool。"
msgstr ""
"When excluding some fields, you may want to obtain new columns through "
"computation. For example, add the sex\\_bool attribute and set it to True"
" if sex is Male. Otherwise, set it to False. For example:"

#: ../../source/df-quickstart.rst:90
msgid ""
">>> users.select(users.exclude('zip_code', 'sex'), sex_bool=users.sex == "
"'M').head(5)\n"
"   user_id  age  occupation  sex_bool\n"
"0        1   24  technician      True\n"
"1        2   53       other     False\n"
"2        3   23      writer      True\n"
"3        4   24  technician      True\n"
"4        5   33       other     False"
msgstr ""

#: ../../source/df-quickstart.rst:100
msgid "现在，让我们看看年龄在20到25岁之间的人有多少个"
msgstr ""
"Obtain the number of persons at age of 20 to 25, as shown in the "
"following code:"

#: ../../source/df-quickstart.rst:102
msgid ""
">>> users[users.age.between(20, 25)].count()\n"
"195"
msgstr ""

#: ../../source/df-quickstart.rst:107
msgid "接下来，我们看看男女用户分别有多少。"
msgstr ""
"Obtain the numbers of male and female users, as shown in the following "
"code:"

#: ../../source/df-quickstart.rst:109
msgid ""
">>> users.groupby(users.sex).agg(count=users.count())\n"
"   sex  count\n"
"0    F    273\n"
"1    M    670"
msgstr ""

#: ../../source/df-quickstart.rst:116
msgid "用户按职业划分，从高到底，人数最多的前10职业是哪些呢？"
msgstr ""
"To divide users by job, obtain the first 10 jobs that have the largest "
"population, and sort the jobs in the descending order of population. See "
"the following:"

#: ../../source/df-quickstart.rst:118
msgid ""
">>> df = "
"users.groupby('occupation').agg(count=users['occupation'].count())\n"
">>> df.sort(df['count'], ascending=False)[:10]\n"
"      occupation  count\n"
"0        student    196\n"
"1          other    105\n"
"2       educator     95\n"
"3  administrator     79\n"
"4       engineer     67\n"
"5     programmer     66\n"
"6      librarian     51\n"
"7         writer     45\n"
"8      executive     32\n"
"9      scientist     31"
msgstr ""

#: ../../source/df-quickstart.rst:134
msgid ""
"DataFrame API提供了value\\_counts这个方法来快速达到同样的目的。注意该方法"
"返回的行数受到 ``options.df.odps.sort.limit`` 的限制，详见 :ref:`配置选项"
" <options>` 。"
msgstr ""
"DataFrame APIs provide the value\\_counts method to quickly achieve the "
"same result. An example is shown below. Note that the number of records "
"returned by this method is limited by ``options.df.odps.sort.limit``, "
"whose default value is 10,000. More information can be found in "
":ref:`configuration section <options>`."

#: ../../source/df-quickstart.rst:137
msgid ""
">>> uses.occupation.value_counts()[:10]\n"
"      occupation  count\n"
"0        student    196\n"
"1          other    105\n"
"2       educator     95\n"
"3  administrator     79\n"
"4       engineer     67\n"
"5     programmer     66\n"
"6      librarian     51\n"
"7         writer     45\n"
"8      executive     32\n"
"9      scientist     31"
msgstr ""

#: ../../source/df-quickstart.rst:152
msgid "让我们用更直观的图来看这份数据。"
msgstr "Show data in a more intuitive graph, as shown in the following code:"

#: ../../source/df-quickstart.rst:154
msgid ">>> %matplotlib inline"
msgstr ""

#: ../../source/df-quickstart.rst:158
msgid "我们可以用个横向的柱状图来可视化"
msgstr ""
"Use a horizontal bar chart to visualize data, as shown in the following "
"code:"

#: ../../source/df-quickstart.rst:160
msgid ""
">>> users['occupation'].value_counts().plot(kind='barh', x='occupation', "
"ylabel='prefession')\n"
"<matplotlib.axes._subplots.AxesSubplot at 0x10653cfd0>"
msgstr ""

#: ../../source/df-quickstart.rst:167
msgid ".. image:: _static/df-value-count-plot.png"
msgstr ""

#: ../../source/df-quickstart.rst:168
msgid "我们将年龄分成30组，来看个年龄分布的直方图"
msgstr ""
"Divide ages into 30 groups and view the histogram of age distribution, as"
" shown in the following code:"

#: ../../source/df-quickstart.rst:170
msgid ""
">>> users.age.hist(bins=30, title=\"Distribution of users' ages\", "
"xlabel='age', ylabel='count of users')\n"
"<matplotlib.axes._subplots.AxesSubplot at 0x10667a510>"
msgstr ""

#: ../../source/df-quickstart.rst:177
msgid ".. image:: _static/df-age-hist.png"
msgstr ""

#: ../../source/df-quickstart.rst:178
msgid ""
"好了，现在我们把这三张表联合起来，只需要使用join就可以了。join完成后我们"
"把它保存成一张新的表。"
msgstr ""
"Use join to join the three tables and save the joined tables as a new "
"table. For example:"

#: ../../source/df-quickstart.rst:180
msgid ""
">>> movies = DataFrame(o.get_table('pyodps_ml_100k_movies'))\n"
">>> ratings = DataFrame(o.get_table('pyodps_ml_100k_ratings'))\n"
">>>\n"
">>> o.delete_table('pyodps_ml_100k_lens', if_exists=True)\n"
">>> lens = "
"movies.join(ratings).join(users).persist('pyodps_ml_100k_lens')\n"
">>>\n"
">>> lens.dtypes\n"
"odps.Schema {\n"
"  movie_id                            int64\n"
"  title                               string\n"
"  release_date                        string\n"
"  video_release_date                  string\n"
"  imdb_url                            string\n"
"  user_id                             int64\n"
"  rating                              int64\n"
"  unix_timestamp                      int64\n"
"  age                                 int64\n"
"  sex                                 string\n"
"  occupation                          string\n"
"  zip_code                            string\n"
"}"
msgstr ""

#: ../../source/df-quickstart.rst:204
msgid "现在我们把年龄分成从0到80岁，分成8个年龄段，"
msgstr "Divide ages of 0 to 80 into eight groups, as shown in the following code:"

#: ../../source/df-quickstart.rst:206
msgid ""
">>> labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-59', "
"'60-69', '70-79']\n"
">>> cut_lens = lens[lens, lens.age.cut(range(0, 81, 10), right=False, "
"labels=labels).rename('年龄分组')]"
msgstr ""
">>> labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-59', "
"'60-69', '70-79']\n"
">>> cut_lens = lens[lens, lens.age.cut(range(0, 81, 10), right=False, "
"labels=labels).rename('age_group')]"

#: ../../source/df-quickstart.rst:211
msgid "我们取分组和年龄唯一的前10条看看。"
msgstr ""
"View the first 10 data records of a single age in a group, as shown in "
"the following code:"

#: ../../source/df-quickstart.rst:213
msgid ""
">>> cut_lens['年龄分组', 'age'].distinct()[:10]\n"
"   年龄分组  age\n"
"0       0-9    7\n"
"1     10-19   10\n"
"2     10-19   11\n"
"3     10-19   13\n"
"4     10-19   14\n"
"5     10-19   15\n"
"6     10-19   16\n"
"7     10-19   17\n"
"8     10-19   18\n"
"9     10-19   19"
msgstr ""
">>> cut_lens['age_group', 'age'].distinct()[:10]\n"
"   age_group  age\n"
"0        0-9    7\n"
"1      10-19   10\n"
"2      10-19   11\n"
"3      10-19   13\n"
"4      10-19   14\n"
"5      10-19   15\n"
"6      10-19   16\n"
"7      10-19   17\n"
"8      10-19   18\n"
"9      10-19   19"

#: ../../source/df-quickstart.rst:228
msgid "最后，我们来看看在各个年龄分组下，用户的评分总数和评分均值分别是多少。"
msgstr ""
"View users’ total rating and average rating of each age group, as shown "
"in the following code:"

#: ../../source/df-quickstart.rst:230
msgid ""
">>> cut_lens.groupby('年龄分组').agg(cut_lens.rating.count().rename('评分"
"总数'), cut_lens.rating.mean().rename('评分均值'))\n"
"     年龄分组  评分均值  评分总数\n"
"0       0-9  3.767442        43\n"
"1     10-19  3.486126      8181\n"
"2     20-29  3.467333     39535\n"
"3     30-39  3.554444     25696\n"
"4     40-49  3.591772     15021\n"
"5     50-59  3.635800      8704\n"
"6     60-69  3.648875      2623\n"
"7     70-79  3.649746       197"
msgstr ""
">>> "
"cut_lens.groupby('age_group').agg(cut_lens.rating.count().rename('total_rating'),"
" cut_lens.rating.mean().rename('avg_rating'))\n"
"     age_group  avg_rating  total_rating\n"
"0          0-9    3.767442            43\n"
"1        10-19    3.486126          8181\n"
"2        20-29    3.467333         39535\n"
"3        30-39    3.554444         25696\n"
"4        40-49    3.591772         15021\n"
"5        50-59    3.635800          8704\n"
"6        60-69    3.648875          2623\n"
"7        70-79    3.649746           197"

