# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-05-17 10:30+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-debug-instruction.rst:5
msgid "调试指南"
msgstr "Debugging instructions"

#: ../../source/df-debug-instruction.rst:9
msgid "可视化DataFrame"
msgstr "Visualized DataFrame"

#: ../../source/df-debug-instruction.rst:11
msgid ""
"由于PyODPS DataFrame本身会对整个操作执行优化，为了能直观地反应整个过程， "
"我们可以使用可视化的方式显示整个表达式的计算过程。"
msgstr ""
"Python on MaxCompute (PyODPS) DataFrame can optimize and display the "
"entire operation execution. You can use this to visualize the entire "
"computation process of the operation execution. "

#: ../../source/df-debug-instruction.rst:14
msgid ""
"值得注意的是，可视化需要依赖 `graphviz 软件 <http://www.graphviz.org/"
"Download..php>`_ 和 **graphviz** Python 包。"
msgstr ""
"Note that this visualization depends on `graphviz "
"<http://www.graphviz.org/Download..php>`_ and the **graphviz** Python "
"package."

#: ../../source/df-debug-instruction.rst:22
msgid ""
">>> df = iris.groupby('name').agg(id=iris.sepalwidth.sum())\n"
">>> df = df[df.name, df.id + 3]\n"
">>> df.visualize()"
msgstr ""

#: ../../source/df-debug-instruction.rst:24
msgid ".. image:: _static/df-steps-visualize.svg"
msgstr ""

#: ../../source/df-debug-instruction.rst:25
msgid "可以看到，这个计算过程中，PyODPS DataFrame将GroupBy和列筛选做了操作合并。"
msgstr ""
"PyODPS DataFrame combines the GroupBy operation and column filtering, as "
"seen in the computation process."

#: ../../source/df-debug-instruction.rst:32
msgid ""
">>> df = iris.groupby('name').agg(id=iris.sepalwidth.sum()).cache()\n"
">>> df2 = df[df.name, df.id + 3]\n"
">>> df2.visualize()"
msgstr ""

#: ../../source/df-debug-instruction.rst:35
msgid ".. image:: _static/df-op-merge-visualize.svg"
msgstr ""

#: ../../source/df-debug-instruction.rst:36
msgid "此时，由于用户执行了cache操作，这时整个执行计划将会分成两步来执行。"
msgstr "Due to the executed cache operation, the entire process runs in two steps."

#: ../../source/df-debug-instruction.rst:39
msgid "ODPS SQL后端查看编译结果"
msgstr "View compiling results at the MaxCompute SQL backend"

#: ../../source/df-debug-instruction.rst:41
msgid "我们可以直接调用 ``compile`` 方法来查看ODPS SQL后端编译到SQL的结果。"
msgstr ""
"Use the ``compile`` method to view SQL compiling results at the "
"MaxCompute SQL backend."

#: ../../source/df-debug-instruction.rst:58
msgid ""
">>> df = iris.groupby('name').agg(sepalwidth=iris.sepalwidth.max())\n"
">>> df.compile()\n"
"Stage 1:\n"
"\n"
"SQL compiled:\n"
"\n"
"SELECT\n"
"  t1.`name`,\n"
"  MAX(t1.`sepalwidth`) AS `sepalwidth`\n"
"FROM test_pyodps_dev.`pyodps_iris` t1\n"
"GROUP BY\n"
"  t1.`name`"
msgstr ""

#: ../../source/df-debug-instruction.rst:60
msgid "使用pandas计算后端执行本地调试"
msgstr "Execute local debugging with the pandas computation backend"

#: ../../source/df-debug-instruction.rst:62
msgid ""
"对于来自ODPS表的DataFrame，一些操作不会compile到ODPS SQL执行，而是会使用"
"Tunnel下载， 这个过程是很快的，且无需等待ODPS SQL任务的调度。 利用这个"
"特性，我们能快速下载小部分ODPS数据到本地，使用pandas计算后端来进行代码"
"编写和调试。"
msgstr ""
"The DataFrame application program interfaces (APIs) that are created from"
" the MaxCompute table do not compile some operations to MaxCompute SQL "
"for execution. DataFrame instead uses the Tunnel API to download data "
"quickly, without the need to wait for MaxCompute SQL task scheduling. "
"Using this feature, you can quickly download small amounts of MaxCompute "
"data to a local directory, and use the pandas computation backend to "
"compile and debug code."

#: ../../source/df-debug-instruction.rst:66
msgid "这些操作包括："
msgstr "Follow these operations:"

#: ../../source/df-debug-instruction.rst:68
msgid ""
"对非分区表进行选取整个或者有限条数据、或者列筛选的操作（不包括列的各种"
"计算），以及计算其数量"
msgstr ""
"Select all or some items of data from a non-partitioned table, or filter "
"column data excluding column computation, and then calculate the number "
"of specific data items."

#: ../../source/df-debug-instruction.rst:69
msgid ""
"对分区表不选取分区或筛选前几个分区字段，对其进行选取全部或者有限条数据、"
"或者列筛选的操作，以及计算其数量"
msgstr ""
"Select all or some items of data from all or the first several partition "
"columns that you have specified in a partitioned table, or filter the "
"column data, and then calculate the number of data items."

#: ../../source/df-debug-instruction.rst:71
msgid ""
"如我们的iris这个DataFrame的来源ODPS表是非分区表，以下操作会使用tunnel进行"
"下载。"
msgstr ""
"If the iris object of DataFrame uses non-partitioned MaxCompute table as "
"the source, the following operation uses the Tunnel API to download data:"

#: ../../source/df-debug-instruction.rst:77
msgid ""
">>> iris.count()\n"
">>> iris['name', 'sepalwidth'][:10]"
msgstr ""

#: ../../source/df-debug-instruction.rst:78
msgid ""
"对于分区表，如有个DataFrame来源于分区表（有三个分区字段，ds、hh、mm），"
"以下操作会使用tunnel下载。"
msgstr ""
"If DataFrame uses a partitioned table that includes three fields ds, hh, "
"and mm, the following operation uses Tunnel commands to download data:"

#: ../../source/df-debug-instruction.rst:86
msgid ""
">>> df[:10]\n"
">>> df[df.ds == '20160808']['f0', 'f1']\n"
">>> df[(df.ds == '20160808') & (df.hh == 3)][:10]\n"
">>> df[(df.ds == '20160808') & (df.hh == 3) & (df.mm == 15)]"
msgstr ""

#: ../../source/df-debug-instruction.rst:87
msgid ""
"因此我们可以使用 ``to_pandas`` 方法来将部分数据下载到本地来进行调试，我们"
"可以写出如下代码："
msgstr ""
"You can use the ``to_pandas`` method to download some data to a local "
"directory for debugging. You can write the following code:"

#: ../../source/df-debug-instruction.rst:92
msgid ">>> DEBUG = True"
msgstr ""

#: ../../source/df-debug-instruction.rst:99
msgid ""
">>> if DEBUG:\n"
">>>     df = iris[:100].to_pandas(wrap=True)\n"
">>> else:\n"
">>>     df = iris"
msgstr ""

#: ../../source/df-debug-instruction.rst:100
msgid ""
"这样，当我们全部编写完成时，再把 ``DEBUG`` 设置为False就可以在ODPS上执行"
"完整的计算了。"
msgstr ""
"At the end of compiling, set ``DEBUG`` to False to execute complete "
"computation on MaxCompute."

#: ../../source/df-debug-instruction.rst:104
msgid "**由于沙箱的限制，本地调试通过的程序不一定能在ODPS上也跑通。**"
msgstr ""
"**Restricted by the sandbox, some programs that pass local debugging may "
"fail to run on MaxCompute.**"

