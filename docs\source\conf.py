# -*- coding: utf-8 -*-
#
# PyOdps documentation build configuration file, created by
# sphinx-quickstart on Wed Nov 18 09:47:14 2015.
#
# This file is execfile()d with the current directory set to its
# containing dir.
#
# Note that not all possible configuration values are present in this
# autogenerated file.
#
# All configuration values have a default; values that are commented out
# serve to show the default.

import atexit
import codecs
import os
import re
import shutil
import sys
import tempfile
import textwrap

try:
    from sphinx.directives import Include
except ImportError:
    from sphinx.directives.other import Include


dirname = os.path.dirname
docroot = os.path.dirname(os.path.abspath(__file__))

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
sys.path.insert(0, dirname(dirname(dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.abspath('../sphinx-ext/'))

# on_rtd is whether we are on readthedocs.org, this line of code grabbed from docs.readthedocs.org
on_rtd = os.environ.get('READTHEDOCS', None) == 'True'

# -- General configuration ------------------------------------------------

# If your documentation needs a minimal Sphinx version, state it here.
#needs_sphinx = '1.0'

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.todo',
    'sphinx.ext.ifconfig',
    'sphinx.ext.viewcode',
    'sphinx.ext.autosummary',
    'sphinx.ext.napoleon',
    'sphinx.ext.mathjax',
    'sphinxcontrib.jquery',
]

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# The suffix(es) of source filenames.
# You can specify multiple suffix as a list of string:
# source_suffix = ['.rst', '.md']
source_suffix = '.rst'

# The encoding of source files.
#source_encoding = 'utf-8-sig'

# The master toctree document.
master_doc = 'index'

# General information about the project.
project = u'PyODPS'
copyright = u'2014-2018, The Alibaba Group Holding Ltd.'
author = u'Qin Xuye'

# The version info for the project you're documenting, acts as replacement for
# |version| and |release|, also used in various other places throughout the
# built documents.
#
# The short X.Y version.
from odps import __version__

version = __version__
# The full version, including alpha/beta/rc tags.
release = __version__

# The language for content autogenerated by Sphinx. Refer to documentation
# for a list of supported languages.
#
# This is also used if you do content translation via gettext catalogs.
# Usually you set "language" from the command line for these cases.
language = None

# There are two options for replacing |today|: either, you set today to some
# non-false value, then it is used:
#today = ''
# Else, today_fmt is used as the format for a strftime call.
#today_fmt = '%B %d, %Y'

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
if 'gettext' not in sys.argv:
    try:
        import odps.internal
        with_internal = True
        exclude_patterns = ['*-ext.rst', '*-ext-*.rst']
    except ImportError:
        with_internal = False
        exclude_patterns = ['*-int.rst', '*-int-*.rst']
else:
    with_internal = None

# The reST default role (used for this markup: `text`) to use for all
# documents.
#default_role = None

# If true, '()' will be appended to :func: etc. cross-reference text.
#add_function_parentheses = True

# If true, the current module name will be prepended to all description
# unit titles (such as .. function::).
#add_module_names = True

# If true, sectionauthor and moduleauthor directives will be shown in the
# output. They are ignored by default.
#show_authors = False

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = 'sphinx'

# A list of ignored prefixes for module index sorting.
#modindex_common_prefix = []

# If true, keep warnings as "system message" paragraphs in the built documents.
#keep_warnings = False

# If true, `todo` and `todoList` produce output, else they produce nothing.
todo_include_todos = True

# -- Options for HTML output ----------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
html_theme = 'sphinx_rtd_theme'

# Theme options are theme-specific and customize the look and feel of a theme
# further.  For a list of options available for each theme, see the
# documentation.
#html_theme_options = {}

# Add any paths that contain custom themes here, relative to this directory.
#html_theme_path = []

# The name for this set of Sphinx documents.  If None, it defaults to
# "<project> v<release> documentation".
#html_title = None

# A shorter title for the navigation bar.  Default is the same as html_title.
#html_short_title = None

# The name of an image file (relative to this directory) to place at the top
# of the sidebar.
html_logo = '_static/PyODPS.png'

# The name of an image file (within the static path) to use as favicon of the
# docs.  This file should be a Windows icon file (.ico) being 16x16 or 32x32
# pixels large.
#html_favicon = None

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = ['_static']

# Add any extra paths that contain custom files (such as robots.txt or
# .htaccess) here, relative to this directory. These files are copied
# directly to the root of the documentation.
#html_extra_path = []

# If not '', a 'Last updated on:' timestamp is inserted at every page bottom,
# using the given strftime format.
#html_last_updated_fmt = '%b %d, %Y'

# If true, SmartyPants will be used to convert quotes and dashes to
# typographically correct entities.
#html_use_smartypants = True

# Custom sidebar templates, maps document names to template names.
#html_sidebars = {}

# Additional templates that should be rendered to pages, maps page names to
# template names.
#html_additional_pages = {}

# If false, no module index is generated.
#html_domain_indices = True

# If false, no index is generated.
#html_use_index = True

# If true, the index is split into individual pages for each letter.
#html_split_index = False

# If true, links to the reST sources are added to the pages.
#html_show_sourcelink = True

# If true, "Created using Sphinx" is shown in the HTML footer. Default is True.
#html_show_sphinx = True

# If true, "(C) Copyright ..." is shown in the HTML footer. Default is True.
#html_show_copyright = True

# If true, an OpenSearch description file will be output, and all pages will
# contain a <link> tag referring to it.  The value of this option must be the
# base URL from which the finished HTML is served.
#html_use_opensearch = ''

# This is the file name suffix for HTML files (e.g. ".xhtml").
#html_file_suffix = None

# Language to be used for generating the HTML full-text search index.
# Sphinx supports the following languages:
#   'da', 'de', 'en', 'es', 'fi', 'fr', 'hu', 'it', 'ja'
#   'nl', 'no', 'pt', 'ro', 'ru', 'sv', 'tr'
#html_search_language = 'en'

# A dictionary with options for the search language support, empty by default.
# Now only 'ja' uses this config value
#html_search_options = {'type': 'default'}

# The name of a javascript file (relative to the configuration directory) that
# implements a search results scorer. If empty, the default will be used.
#html_search_scorer = 'scorer.js'

# Output file base name for HTML help builder.
htmlhelp_basename = 'PyOdpsdoc'

# -- Options for LaTeX output ---------------------------------------------

latex_engine = 'xelatex'

latex_elements = {
    'preamble': textwrap.dedent(r"""
    \usepackage{svg}
    \usepackage{hyperref}
    \setcounter{tocdepth}{2}
    """)
}

if on_rtd:
    del latex_engine
    latex_elements['preamble'] = textwrap.dedent(r"""
    \hypersetup{unicode=true}
    \usepackage{CJKutf8}
    \usepackage{svg}
    \usepackage{hyperref}
    \setcounter{tocdepth}{2}
    \DeclareUnicodeCharacter{00A0}{\nobreakspace}
    \DeclareUnicodeCharacter{2203}{\ensuremath{\exists}}
    \DeclareUnicodeCharacter{2200}{\ensuremath{\forall}}
    \DeclareUnicodeCharacter{2286}{\ensuremath{\subseteq}}
    \DeclareUnicodeCharacter{2713}{x}
    \DeclareUnicodeCharacter{27FA}{\ensuremath{\Longleftrightarrow}}
    \DeclareUnicodeCharacter{221A}{\ensuremath{\sqrt{}}}
    \DeclareUnicodeCharacter{221B}{\ensuremath{\sqrt[3]{}}}
    \DeclareUnicodeCharacter{2295}{\ensuremath{\oplus}}
    \DeclareUnicodeCharacter{2297}{\ensuremath{\otimes}}
    \begin{CJK}{UTF8}{gbsn}
    \AtEndDocument{\end{CJK}}
    """)

# Grouping the document tree into LaTeX files. List of tuples
# (source start file, target name, title,
#  author, documentclass [howto, manual, or own class]).
latex_documents = [
  (master_doc, 'PyOdps.tex', u'PyODPS Documentation',
   u'The Alibaba Group Holding Ltd.', 'manual'),
]

# The name of an image file (relative to this directory) to place at the top of
# the title page.
#latex_logo = None

# For "manual" documents, if this is true, then toplevel headings are parts,
# not chapters.
#latex_use_parts = False

# If true, show page references after internal links.
#latex_show_pagerefs = False

# If true, show URL addresses after external links.
#latex_show_urls = False

# Documents to append as an appendix to all manuals.
#latex_appendices = []

# If false, no module index is generated.
#latex_domain_indices = True


# -- Options for manual page output ---------------------------------------

# One entry per manual page. List of tuples
# (source start file, name, description, authors, manual section).
man_pages = [
    (master_doc, 'pyodps', u'PyODPS Documentation',
     [author], 1)
]

# If true, show URL addresses after external links.
#man_show_urls = False


# -- Options for Texinfo output -------------------------------------------

# Grouping the document tree into Texinfo files. List of tuples
# (source start file, target name, title, author,
#  dir menu entry, description, category)
texinfo_documents = [
  (master_doc, 'PyODPS', u'PyODPS Documentation',
   author, 'PyODPS', 'One line description of project.',
   'Miscellaneous'),
]

# Documents to append as an appendix to all manuals.
#texinfo_appendices = []

# If false, no module index is generated.
#texinfo_domain_indices = True

# How to display URL addresses: 'footnote', 'no', or 'inline'.
#texinfo_show_urls = 'footnote'

# If true, do not generate a @detailmenu in the "Top" node's menu.
#texinfo_no_detailmenu = False

# napoleon_google_docstring = False

gettext_additional_targets = ['literal-block', 'image', 'raw']

# Example configuration for intersphinx: refer to the Python standard library.
intersphinx_mapping = {'https://docs.python.org/': None}

mathjax_path = "https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML"

# -- Extension configuration -------------------------------------------------

if any('sphinx-intl' in a for a in sys.argv) or 'gettext' in sys.argv or on_rtd:
    locale_dirs = ['locale/']
    locale_dir = os.path.join(docroot, 'locale')
else:
    temp_lc_dir = tempfile.mkdtemp(prefix='pyodps-po-')
    locale_dir = os.path.join(temp_lc_dir, 'locale')
    shutil.copytree('locale', locale_dir)
    locale_dirs = [locale_dir]
    atexit.register(shutil.rmtree, temp_lc_dir)
gettext_compact = False


class IncludeInternal(Include):
    def run(self):
        if with_internal is None:
            return []
        if with_internal:
            return Include.run(self)
        else:
            return []


class IncludeExternal(Include):
    def run(self):
        if with_internal is None:
            return []
        if with_internal:
            return []
        else:
            return Include.run(self)


def merge_po(dest_file, *src_files):
    from collections import OrderedDict
    pairs = OrderedDict()
    for src_file in src_files:
        lines = []
        last_msg_id = None
        for line in codecs.open(src_file, 'r', encoding='utf-8'):
            if line.startswith('#'):
                continue
            if not line.strip():
                continue
            if line.startswith('msgid'):
                if last_msg_id:
                    msg_txt = '\n'.join(lines)
                    pairs[last_msg_id] = msg_txt
                    last_msg_id = None
                lines = [line.strip('\n')]
            elif line.startswith('msgstr'):
                if lines:
                    last_msg_id = '\n'.join(lines)
                lines = [line.strip('\n')]
            else:
                lines.append(line.strip('\n'))

        if last_msg_id:
            msg_txt = '\n'.join(lines)
            pairs[last_msg_id] = msg_txt

    with codecs.open(dest_file, 'w', encoding='utf-8') as outf:
        for k, v in pairs.items():
            outf.write(k)
            outf.write('\n')
            outf.write(v)
            outf.write('\n\n')


# config for internal label
def setup(app):
    if with_internal:
        tags.add('internal')
    app.add_css_file('theme_override.css')
    app.add_js_file('theme_override.js')
    if not on_rtd:
        app.add_css_file('theme_override-nrtd.css')
        app.add_js_file('theme_override-nrtd.js')
        if os.path.exists(os.path.join(docroot, '_static', 'theme_override-nrtd-int.css')):
            app.add_css_file('theme_override-nrtd-int.css')
        if os.path.exists(os.path.join(docroot, '_static', 'theme_override-nrtd-int.js')):
            app.add_js_file('theme_override-nrtd-int.js')

    app.add_directive('intinclude', IncludeInternal)
    app.add_directive('extinclude', IncludeExternal)

    if with_internal is None:
        return

    lang = app.config.language
    if lang is None:
        return

    if with_internal:
        inc_pattern = re.compile(r'\n *\.\. *intinclude:: *([^\s]+)')
    else:
        inc_pattern = re.compile(r'\n *\.\. *extinclude:: *([^\s]+)')

    conf_root = os.path.dirname(os.path.abspath(__file__))
    for root, _, files in os.walk(conf_root):
        for f in files:
            if not f.lower().endswith('.rst'):
                continue

            dest_po_file, _ = os.path.splitext(f)
            dest_po_file = os.path.join(locale_dir, lang, 'LC_MESSAGES', dest_po_file + '.po')
            with codecs.open(os.path.join(root, f), 'r') as inf:
                content = inf.read()
            src_po_files = []
            for match in inc_pattern.finditer(content):
                src_po_file, _ = os.path.splitext(match.group(1))
                src_po_file = os.path.join(locale_dir, lang, 'LC_MESSAGES', src_po_file + '.po')
                if os.path.exists(src_po_file):
                    src_po_files.append(src_po_file)

            if src_po_files:
                if os.path.exists(dest_po_file):
                    src_po_files = [dest_po_file] + src_po_files
                print('Merge %s <- %s' % (dest_po_file, ','.join(src_po_files)))
                merge_po(dest_po_file, *src_po_files)
