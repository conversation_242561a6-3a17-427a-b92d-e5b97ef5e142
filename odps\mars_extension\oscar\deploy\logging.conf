[loggers]
keys=root,main,tornado,mars_deploy,mars_oscar,mars_dataframe,mars_learn,mars_tensor,mars_services,odps_mars

[handlers]
keys=stream_handler,null_handler

[formatters]
keys=formatter

[logger_root]
level=WARN
handlers=stream_handler

[logger_main]
level=DEBUG
handlers=stream_handler
qualname=__main__
propagate=0

[logger_tornado]
level=WARN
handlers=stream_handler
qualname=tornado
propagate=0

[logger_mars_deploy]
level=DEBUG
handlers=stream_handler
qualname=mars.deploy
propagate=0

[logger_mars_oscar]
level=DEBUG
handlers=stream_handler
qualname=mars.oscar
propagate=0

[logger_mars_dataframe]
level=DEBUG
handlers=stream_handler
qualname=mars.dataframe
propagate=0

[logger_mars_learn]
level=DEBUG
handlers=stream_handler
qualname=mars.learn
propagate=0

[logger_mars_tensor]
level=DEBUG
handlers=stream_handler
qualname=mars.tensor
propagate=0

[logger_mars_services]
level=DEBUG
handlers=stream_handler
qualname=mars.services
propagate=0

[logger_odps_mars]
level=DEBUG
handlers=stream_handler
qualname=odps.mars_extension
propagate=0

[handler_null_handler]
class=NullHandler
args=()

[handler_stream_handler]
class=StreamHandler
formatter=formatter
args=(sys.stderr,)

[formatter_formatter]
format=%(asctime)s %(name)-12s %(process)d %(levelname)-8s %(message)s
