# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: kv_store_service.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import service as _service
from google.protobuf import service_reflection
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='kv_store_service.proto',
  package='apsara.odps.cupid.protocol',
  syntax='proto2',
  serialized_pb=_b('\n\x16kv_store_service.proto\x12\x1a\x61psara.odps.cupid.protocol\"(\n\nPutRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c\"\r\n\x0bPutResponse\"\x1b\n\nGetRequest\x12\r\n\x05value\x18\x01 \x01(\t\"\x1c\n\x0bGetResponse\x12\r\n\x05value\x18\x01 \x01(\x0c\x32\xc0\x01\n\x0eKVStoreService\x12V\n\x03Put\x12&.apsara.odps.cupid.protocol.PutRequest\x1a\'.apsara.odps.cupid.protocol.PutResponse\x12V\n\x03Get\x12&.apsara.odps.cupid.protocol.GetRequest\x1a\'.apsara.odps.cupid.protocol.GetResponseB\t\x80\x01\x01\x88\x01\x01\x90\x01\x01')
)




_PUTREQUEST = _descriptor.Descriptor(
  name='PutRequest',
  full_name='apsara.odps.cupid.protocol.PutRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.PutRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.PutRequest.value', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=54,
  serialized_end=94,
)


_PUTRESPONSE = _descriptor.Descriptor(
  name='PutResponse',
  full_name='apsara.odps.cupid.protocol.PutResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=96,
  serialized_end=109,
)


_GETREQUEST = _descriptor.Descriptor(
  name='GetRequest',
  full_name='apsara.odps.cupid.protocol.GetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.GetRequest.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=111,
  serialized_end=138,
)


_GETRESPONSE = _descriptor.Descriptor(
  name='GetResponse',
  full_name='apsara.odps.cupid.protocol.GetResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.GetResponse.value', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=140,
  serialized_end=168,
)

DESCRIPTOR.message_types_by_name['PutRequest'] = _PUTREQUEST
DESCRIPTOR.message_types_by_name['PutResponse'] = _PUTRESPONSE
DESCRIPTOR.message_types_by_name['GetRequest'] = _GETREQUEST
DESCRIPTOR.message_types_by_name['GetResponse'] = _GETRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PutRequest = _reflection.GeneratedProtocolMessageType('PutRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTREQUEST,
  __module__ = 'kv_store_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.PutRequest)
  ))
_sym_db.RegisterMessage(PutRequest)

PutResponse = _reflection.GeneratedProtocolMessageType('PutResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTRESPONSE,
  __module__ = 'kv_store_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.PutResponse)
  ))
_sym_db.RegisterMessage(PutResponse)

GetRequest = _reflection.GeneratedProtocolMessageType('GetRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETREQUEST,
  __module__ = 'kv_store_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetRequest)
  ))
_sym_db.RegisterMessage(GetRequest)

GetResponse = _reflection.GeneratedProtocolMessageType('GetResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRESPONSE,
  __module__ = 'kv_store_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetResponse)
  ))
_sym_db.RegisterMessage(GetResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\200\001\001\210\001\001\220\001\001'))

_KVSTORESERVICE = _descriptor.ServiceDescriptor(
  name='KVStoreService',
  full_name='apsara.odps.cupid.protocol.KVStoreService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=171,
  serialized_end=363,
  methods=[
  _descriptor.MethodDescriptor(
    name='Put',
    full_name='apsara.odps.cupid.protocol.KVStoreService.Put',
    index=0,
    containing_service=None,
    input_type=_PUTREQUEST,
    output_type=_PUTRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Get',
    full_name='apsara.odps.cupid.protocol.KVStoreService.Get',
    index=1,
    containing_service=None,
    input_type=_GETREQUEST,
    output_type=_GETRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_KVSTORESERVICE)

DESCRIPTOR.services_by_name['KVStoreService'] = _KVSTORESERVICE

KVStoreService = service_reflection.GeneratedServiceType('KVStoreService', (_service.Service,), dict(
  DESCRIPTOR = _KVSTORESERVICE,
  __module__ = 'kv_store_service_pb2'
  ))

KVStoreService_Stub = service_reflection.GeneratedServiceStubType('KVStoreService_Stub', (KVStoreService,), dict(
  DESCRIPTOR = _KVSTORESERVICE,
  __module__ = 'kv_store_service_pb2'
  ))


# @@protoc_insertion_point(module_scope)
