.. _api_df:

DataFrame
=========

.. autoclass:: odps.df.DataFrame
    :members:

.. autoclass:: odps.df.CollectionExpr
    :members:
    :inherited-members:

.. autoclass:: odps.df.SequenceExpr
    :members:
    :inherited-members:

.. autoclass:: odps.df.expr.expressions.Int64SequenceExpr
    :members:

.. autoclass:: odps.df.expr.expressions.StringSequenceExpr
    :members:

.. autoclass:: odps.df.Scalar
    :members:
    :inherited-members:

.. autofunction:: odps.df.NullScalar

.. autoclass:: odps.df.RandomScalar
    :members:
    :inherited-members:

.. autoclass:: odps.df.expr.groupby.GroupBy
    :members:
    :inherited-members:

.. autoclass:: odps.df.expr.groupby.SequenceGroupBy
    :members:
    :inherited-members:

.. autoclass:: odps.df.expr.groupby.Int64SequenceGroupBy
    :members:
    :inherited-members:
