# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-19 13:54+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/faq-ext.rst:2
msgid "安装失败 / 出现问题"
msgstr "Installation failure/error"

#: ../../source/faq-ext.rst:3
msgid ""
"请参考 `PyODPS 安装常见问题解决 <https://yq.aliyun.com/articles/277333>`_"
" 。"
msgstr ""
"For more information, see `PyODPS installation FAQ (Chinese version only)"
" <https://yq.aliyun.com/articles/277333>`_ ."

#: ../../source/faq-ext.rst:6
msgid "提示 Project not found"
msgstr "Project not found error"

#: ../../source/faq-ext.rst:7
msgid ""
"Endpoint配置不对，详细配置参考 `MaxCompute 开通 Region 和服务连接对照表 <"
"https://help.aliyun.com/document_detail/34951.html#h2-maxcompute-region-3"
">`_ 。 此外还需要注意 ODPS 入口对象参数位置是否填写正确。"
msgstr ""
"This error is caused by an error in the configuration of Endpoint. For "
"more information, see `MaxCompute activation and service connections by "
"region <https://www.alibabacloud.com/help/en/doc-"
"detail/34951.htm#MaxCompute%20activation%20and%20service%20connections%20by%20region>`_"
" . Check to see if the ODPS object parameter position is correct."

#: ../../source/faq-ext.rst:-1
msgid "如何手动指定 Tunnel Endpoint"
msgstr "How to specify tunnel endpoint manually"

#: ../../source/faq-ext.rst:14
msgid ""
"可以使用下面的方法创建带有 Tunnel Endpoint 的 ODPS 入口（参数值请自行替换"
"，不包含星号）："
msgstr ""
"You can create your MaxCompute (ODPS) entrance object with an extra "
"```tunnel_endpoint``` parameter, as shown in the following code. "
"Asterisks should be removed."

#: ../../source/faq-ext.rst:16
msgid ""
"import os\n"
"from odps import ODPS\n"
"# 保证 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为用户 Access Key ID，\n"
"# ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为用户 Access Key Secret，\n"
"# 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
"    tunnel_endpoint='**your-tunnel-endpoint**',\n"
")"
msgstr ""
"import os\n"
"from odps import ODPS\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to Access Key ID of user\n"
"# while environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to "
"Access Key Secret of user.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
"    tunnel_endpoint='**your-tunnel-endpoint**',\n"
")"

