# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-08-06 11:10+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/platform-migrate-ext.rst:2
msgid "从平台到自行部署"
msgstr "Migration from a platform to local PyODPS"

#: ../../source/platform-migrate-ext.rst:3
msgid ""
"如果你需要在本地调试 PyODPS，或者平台中没有提供你所需的包，或者平台的资源"
"限制不能满足你的要求，此时你可能需要 从平台迁移到自己部署的 PyODPS 环境。"
msgstr ""
"If Python on MaxCompute (PyODPS) does not have the required packages or "
"resources, and you need to debug PyODPS locally, then you need to migrate"
" to a local PyODPS environment."

#: ../../source/platform-migrate-ext.rst:6
msgid ""
"安装 PyODPS 的步骤可以参考 :ref:`安装指南 <install>`。你需要手动创建先前"
"平台为你创建的 ODPS 入口对象。可以在先前的平台 使用下列语句生成创建 ODPS "
"入口对象所需要的语句模板，然后手动修改为可用的代码："
msgstr ""
"For more information about installing PyODPS, see :ref:`Installation "
"instruction <install>`. You need to use PyODPS to manually create the "
"ODPS object. Use the following statement on PyODPS to generate the "
"template that is required for creating the ODPS object, and then modify "
"the template using the following code:"

#: ../../source/platform-migrate-ext.rst:9
#, python-format
msgid ""
"print(\"\\nfrom odps import ODPS\\no = ODPS(%r, '<access-key>', %r, "
"'<endpoint>')\\n\" % (o.account.access_id, o.project))"
msgstr ""

#: ../../source/platform-migrate-ext.rst:13
msgid ""
"其中，\\<access-key> 和 \\<endpoint> 需要手动替换成可用的值。access-key "
"可在 DataWorks 中点击右上角图标 -> 用户信息， 再点击“点击查看”获得。"
"Endpoint 可在 `MaxCompute开通Region和服务连接对照表 <https://help.aliyun."
"com/document_detail/34951.html#h2-maxcompute-region-3>`_ 获得， 或者联系"
"项目管理员。"
msgstr ""
"In this code, replace \\<access-key> and \\<endpoint> with applicable "
"values. To obtain AccessKeys, move the pointer to the user icon in the "
"upper-right corner in the DataWorks console, and click the accesskeys "
"icon in the displayed menu. To obtain endpoints, see `MaxCompute "
"activation and service connections by region "
"<https://www.alibabacloud.com/help/en/doc-"
"detail/34951.htm#MaxCompute%20activation%20and%20service%20connections%20by%20region>`_"
" , or contact the owner of your project."

#: ../../source/platform-migrate-ext.rst:17
msgid "修改完成后，将上述代码放置到所有代码的最顶端即可。"
msgstr "Then, place the modified code to the top of all other code."

