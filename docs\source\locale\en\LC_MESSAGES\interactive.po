# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-17 15:51+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/interactive.rst:5
msgid "交互体验增强"
msgstr "User experience enhancement"

#: ../../source/interactive.rst:8
msgid "命令行增强"
msgstr "Command line"

#: ../../source/interactive.rst:10
msgid ""
"PyODPS 提供了命令行下的增强工具。首先，用户可以在任何地方配置了帐号以后，"
"下次就无需再次输入帐号信息。"
msgstr ""
"PyODPS provides an enhanced command line experience. Once you have set up"
" your account the first time, you no longer need to re-enter account "
"information at logon."

#: ../../source/interactive.rst:12
msgid "from odps.inter import setup, enter, teardown"
msgstr ""

#: ../../source/interactive.rst:16
msgid "接着就可以配置帐号"
msgstr "Configure your account as follows:"

#: ../../source/interactive.rst:18
msgid ""
"import os\n"
"# 保证 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为用户 Access Key ID，\n"
"# ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为用户 Access Key Secret\n"
"# 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
"setup(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    '**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
")"
msgstr ""
"import os\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to Access Key ID of user\n"
"# while environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to "
"Access Key Secret of user.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
"setup(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    '**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
")"

#: ../../source/interactive.rst:31
msgid ""
"在不指定\\ ``room``\\ 这个参数时，会被配置到叫做\\ ``default``\\ 的room里"
"。"
msgstr ""
"When the \\``room``\\ parameter has not been specified, the "
"\\``default``\\ room is set."

#: ../../source/interactive.rst:33
msgid "以后，在任何命令行打开的地方，都可以直接调用："
msgstr ""
"You can then call the enter method to create a Room object in any Python "
"interactive interface, as follows:"

#: ../../source/interactive.rst:35
msgid "room = enter()"
msgstr ""

#: ../../source/interactive.rst:39
msgid "我们可以拿到ODPS的入口："
msgstr "You can retrieve the ODPS object as follows:"

#: ../../source/interactive.rst:41
msgid "o = room.odps"
msgstr ""

#: ../../source/interactive.rst:45
msgid "o.get_table('dual')"
msgstr ""

#: ../../source/interactive.rst:51 ../../source/interactive.rst:116
#: ../../source/interactive.rst:210
msgid ""
"odps.Table\n"
"  name: odps_test_sqltask_finance.`dual`\n"
"  schema:\n"
"    c_int_a                 : bigint\n"
"    c_int_b                 : bigint\n"
"    c_double_a              : double\n"
"    c_double_b              : double\n"
"    c_string_a              : string\n"
"    c_string_b              : string\n"
"    c_bool_a                : boolean\n"
"    c_bool_b                : boolean\n"
"    c_datetime_a            : datetime\n"
"    c_datetime_b            : datetime"
msgstr ""

#: ../../source/interactive.rst:67
msgid ""
"**注意**\\ ：在重新 setup room 后，ODPS 入口对象并不会自动替换，需要再次"
"调用 enter() 以获得新的 Room 对象。"
msgstr ""
"**Note**\\: The ODPS object is not automatically updated when you change "
"the setup of the room. You need to call enter() again to retrieve the new"
" Room object."

#: ../../source/interactive.rst:69
msgid "我们可以把常用的ODPS表或者资源都可以存放在room里。"
msgstr "You can store commonly used MaxCompute tables or resources in the room."

#: ../../source/interactive.rst:71
msgid "room.store('存储表示例', o.get_table('dual'), desc='简单的表存储示例')"
msgstr ""
"room.store('stored-table', o.get_table('dual'), desc='Simple Table Store "
"Example')"

#: ../../source/interactive.rst:75
msgid ""
"我们可以调用\\ ``display``\\ 方法，来把已经存储的对象以表格的形式打印出来"
"："
msgstr ""
"You can call the \\ ``display``\\ method to print the stored objects in a"
" table format:"

#: ../../source/interactive.rst:77 ../../source/interactive.rst:136
msgid "room.display()"
msgstr ""

#: ../../source/interactive.rst:81
msgid ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th>default</th>\n"
"      <th>desc</th>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>name</th>\n"
"      <th></th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>存储表示例</th>\n"
"      <td>简单的表存储示例</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>iris</th>\n"
"      <td>安德森鸢尾花卉数据集</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th>default</th>\n"
"      <th>desc</th>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>name</th>\n"
"      <th></th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>stored-table</th>\n"
"      <td>Simple Table Store Example</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>iris</th>\n"
"      <td>Anderson's Iris data</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"

#: ../../source/interactive.rst:108
msgid ""
"我们通过\\ ``room['存储表示例']``\\ ，或者像\\ ``room.iris``\\ ，就可以"
"取出来存储的对象了。"
msgstr ""
"You can use \\ ``room['stored-table']``\\ or \\ ``room.iris``\\ to "
"retrieve the stored objects."

#: ../../source/interactive.rst:110
msgid "room['存储表示例']"
msgstr "room['stored-table']"

#: ../../source/interactive.rst:130
msgid "删除也很容易，只需要调用drop方法"
msgstr "You can call the drop method to delete objects."

#: ../../source/interactive.rst:132
msgid "room.drop('存储表示例')"
msgstr "room.drop('stored-table')"

#: ../../source/interactive.rst:140 ../../source/interactive.rst:233
msgid ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th>default</th>\n"
"      <th>desc</th>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>name</th>\n"
"      <th></th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>iris</th>\n"
"      <td>安德森鸢尾花卉数据集</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th>default</th>\n"
"      <th>desc</th>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>name</th>\n"
"      <th></th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>iris</th>\n"
"      <td>Anderson's Iris data</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"

#: ../../source/interactive.rst:165
msgid "要删除某个room，只需要调用teardown就可以了，不传参数时删除默认room。"
msgstr ""
"You can delete a room by calling teardown. When no parameter has been "
"specified, the default room is deleted."

#: ../../source/interactive.rst:169
msgid "teardown()"
msgstr ""

#: ../../source/interactive.rst:172
msgid "IPython增强"
msgstr "IPython"

#: ../../source/interactive.rst:174
msgid "PyODPS 还提供了 IPython 的插件，来更方便得操作 ODPS。"
msgstr ""
"PyODPS provides IPython plugins to make it easy to perform MaxCompute "
"operations."

#: ../../source/interactive.rst:176
msgid "首先，针对命令行增强，也有相应的命令。让我们先加载插件："
msgstr ""
"Some commands are provided for command line enhancement. You can load the"
" plugins as follows:"

#: ../../source/interactive.rst:178
#, python-format
msgid "%load_ext odps"
msgstr ""

#: ../../source/interactive.rst:185
#, python-format
msgid "%enter"
msgstr ""

#: ../../source/interactive.rst:194
msgid "<odps.inter.Room at 0x11341df10>"
msgstr ""

#: ../../source/interactive.rst:197
msgid "此时全局会包含o和odps变量，即ODPS入口。"
msgstr "Now the global o and odps variables can be retrieved, as follows:"

#: ../../source/interactive.rst:200
msgid ""
"o.get_table('dual')\n"
"odps.get_table('dual')"
msgstr ""

#: ../../source/interactive.rst:226
#, python-format
msgid "%stores"
msgstr ""

#: ../../source/interactive.rst:258
msgid "对象名补全"
msgstr "Object name completion"

#: ../../source/interactive.rst:259
msgid ""
"PyODPS 拓展了 IPython 原有的代码补全功能，支持在书写 ``o.get_xxx`` 这样的"
"语句时，自动补全对象名。"
msgstr ""
"PyODPS enhances the code completion feature that is provided by IPython. "
"When you are writing statements such as ``o.get_xxx``, the object name is"
" automatically completed."

#: ../../source/interactive.rst:261
msgid ""
"例如，在 IPython 中输入下列语句（<tab>不是实际输入的字符，而是当所有输入"
"完成后，将光标移动到相应位置， 并按 Tab 键）："
msgstr ""
"For example, enter the following statement in IPython. Note that <tab> is"
" not the actual input character. When the entry is complete, move the "
"cursor to the end of the statement and click the Tab button."

#: ../../source/interactive.rst:264
msgid "o.get_table(<tab>"
msgstr ""

#: ../../source/interactive.rst:268
msgid "如果已知需要补全对象的前缀，也可以使用"
msgstr "If you know the first few characters of the object name, for example:"

#: ../../source/interactive.rst:270
msgid "o.get_table('tabl<tab>"
msgstr ""

#: ../../source/interactive.rst:274
msgid "IPython 会自动补全前缀为 tabl 的表。"
msgstr "IPython automatically enters a table name starting with tabl."

#: ../../source/interactive.rst:276
msgid "对象名补全也支持补全不同 Project 下的对象名。下列用法都被支持："
msgstr ""
"This feature can also complete the names of objects from different "
"projects."

#: ../../source/interactive.rst:278
msgid ""
"o.get_table(project='project_name', name='tabl<tab>\n"
"o.get_table('tabl<tab>', project='project_name')"
msgstr ""

#: ../../source/interactive.rst:283
msgid ""
"如果匹配的对象有多个，IPython 会给出一个列表，其最大长度由 ``options."
"completion_size`` 给出， 默认为 10。"
msgstr ""
"A list is given when multiple matching objects exist. The maximum length "
"of the list is set by ``options.completion_size``. The default is 10."

#: ../../source/interactive.rst:288
msgid "SQL命令"
msgstr "SQL statements"

#: ../../source/interactive.rst:292
msgid "PyODPS 还提供了 SQL 插件，来执行 ODPS SQL。下面是单行 SQL："
msgstr ""
"PyODPS provides SQL plugins to execute MaxCompute SQL statements. The "
"following is a single-line SQL statement."

#: ../../source/interactive.rst:294
#, python-format
msgid "%sql select * from pyodps_iris limit 5"
msgstr ""

#: ../../source/interactive.rst:301
msgid ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th></th>\n"
"      <th>sepallength</th>\n"
"      <th>sepalwidth</th>\n"
"      <th>petallength</th>\n"
"      <th>petalwidth</th>\n"
"      <th>name</th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>0</th>\n"
"      <td>5.1</td>\n"
"      <td>3.5</td>\n"
"      <td>1.4</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>1</th>\n"
"      <td>4.9</td>\n"
"      <td>3.0</td>\n"
"      <td>1.4</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>2</th>\n"
"      <td>4.7</td>\n"
"      <td>3.2</td>\n"
"      <td>1.3</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>3</th>\n"
"      <td>4.6</td>\n"
"      <td>3.1</td>\n"
"      <td>1.5</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>4</th>\n"
"      <td>5.0</td>\n"
"      <td>3.6</td>\n"
"      <td>1.4</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"
msgstr ""

#: ../../source/interactive.rst:362
#, python-format
msgid "多行SQL可以使用\\ ``%%sql``\\ 的命令"
msgstr "You can use \\ ``%%sql``\\  to execute multi-line SQL statements."

#: ../../source/interactive.rst:364
#, python-format
msgid ""
"%%sql\n"
"\n"
"select * from pyodps_iris\n"
"where sepallength < 5\n"
"limit 5"
msgstr ""

#: ../../source/interactive.rst:375
msgid ""
"<div style='padding-bottom: 30px'>\n"
"<table border=\"1\" class=\"dataframe\">\n"
"  <thead>\n"
"    <tr style=\"text-align: right;\">\n"
"      <th></th>\n"
"      <th>sepallength</th>\n"
"      <th>sepalwidth</th>\n"
"      <th>petallength</th>\n"
"      <th>petalwidth</th>\n"
"      <th>name</th>\n"
"    </tr>\n"
"  </thead>\n"
"  <tbody>\n"
"    <tr>\n"
"      <th>0</th>\n"
"      <td>4.9</td>\n"
"      <td>3.0</td>\n"
"      <td>1.4</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>1</th>\n"
"      <td>4.7</td>\n"
"      <td>3.2</td>\n"
"      <td>1.3</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>2</th>\n"
"      <td>4.6</td>\n"
"      <td>3.1</td>\n"
"      <td>1.5</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>3</th>\n"
"      <td>4.6</td>\n"
"      <td>3.4</td>\n"
"      <td>1.4</td>\n"
"      <td>0.3</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"    <tr>\n"
"      <th>4</th>\n"
"      <td>4.4</td>\n"
"      <td>2.9</td>\n"
"      <td>1.4</td>\n"
"      <td>0.2</td>\n"
"      <td>Iris-setosa</td>\n"
"    </tr>\n"
"  </tbody>\n"
"</table>\n"
"</div>"
msgstr ""

#: ../../source/interactive.rst:436
msgid "如果想执行参数化SQL查询，则需要替换的参数可以使用\\ ``:参数``\\ 的方式。"
msgstr ""
"To execute parameterized SQL statements, you can use \\ ``:parameter``\\ "
"to specify the parameter."

#: ../../source/interactive.rst:439
#, python-format
msgid ""
"In [1]: %load_ext odps\n"
"\n"
"In [2]: var = '0'\n"
"\n"
"In [3]: %sql select * from table_name where c_string_a = :var\n"
"|==========================================|   1 /  1  (100.00%)         "
"2s\n"
"Out[3]:\n"
"   c_int_a  c_int_b  c_double_a  c_double_b  c_string_a  c_string_b "
"c_bool_a  \\\n"
"0        0        0       -1203           0           0       -1203     "
"True\n"
"\n"
"  c_bool_b         c_datetime_a         c_datetime_b\n"
"0    False  2012-03-30 23:59:58  2012-03-30 23:59:59"
msgstr ""

#: ../../source/interactive.rst:454
#, python-format
msgid ""
"设置SQL运行时参数，可以通过 ``%set`` 设置到全局，或者在sql的cell里用SET"
"进行局部设置。"
msgstr ""
"For SQL runtime parameters, you can use ``%set`` to set a global "
"parameter, or use set within a SQL Cell to set a local parameter."

#: ../../source/interactive.rst:456
#, python-format
msgid ""
"In [17]: %%sql\n"
"         set odps.sql.mapper.split.size = 16;\n"
"         select * from pyodps_iris;"
msgstr ""

#: ../../source/interactive.rst:462
msgid "这个会局部设置，不会影响全局的配置。"
msgstr "The example above sets a local parameter."

#: ../../source/interactive.rst:464
#, python-format
msgid "In [18]: %set odps.sql.mapper.split.size = 16"
msgstr ""

#: ../../source/interactive.rst:468
msgid "这样设置后，后续运行的SQL都会使用这个设置。"
msgstr "The example above sets a global parameter."

#: ../../source/interactive.rst:472
msgid "持久化 pandas DataFrame 到 ODPS 表"
msgstr "Upload pandas DataFrame to MaxCompute tables"

#: ../../source/interactive.rst:475
msgid "PyODPS 还提供把 pandas DataFrame 上传到 ODPS 表的命令:"
msgstr ""
"PyODPS provides the following command to upload pandas DataFrame objects "
"to MaxCompute tables:"

#: ../../source/interactive.rst:477
msgid ""
"import pandas as pd\n"
"import numpy as np\n"
"\n"
"df = pd.DataFrame(np.arange(9).reshape(3, 3), columns=list('abc'))"
msgstr ""

#: ../../source/interactive.rst:484
msgid "%persist df pyodps_pandas_df"
msgstr ""

#: ../../source/interactive.rst:488
msgid ""
"这里的第0个参数\\ ``df``\\ 是前面的变量名，\\ ``pyodps_pandas_df``\\ 是"
"ODPS表名。"
msgstr ""
"The first parameter \\ ``df``\\ is the variable name. \\ "
"``pyodps_pandas_df``\\ is the MaxCompute table name."

#: ../../source/interactive.rst:482
msgid "Jupyter Notebook 增强"
msgstr "Jupyter Notebook "

#: ../../source/interactive.rst:486
msgid "该功能为测试功能，目前对 JupyterLab 不适用，且不保证在所有版本的 Jupyter Notebook 下均可用。"
msgstr ""
"This function is experimental and does not work under JupyterLab. What's "
"more, we do not guarantee its availability under all versions of Jupyter "
"Notebook."

#: ../../source/interactive.rst:488
msgid "PyODPS 针对 Jupyter Notebook 下的探索性数据分析进行了增强，包括结果探索功能以及进度展示功能。"
msgstr ""
"PyODPS enhances features such as result exploration and progress display "
"in Jupyter Notebook."

#: ../../source/interactive.rst:491
msgid "结果探索"
msgstr "Result exploration"

#: ../../source/interactive.rst:492
msgid ""
"PyODPS 在 Jupyter Notebook 中为 SQL Cell 和 DataFrame "
"提供了数据探索功能。对于已拉到本地的数据，可使用交互式的数据探索工具 浏览数据，交互式地绘制图形。"
msgstr ""
"PyODPS provides a data exploration feature for SQL Cell and DataFrame in "
"Jupyter Notebook. You can use interactive data exploration tools to "
"browse data and create graphs."

#: ../../source/interactive.rst:495
msgid ""
"当执行结果为 DataFrame 时，PyODPS 会读取执行结果，并以分页表格的形式展示出来。单击页号或前进 / 后退按钮可在数据中导航， "
"如下图。"
msgstr ""
"If the execution result is a DataFrame object, PyODPS reads the result "
"and displays it in a paged table. Click a page number or the pre/next "
"button to browse the data."

#: ../../source/interactive.rst:499
msgid ".. image:: _static/dfview-data-grid.png"
msgstr ""

#: ../../source/interactive.rst:500
msgid "结果区的顶端为模式选择区。除数据表外，也可以选择柱状图、饼图、折线图和散点图。下图为使用默认字段选择（即前三个字段） 绘制的散点图。"
msgstr ""
"You can choose different graphs to present data on the top of the page. "
"In addition to the data table, a bar chart, pie chart, line chart, and "
"scatter plot are also provided. The following example is a scatter plot "
"created from the default fields, which are the first three fields."

#: ../../source/interactive.rst:504
msgid ".. image:: _static/dfview-scatter.png"
msgstr ""

#: ../../source/interactive.rst:505
msgid ""
"在绘图模式下，单击右上角的配置按钮可以修改图表设置。如下图中，将 name 设置为分组列，X 轴选择为 petallength，Y 轴选择为 "
"petalwidth，则图表变为下图。可见在 petallength - petalwidth 维度下，数据对 name 有较好的区分度。"
msgstr ""
"In a graph mode, you can click the Configuration button on the top right "
"corner to change the settings of a graph. For example, select the name "
"for the grouping column, select the petallength for the X-axis, and "
"select the petalwidth for the Y-axis. As you can see in the following "
"graph, this dimension setting makes it easy to understand the data:"

#: ../../source/interactive.rst:509
msgid ".. image:: _static/dfview-scatter-opts.png"
msgstr ""

#: ../../source/interactive.rst:510
msgid ""
"对于柱状图和饼图，值字段支持选择聚合函数。PyODPS 对柱状图的默认聚合函数为 sum，对饼图则为 count。如需修改聚合函数， "
"可在值字段名称后的聚合函数名上单击，此后选择所需的聚合函数即可。"
msgstr ""
"For bar charts and pie charts, you can select an aggregate function for "
"the value fields. The default function is sum for bar charts, and count "
"for pie charts. You can click the function name after the value fields to"
" change it."

#: ../../source/interactive.rst:513
msgid "对于折线图，需要避免 X 轴包含空值，否则图像可能不符合预期。"
msgstr ""
"For line charts, the values on the X-axis cannot be null. The graph may "
"not meet expectations."

#: ../../source/interactive.rst:516
msgid ".. image:: _static/dfview-pie-aggsel.png"
msgstr ""

#: ../../source/interactive.rst:517
msgid "完成绘图后，可单击“下载”保存绘制的图表。"
msgstr "After finishing your graph, click the Download button to save it."

#: ../../source/interactive.rst:521
msgid "**注意**\\ ：使用此功能需要安装 Pandas ，并保证 ipywidgets 被正确安装。"
msgstr ""
"**Note**\\: You must have Pandas and ipywidgets properly installed to use"
" this feature."

#: ../../source/interactive.rst:524
msgid "进度展示"
msgstr "Progress bar"

#: ../../source/interactive.rst:526
#, python-format
msgid ""
"大型作业执行通常需要较长的时间，因而 PyODPS 提供了进度展示功能。当 DataFrame、机器学习作业或通过 %sql 编写的 SQL "
"语句在 Jupyter Notebook 中执行作业时，会显示当前正在执行的作业列表及总体进度，如下图："
msgstr ""
"The execution of large jobs usually takes a long time. Therefore, PyODPS "
"provides progress bars to show the progress of the execution. When "
"DataFrame objects, machine learning jobs, and SQL statements starting "
"with %sql are executing in Jupyter Notebook, a list of these jobs and "
"their overall progress are displayed as follows:"

#: ../../source/interactive.rst:530
msgid ".. image:: _static/progress_example.png"
msgstr ""

#: ../../source/interactive.rst:531
msgid "当点击某个作业名称上的链接时，会弹出一个对话框，显示该作业中每个 Task 的具体执行进度，如图："
msgstr ""
"When you click a job name, a dialog box pops up, displaying the progress "
"of each task in the job."

#: ../../source/interactive.rst:534
msgid ".. image:: _static/task_details_dialog.png"
msgstr ""

#: ../../source/interactive.rst:535
msgid "当作业运行成功后，浏览器将给出提醒信息，告知作业是否成功："
msgstr ""
"After the execution has been completed, a message pops up, displaying "
"whether the job has been successful."

#: ../../source/interactive.rst:537
msgid ".. image:: _static/exec_notify.png"
msgstr ""

