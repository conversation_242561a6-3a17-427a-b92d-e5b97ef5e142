# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-03 10:06+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-sort-distinct-apply.rst:4
msgid "排序、去重、采样、数据变换"
msgstr "Sorting, deduplication, sampling, and data transformation"

#: ../../source/df-sort-distinct-apply.rst:6
msgid "from odps.df import DataFrame"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:10
msgid "iris = DataFrame(o.get_table('pyodps_iris'))"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:15
msgid "排序"
msgstr "Sorting"

#: ../../source/df-sort-distinct-apply.rst:17
msgid "排序操作只能作用于Collection。我们只需要调用sort或者sort\\_values方法。"
msgstr ""
"You can sort Collection objects only. Use the sort or sort\\_values "
"method to start sorting."

#: ../../source/df-sort-distinct-apply.rst:19
msgid ""
">>> iris.sort('sepalwidth').head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.0         2.0          3.5         1.0  Iris-versicolor\n"
"1          6.2         2.2          4.5         1.5  Iris-versicolor\n"
"2          6.0         2.2          5.0         1.5   Iris-virginica\n"
"3          6.0         2.2          4.0         1.0  Iris-versicolor\n"
"4          5.5         2.3          4.0         1.3  Iris-versicolor"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:29
msgid "如果想要降序排列，则可以使用参数\\ ``ascending``\\ ，并设为False。"
msgstr ""
"To sort data in a descending order, set the \\ ``ascending``\\ parameter "
"to False."

#: ../../source/df-sort-distinct-apply.rst:31
msgid ""
">>> iris.sort('sepalwidth', ascending=False).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.7         4.4          1.5         0.4  Iris-setosa\n"
"1          5.5         4.2          1.4         0.2  Iris-setosa\n"
"2          5.2         4.1          1.5         0.1  Iris-setosa\n"
"3          5.8         4.0          1.2         0.2  Iris-setosa\n"
"4          5.4         3.9          1.3         0.4  Iris-setosa"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:41
msgid "也可以这样调用，来进行降序排列："
msgstr "You can also sort data in a descending order in the following way:"

#: ../../source/df-sort-distinct-apply.rst:43
msgid ""
">>> iris.sort(-iris.sepalwidth).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.7         4.4          1.5         0.4  Iris-setosa\n"
"1          5.5         4.2          1.4         0.2  Iris-setosa\n"
"2          5.2         4.1          1.5         0.1  Iris-setosa\n"
"3          5.8         4.0          1.2         0.2  Iris-setosa\n"
"4          5.4         3.9          1.3         0.4  Iris-setosa"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:53
msgid "多字段排序也很简单："
msgstr "The system sorts multiple fields easily as shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:55
msgid ""
">>> iris.sort(['sepalwidth', 'petallength']).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.0         2.0          3.5         1.0  Iris-versicolor\n"
"1          6.0         2.2          4.0         1.0  Iris-versicolor\n"
"2          6.2         2.2          4.5         1.5  Iris-versicolor\n"
"3          6.0         2.2          5.0         1.5   Iris-virginica\n"
"4          4.5         2.3          1.3         0.3      Iris-setosa"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:65
msgid ""
"多字段排序时，如果是升序降序不同，\\ ``ascending``\\ 参数可以传入一个列表"
"，长度必须等同于排序的字段，它们的值都是boolean类型"
msgstr ""
"To sort multiple fields in both ascending and descending orders, use the "
"\\ ``ascending``\\ parameter with a list of boolean values. The length of"
" the list must be equal to that of sorted fields."

#: ../../source/df-sort-distinct-apply.rst:67
msgid ""
">>> iris.sort(['sepalwidth', 'petallength'], ascending=[True, "
"False]).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.0         2.0          3.5         1.0  Iris-versicolor\n"
"1          6.0         2.2          5.0         1.5   Iris-virginica\n"
"2          6.2         2.2          4.5         1.5  Iris-versicolor\n"
"3          6.0         2.2          4.0         1.0  Iris-versicolor\n"
"4          6.3         2.3          4.4         1.3  Iris-versicolor"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:77
msgid "下面效果是一样的："
msgstr "To achieve the same effect, the preceding code can be modified as follows:"

#: ../../source/df-sort-distinct-apply.rst:79
msgid ""
">>> iris.sort(['sepalwidth', -iris.petallength]).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.0         2.0          3.5         1.0  Iris-versicolor\n"
"1          6.0         2.2          5.0         1.5   Iris-virginica\n"
"2          6.2         2.2          4.5         1.5  Iris-versicolor\n"
"3          6.0         2.2          4.0         1.0  Iris-versicolor\n"
"4          6.3         2.3          4.4         1.3  Iris-versicolor"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:92
msgid ""
"由于 ODPS 要求排序必须指定个数，所以在 ODPS 后端执行时， 会通过 ``options"
".df.odps.sort.limit`` 指定排序个数，这个值默认是 10000， 如果要排序尽量多"
"的数据，可以把这个值设到较大的值。不过注意，此时可能会导致 OOM。"
msgstr ""
"MaxCompute requires the number of items to be picked from the top of the "
"sorted list. You can use ``options.df.odps.sort.limit`` to specify the "
"number of items that you want to retrieve from the top of the sorted "
"list. The default value is 10000. To sort large volumes of data, set the "
"parameter to a larger value. However, this setting may cause an out-of-"
"memory (OOM) error."

#: ../../source/df-sort-distinct-apply.rst:97
msgid "去重"
msgstr "Deduplication"

#: ../../source/df-sort-distinct-apply.rst:99
msgid "去重在Collection上，用户可以调用distinct方法。"
msgstr "To deduplicate Collection objects, use the distinct method."

#: ../../source/df-sort-distinct-apply.rst:101
msgid ""
">>> iris[['name']].distinct()\n"
"              name\n"
"0      Iris-setosa\n"
"1  Iris-versicolor\n"
"2   Iris-virginica"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:109
msgid ""
">>> iris.distinct('name')\n"
"              name\n"
"0      Iris-setosa\n"
"1  Iris-versicolor\n"
"2   Iris-virginica"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:117
msgid ""
">>> iris.distinct('name', 'sepallength').head(3)\n"
"          name  sepallength\n"
"0  Iris-setosa          4.3\n"
"1  Iris-setosa          4.4\n"
"2  Iris-setosa          4.5"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:125
msgid ""
"在Sequence上，用户可以调用unique，但是记住，调用unique的Sequence不能用在"
"列选择中。"
msgstr ""
"To deduplicate Sequence objects, use the unique method. However, the "
"Sequence object for the unique method cannot be used in column selection."

#: ../../source/df-sort-distinct-apply.rst:127
msgid ""
">>> iris.name.unique()\n"
"              name\n"
"0      Iris-setosa\n"
"1  Iris-versicolor\n"
"2   Iris-virginica"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:136
msgid "下面的代码是错误的用法。"
msgstr "We do not recommend that you use the following code:"

#: ../../source/df-sort-distinct-apply.rst:138
msgid ">>> iris[iris.name, iris.name.unique()]  # 错误的"
msgstr ">>> iris[iris.name, iris.name.unique()]  # this line produces an error"

#: ../../source/df-sort-distinct-apply.rst:144
msgid "采样"
msgstr "Sampling"

#: ../../source/df-sort-distinct-apply.rst:147
msgid ""
"要对一个 collection 的数据采样，可以调用 ``sample`` 方法。PyODPS 支持四种"
"采样方式。"
msgstr ""
"To sample data from a Collection object, use the ``sample`` method. "
"Python on MaxCompute (PyODPS) supports the following sampling means:"

#: ../../source/df-sort-distinct-apply.rst:150
msgid ""
"除了按份数采样外，其余方法如果要在 ODPS DataFrame 上执行，需要 Project "
"支持 XFlow，否则，这些方法只能在 Pandas DataFrame 后端上执行。"
msgstr ""
"Except for sampling by parts, other sampling methods require XFlow to "
"execute on the MaxCompute DataFrame. If they do not support XFlow, these "
"sampling methods can only be executed at the backend of Pandas DataFrame."
" "

#: ../../source/df-sort-distinct-apply.rst:153
msgid "按份数采样"
msgstr "Sampling by parts"

#: ../../source/df-sort-distinct-apply.rst:155
msgid "在这种采样方式下，数据被分为 ``parts`` 份，可选择选取的份数序号。"
msgstr ""
"Data is divided into parts by using this method. You can select the part "
"number that you want to sample."

#: ../../source/df-sort-distinct-apply.rst:157
msgid ""
">>> iris.sample(parts=10)  # 分成10份，默认取第0份\n"
">>> iris.sample(parts=10, i=0)  # 手动指定取第0份\n"
">>> iris.sample(parts=10, i=[2, 5])   # 分成10份，取第2和第5份\n"
">>> iris.sample(parts=10, columns=['name', 'sepalwidth'])  # 根据name和"
"sepalwidth的值做采样"
msgstr ""
">>> iris.sample(parts=10)  # split into 10 parts and take the 0th by "
"default\n"
">>> iris.sample(parts=10, i=0)  # split into 10 parts and take the 0th "
"part\n"
">>> iris.sample(parts=10, i=[2, 5])   # split into 10 parts and take the "
"2nd and 5th part\n"
">>> iris.sample(parts=10, columns=['name', 'sepalwidth'])  # sample given"
" values of name and sepalwidth"

#: ../../source/df-sort-distinct-apply.rst:164
msgid "按比例 / 条数采样"
msgstr "Sampling by percentage or items"

#: ../../source/df-sort-distinct-apply.rst:166
msgid ""
"在这种采样方式下，用户指定需要采样的数据条数或采样比例。指定 ``replace`` "
"参数为 True 可启用放回采样。"
msgstr ""
"You need to specify the number of data items or the percentage of data "
"that you want to sample using this method. To enable sampling with "
"replacement, set the ``replace`` parameter to True."

#: ../../source/df-sort-distinct-apply.rst:168
msgid ""
">>> iris.sample(n=100)  # 选取100条数据\n"
">>> iris.sample(frac=0.3)  # 采样30%的数据"
msgstr ""
">>> iris.sample(n=100)  # sample 100 records\n"
">>> iris.sample(frac=0.3)  # sample 30% of all records"

#: ../../source/df-sort-distinct-apply.rst:173
msgid "按权重列采样"
msgstr "Sampling by weight"

#: ../../source/df-sort-distinct-apply.rst:175
msgid ""
"在这种采样方式下，用户指定权重列和数据条数 / 采样比例。指定 ``replace`` "
"参数为 True 可启用放回采样。"
msgstr ""
"You need to specify the weight column and the number of data items or the"
" proportion of data that you want sample in this way. To enable sampling "
"with replacement, set the ``replace`` parameter to True."

#: ../../source/df-sort-distinct-apply.rst:177
msgid ""
">>> iris.sample(n=100, weights='sepal_length')\n"
">>> iris.sample(n=100, weights='sepal_width', replace=True)"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:182
msgid "分层采样"
msgstr "Stratified sampling"

#: ../../source/df-sort-distinct-apply.rst:184
msgid ""
"在这种采样方式下，用户指定用于分层的标签列，同时为需要采样的每个标签指定"
"采样比例（ ``frac`` 参数）或条数 （ ``n`` 参数）。暂不支持放回采样。"
msgstr ""
"In this sampling, you need to specify the label column that you want to "
"stratify, and specify the sampling proportion (the ``frac`` parameter) or"
" the number of items (the ``n`` parameter) for each label. Currently, "
"stratified sampling does not support replacement."

#: ../../source/df-sort-distinct-apply.rst:187
msgid ""
">>> iris.sample(strata='category', n={'Iris Setosa': 10, 'Iris "
"Versicolour': 10})\n"
">>> iris.sample(strata='category', frac={'Iris Setosa': 0.5, 'Iris "
"Versicolour': 0.4})"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:193
msgid "数据缩放"
msgstr "Data scaling"

#: ../../source/df-sort-distinct-apply.rst:195
msgid "DataFrame 支持通过最大/最小值或平均值/标准差对数据进行缩放。例如，对数据"
msgstr ""
"DataFrame supports data scaling by using the maximum, minimum, mean, and "
"standard deviation. The following is an example for scaling:"

#: ../../source/df-sort-distinct-apply.rst:197
msgid ""
"    name  id  fid\n"
"0  name1   4  5.3\n"
"1  name2   2  3.5\n"
"2  name2   3  1.5\n"
"3  name1   4  4.2\n"
"4  name1   3  2.2\n"
"5  name1   3  4.1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:207
msgid "使用 min_max_scale 方法进行归一化："
msgstr "You can use the min_max_scale method to normalize data:"

#: ../../source/df-sort-distinct-apply.rst:209
msgid ""
">>> df.min_max_scale(columns=['fid'])\n"
"    name  id       fid\n"
"0  name1   4  1.000000\n"
"1  name2   2  0.526316\n"
"2  name2   3  0.000000\n"
"3  name1   4  0.710526\n"
"4  name1   3  0.184211\n"
"5  name1   3  0.684211"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:220
msgid ""
"min_max_scale 还支持使用 feature_range 参数指定输出值的范围，例如，如果"
"我们需要使输出值在 (-1, 1) 范围内，可使用"
msgstr ""
"min_max_scale can work with the feature_range parameter to specify the "
"output range. The following is an example of how to output values in the "
"range of (-1, 1):"

#: ../../source/df-sort-distinct-apply.rst:223
msgid ""
">>> df.min_max_scale(columns=['fid'], feature_range=(-1, 1))\n"
"    name  id       fid\n"
"0  name1   4  1.000000\n"
"1  name2   2  0.052632\n"
"2  name2   3 -1.000000\n"
"3  name1   4  0.421053\n"
"4  name1   3 -0.631579\n"
"5  name1   3  0.368421"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:234
msgid ""
"如果需要保留原始值，可以使用 preserve 参数。此时，缩放后的数据将会以新增"
"列的形式追加到数据中， 列名默认为原列名追加“_scaled”后缀，该后缀可使用 "
"suffix 参数更改。例如，"
msgstr ""
"To keep original values, use the preserve parameter. Then, scaled data is"
" added to a new column that is named as the original column name suffixed"
" with “_scaled”. To change the suffix, use the suffix parameter, as "
"shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:237
msgid ""
">>> df.min_max_scale(columns=['fid'], preserve=True)\n"
"    name  id  fid  fid_scaled\n"
"0  name1   4  5.3    1.000000\n"
"1  name2   2  3.5    0.526316\n"
"2  name2   3  1.5    0.000000\n"
"3  name1   4  4.2    0.710526\n"
"4  name1   3  2.2    0.184211\n"
"5  name1   3  4.1    0.684211"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:248
msgid ""
"min_max_scale 也支持使用 group 参数指定一个或多个分组列，在分组列中分别取"
"最值进行缩放。例如，"
msgstr ""
"min_max_scale can also work with the group parameter to specify one or "
"more group columns and to retrieve the minimum and maximum from the "
"specified column to scale data, as shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:250
msgid ""
">>> df.min_max_scale(columns=['fid'], group=['name'])\n"
"    name  id       fid\n"
"0  name1   4  1.000000\n"
"1  name1   4  0.645161\n"
"2  name1   3  0.000000\n"
"3  name1   3  0.612903\n"
"4  name2   2  1.000000\n"
"5  name2   3  0.000000"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:261
msgid "可见结果中，name1 和 name2 两组均按组中的最值进行了缩放。"
msgstr ""
"The system has scaled data in both name1 and name2 by using the minimum "
"and maximum."

#: ../../source/df-sort-distinct-apply.rst:263
msgid "std_scale 可依照标准正态分布对数据进行调整。例如，"
msgstr ""
"The std_scale method scales data by using standard normal distribution, "
"as shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:265
msgid ""
">>> df.std_scale(columns=['fid'])\n"
"    name  id       fid\n"
"0  name1   4  1.436467\n"
"1  name2   2  0.026118\n"
"2  name2   3 -1.540938\n"
"3  name1   4  0.574587\n"
"4  name1   3 -0.992468\n"
"5  name1   3  0.496234"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:276
msgid ""
"std_scale 同样支持 preserve 参数保留原始列以及使用 group 进行分组，具体请"
"参考 min_max_scale，此处不再赘述。"
msgstr ""
"This method also supports using the preserve parameter to keep the "
"original column and the group parameter, in order to group data. For more"
" information about grouping, see min_max_scale."

#: ../../source/df-sort-distinct-apply.rst:279
msgid "空值处理"
msgstr "Null value processing"

#: ../../source/df-sort-distinct-apply.rst:281
msgid "DataFrame 支持筛去空值以及填充空值的功能。例如，对数据"
msgstr ""
"DataFrame supports removing or filling null values. The following data is"
" the example for processing:"

#: ../../source/df-sort-distinct-apply.rst:283
msgid ""
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  NaN  3.0  4.0\n"
"1   1  name1  2.0  NaN  NaN  1.0\n"
"2   2  name1  3.0  4.0  1.0  NaN\n"
"3   3  name1  NaN  1.0  2.0  3.0\n"
"4   4  name1  1.0  NaN  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0\n"
"6   6  name1  NaN  NaN  NaN  NaN"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:294
msgid "使用 dropna 可删除 subset 中包含空值的行："
msgstr ""
"You can use the dropna method to delete the rows that contain null values"
" in the subset object, as follows:"

#: ../../source/df-sort-distinct-apply.rst:296
msgid ""
">>> df.dropna(subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   5  name1  1.0  2.0  3.0  4.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:302
msgid "如果行中包含非空值则不删除，可以使用 how='all'："
msgstr ""
"If the rows also contain non-null values, you can use how='all' to keep "
"these rows, as follows:"

#: ../../source/df-sort-distinct-apply.rst:304
msgid ""
">>> df.dropna(how='all', subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  NaN  3.0  4.0\n"
"1   1  name1  2.0  NaN  NaN  1.0\n"
"2   2  name1  3.0  4.0  1.0  NaN\n"
"3   3  name1  NaN  1.0  2.0  3.0\n"
"4   4  name1  1.0  NaN  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:315
msgid "你也可以使用 thresh 参数来指定行中至少要有多少个非空值。例如："
msgstr ""
"To specify the minimum number of non-null values in the rows, use the "
"thresh parameter to keep qualified rows, as shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:317
msgid ""
">>> df.dropna(thresh=3, subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  NaN  3.0  4.0\n"
"2   2  name1  3.0  4.0  1.0  NaN\n"
"3   3  name1  NaN  1.0  2.0  3.0\n"
"4   4  name1  1.0  NaN  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:327
msgid "使用 fillna 可使用常数或已有的列填充未知值。下面给出了使用常数填充的例子："
msgstr ""
"You can use fillna to replace null values with a specified constant or "
"values in an existing column. In the following example, null values are "
"replaced with a constant:"

#: ../../source/df-sort-distinct-apply.rst:329
msgid ""
">>> df.fillna(100, subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name     f1     f2     f3     f4\n"
"0   0  name1    1.0  100.0    3.0    4.0\n"
"1   1  name1    2.0  100.0  100.0    1.0\n"
"2   2  name1    3.0    4.0    1.0  100.0\n"
"3   3  name1  100.0    1.0    2.0    3.0\n"
"4   4  name1    1.0  100.0    3.0    4.0\n"
"5   5  name1    1.0    2.0    3.0    4.0\n"
"6   6  name1  100.0  100.0  100.0  100.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:341
msgid "你也可以使用一个已有的列来填充未知值。例如："
msgstr ""
"You can replace null values with values in the specified existing column,"
" as shown in the following code:"

#: ../../source/df-sort-distinct-apply.rst:343
msgid ""
">>> df.fillna(df.f2, subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  NaN  3.0  4.0\n"
"1   1  name1  2.0  NaN  NaN  1.0\n"
"2   2  name1  3.0  4.0  1.0  4.0\n"
"3   3  name1  1.0  1.0  2.0  3.0\n"
"4   4  name1  1.0  NaN  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0\n"
"6   6  name1  NaN  NaN  NaN  NaN"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:355
msgid ""
"特别地，DataFrame 提供了向前 / 向后填充的功能。通过指定 method 参数为下列"
"值可以达到目的："
msgstr ""
"DataFrame also provides backward filling and forward filling by using the"
" method parameter, as follows:"

#: ../../source/df-sort-distinct-apply.rst:358
msgid "取值"
msgstr "Value"

#: ../../source/df-sort-distinct-apply.rst:358
msgid "含义"
msgstr "Definition"

#: ../../source/df-sort-distinct-apply.rst:360
msgid "bfill / backfill"
msgstr "bfill / backfill"

#: ../../source/df-sort-distinct-apply.rst:360
msgid "向前填充"
msgstr "Backward filling"

#: ../../source/df-sort-distinct-apply.rst:361
msgid "ffill / pad"
msgstr "ffill / pad"

#: ../../source/df-sort-distinct-apply.rst:361
msgid "向后填充"
msgstr "Forward filling"

#: ../../source/df-sort-distinct-apply.rst:364
msgid "例如："
msgstr "Example:"

#: ../../source/df-sort-distinct-apply.rst:366
msgid ""
">>> df.fillna(method='bfill', subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  3.0  3.0  4.0\n"
"1   1  name1  2.0  1.0  1.0  1.0\n"
"2   2  name1  3.0  4.0  1.0  NaN\n"
"3   3  name1  1.0  1.0  2.0  3.0\n"
"4   4  name1  1.0  3.0  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0\n"
"6   6  name1  NaN  NaN  NaN  NaN\n"
">>> df.fillna(method='ffill', subset=['f1', 'f2', 'f3', 'f4'])\n"
"   id   name   f1   f2   f3   f4\n"
"0   0  name1  1.0  1.0  3.0  4.0\n"
"1   1  name1  2.0  2.0  2.0  1.0\n"
"2   2  name1  3.0  4.0  1.0  1.0\n"
"3   3  name1  NaN  1.0  2.0  3.0\n"
"4   4  name1  1.0  1.0  3.0  4.0\n"
"5   5  name1  1.0  2.0  3.0  4.0\n"
"6   6  name1  NaN  NaN  NaN  NaN"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:387
msgid ""
"你也可以使用 ffill / bfill 函数来简化代码。ffill 等价于 fillna(method='"
"ffill')， bfill 等价于 fillna(method='bfill')"
msgstr ""
"You can use the ffill / bfill function to simplify code, where ffill "
"equals to fillna(method='ffill') and bfill equals to "
"fillna(method='bfill')."

#: ../../source/df-sort-distinct-apply.rst:391
msgid "对所有行/列调用自定义函数"
msgstr "Applying custom functions for all columns and rows"

#: ../../source/df-sort-distinct-apply.rst:396
msgid "对一行数据使用自定义函数"
msgstr "Using custom functions for one row"

#: ../../source/df-sort-distinct-apply.rst:398
msgid ""
"要对一行数据使用自定义函数，可以使用 apply 方法，axis 参数必须为 1，表示"
"在行上操作。"
msgstr ""
"To use custom functions for one row, you can use the apply method. The "
"axis parameter must be 1 to indicate that the operation works on the row."

#: ../../source/df-sort-distinct-apply.rst:400
msgid ""
"apply 的自定义函数接收一个参数，为上一步 Collection 的一行数据，用户可以"
"通过属性、或者偏移取得一个字段的数据。"
msgstr ""
"The apply method calls your function. You can specify the type or offset "
"in the function to retrieve data in a field from the row in the preceding"
" Collection object."

#: ../../source/df-sort-distinct-apply.rst:402
msgid ""
">>> iris.apply(lambda row: row.sepallength + row.sepalwidth, axis=1, "
"reduce=True, types='float').rename('sepaladd').head(3)\n"
"   sepaladd\n"
"0       8.6\n"
"1       7.9\n"
"2       7.9"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:410
msgid ""
"``reduce``\\ 为 True 时，表示返回结果为Sequence，否则返回结果为Collection"
"。 ``names``\\ 和 ``types``\\ 参数分别指定返回的Sequence或Collection的"
"字段名和类型。 如果类型不指定，将会默认为string类型。"
msgstr ""
"If ``reduce``\\ is True, the system returns the Sequence object. If not, "
"the system returns the Collection object. Use the ``names``\\ and "
"``types``\\ parameters to specify the field name and type of the returned"
" Sequence or Collection object. The type is string by default if not "
"specified."

#: ../../source/df-sort-distinct-apply.rst:414
msgid ""
"在 apply 的自定义函数中，reduce 为 False 时，也可以使用 ``yield``\\ "
"关键字来返回多行结果。"
msgstr ""
"If reduce is False in your function, you can use the ``yield``\\ keyword "
"to return multiple rows."

#: ../../source/df-sort-distinct-apply.rst:416
msgid ""
">>> iris.count()\n"
"150\n"
">>>\n"
">>> def handle(row):\n"
">>>     yield row.sepallength - row.sepalwidth, row.sepallength + "
"row.sepalwidth\n"
">>>     yield row.petallength - row.petalwidth, row.petallength + "
"row.petalwidth\n"
">>>\n"
">>> iris.apply(handle, axis=1, names=['iris_add', 'iris_sub'], "
"types=['float', 'float']).count()\n"
"300\n"
">>> iris.apply(handle, axis=1, names=['iris_add', 'iris_sub'], "
"types=['float', 'float']).head(5)\n"
"   iris_add  iris_sub\n"
"0       1.6       8.6\n"
"1       1.2       1.6\n"
"2       1.9       7.9\n"
"3       1.2       1.6\n"
"4       1.5       7.9"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:435
msgid "我们也可以在函数上来注释返回的字段和类型，这样就不需要在函数调用时再指定。"
msgstr ""
"You can also annotate your function with returned field names and types. "
"In this way, you do not need to specify them when calling your function."

#: ../../source/df-sort-distinct-apply.rst:438
msgid ""
">>> from odps.df import output\n"
">>>\n"
">>> @output(['iris_add', 'iris_sub'], ['float', 'float'])\n"
">>> def handle(row):\n"
">>>     yield row.sepallength - row.sepalwidth, row.sepallength + "
"row.sepalwidth\n"
">>>     yield row.petallength - row.petalwidth, row.petallength + "
"row.petalwidth\n"
">>>\n"
">>> iris.apply(handle, axis=1).count()\n"
"300\n"
">>> iris.apply(handle, axis=1).head(5)\n"
"   iris_add  iris_sub\n"
"0       1.6       8.6\n"
"1       1.2       1.6\n"
"2       1.9       7.9\n"
"3       1.2       1.6\n"
"4       1.5       7.9"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:457
msgid "也可以使用 map-only 的 map_reduce，和 axis=1 的apply操作是等价的。"
msgstr ""
"Calling ``map_reduce`` without reducers is equal to calling ``apply`` "
"method with axis=1."

#: ../../source/df-sort-distinct-apply.rst:459
msgid ""
">>> iris.map_reduce(mapper=handle).count()\n"
"300\n"
">>> iris.map_reduce(mapper=handle).head(5)\n"
"   iris_add  iris_sub\n"
"0       1.6       8.6\n"
"1       1.2       1.6\n"
"2       1.9       7.9\n"
"3       1.2       1.6\n"
"4       1.5       7.9"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:471
msgid "如果想调用 ODPS 上已经存在的 UDTF，则函数指定为函数名即可。"
msgstr ""
"To use an existing user-defined table function (UDTF) in MaxCompute, "
"specify the name of the UDTF."

#: ../../source/df-sort-distinct-apply.rst:473
msgid ""
">>> iris['name', 'sepallength'].apply('your_func', axis=1, "
"names=['name2', 'sepallength2'], types=['string', 'float'])"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:477
msgid ""
"使用 apply 对行操作，且 ``reduce``\\ 为 False 时，可以使用 :ref:`"
"dflateralview` 与已有的行结合，用于后续聚合等操作。"
msgstr ""
"When you use the apply method in a row and ``reduce``\\ is False, combine"
" this row with an existing row by using :ref:`dflateralview` to prepare "
"for aggregation."

#: ../../source/df-sort-distinct-apply.rst:479
msgid ""
">>> from odps.df import output\n"
">>>\n"
">>> @output(['iris_add', 'iris_sub'], ['float', 'float'])\n"
">>> def handle(row):\n"
">>>     yield row.sepallength - row.sepalwidth, row.sepallength + "
"row.sepalwidth\n"
">>>     yield row.petallength - row.petalwidth, row.petallength + "
"row.petalwidth\n"
">>>\n"
">>> iris[iris.category, iris.apply(handle, axis=1)]"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:491
msgid "对所有列调用自定义聚合"
msgstr "Using custom aggregations for all columns"

#: ../../source/df-sort-distinct-apply.rst:493
msgid ""
"调用apply方法，当我们不指定axis，或者axis为0的时候，我们可以通过传入一个"
"自定义聚合类来对所有sequence进行聚合操作。"
msgstr ""
"When you use the apply method and axis is not specified or is 0, call a "
"custom aggregation to aggregate all Sequence objects."

#: ../../source/df-sort-distinct-apply.rst:495
msgid ""
"class Agg(object):\n"
"\n"
"    def buffer(self):\n"
"        return [0.0, 0]\n"
"\n"
"    def __call__(self, buffer, val):\n"
"        buffer[0] += val\n"
"        buffer[1] += 1\n"
"\n"
"    def merge(self, buffer, pbuffer):\n"
"        buffer[0] += pbuffer[0]\n"
"        buffer[1] += pbuffer[1]\n"
"\n"
"    def getvalue(self, buffer):\n"
"        if buffer[1] == 0:\n"
"            return 0.0\n"
"        return buffer[0] / buffer[1]"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:515
msgid ""
">>> iris.exclude('name').apply(Agg)\n"
"   sepallength_aggregation  sepalwidth_aggregation  "
"petallength_aggregation  petalwidth_aggregation\n"
"0                 5.843333                   3.054                 "
"3.758667                1.198667"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:523
msgid ""
"目前，受限于 Python UDF，自定义函数无法支持将 list / dict 类型作为初始"
"输入或最终输出结果。"
msgstr ""
"Limited by Python UDFs, custom aggregations cannot specify the input or "
"the output result type as the list or dict type."

#: ../../source/df-sort-distinct-apply.rst:527
msgid ""
"由于 PyODPS DataFrame 默认 Collection / Sequence 等对象均为分布式对象，故"
"不支持在自定义函数内部引用这些对象。 请考虑改用 :ref:`Join 等方法 <"
"dfmerge>` 引用多个 DataFrame 的数据，或者引用 Collection 作为资源，如下文"
"所述。"
msgstr ""
"PyODPS DataFrame recognizes all collections and sequences as distributed "
"objects, and does not support referencing these objects inside user-"
"defined functions. Please consider using :ref:`methods like join "
"<dfmerge>` to reference data in multiple DataFrames, or referencing "
"collections as resources, which is stated in the next section."

#: ../../source/df-sort-distinct-apply.rst:531
#: ../../source/df-sort-distinct-apply.rst:708
msgid "引用资源"
msgstr "Referring to resources"

#: ../../source/df-sort-distinct-apply.rst:533
msgid ""
"类似于对 :ref:`map <map>` 方法的resources参数，每个resource可以是ODPS上的"
"资源（表资源或文件资源），或者引用一个collection作为资源。"
msgstr ""
"Similar to the resources parameter for :ref:`map <map>` , resources can "
"be tables, files, or Collection objects that you refer to in MaxCompute."

#: ../../source/df-sort-distinct-apply.rst:535
msgid ""
"对于axis为1，也就是在行上操作，我们需要写一个函数闭包或者callable的类。 "
"而对于列上的聚合操作，我们只需在 \\_\\_init\\_\\_ 函数里读取资源即可。"
msgstr ""
"If axis is 1 in a row operation, write a function closure or callable "
"class. For column aggregation, you can read resources by using the "
"\\_\\_init\\_\\_ function."

#: ../../source/df-sort-distinct-apply.rst:539
msgid ""
">>> words_df\n"
"                     sentence\n"
"0                 Hello World\n"
"1                Hello Python\n"
"2  Life is short I use Python\n"
">>>\n"
">>> import pandas as pd\n"
">>> stop_words = DataFrame(pd.DataFrame({'stops': ['is', 'a', 'I']}))\n"
">>>\n"
">>> @output(['sentence'], ['string'])\n"
">>> def filter_stops(resources):\n"
">>>     stop_words = set([r[0] for r in resources[0]])\n"
">>>     def h(row):\n"
">>>         return ' '.join(w for w in row[0].split() if w not in "
"stop_words),\n"
">>>     return h\n"
">>>\n"
">>> words_df.apply(filter_stops, axis=1, resources=[stop_words])\n"
"                sentence\n"
"0            Hello World\n"
"1           Hello Python\n"
"2  Life short use Python"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:563
msgid ""
"可以看到这里的stop_words是存放于本地，但在真正执行时会被上传到ODPS作为"
"资源引用。"
msgstr ""
"In this example, ``stop_words`` is a local variable which is referenced "
"as a resource in MaxCompute during execution."

#: ../../source/df-sort-distinct-apply.rst:567
#: ../../source/df-sort-distinct-apply.rst:687
msgid "使用第三方Python库"
msgstr "Using a third-party Python library"

#: ../../source/df-sort-distinct-apply.rst:568
#: ../../source/df-sort-distinct-apply.rst:688
msgid ""
"现在用户可以把第三方 Wheel 包作为资源上传到 MaxCompute。在全局或者在立即"
"执行的方法时，指定需要使用的包文件， 即可以在自定义函数中使用第三方库。"
"值得注意的是，第三方库的依赖库也必须指定，否则依然会有导入错误。"
msgstr ""
"You can upload third-party wheel packages as resources to MaxCompute. You"
" need to specify the package files globally or in methods that execute "
"DataFrames immediately. Note that you also need to add dependencies of "
"your third-party libraries, or import could fail."

#: ../../source/df-sort-distinct-apply.rst:571
#: ../../source/df-sort-distinct-apply.rst:691
msgid ""
"如果你的 MaxCompute 服务支持在执行 SQL 时使用镜像，可以在 execute / "
"persist / to_pandas 方法指定 ``image`` 参数以使用镜像。与此同时，"
"DataFrame 的 execute / persist / to_pandas 方法支持增加 ``libraries`` "
"参数以将资源作为三方包。 PyODPS 提供了 ``pyodps-pack`` 工具，可在安装完 "
"PyODPS 后打包三方包及其依赖。如何制作及使用三方包的说明请参考 :ref:`这里 "
"<pyodps_pack>`。"
msgstr ""
"If your MaxCompute service supports specifying images when executing SQL "
"statements, you may specify ``image`` argument with ``execute``, "
"``persist`` or ``to_pandas`` to use these images. Meanwhile ``libraries``"
" argument can be used with ``execute``, ``persist`` or ``to_pandas`` to "
"specify resources as thirdparty libraries. PyODPS installation provides "
"``pyodps-pack`` tool for packing third-party libraries. You may take a "
"look at :ref:`documents here <pyodps_pack>` to see how to build and use "
"these third-party libraries."

#: ../../source/df-sort-distinct-apply.rst:577
#: ../../source/df-sort-distinct-apply.rst:698
msgid ""
"由于字节码定义的差异，Python 3 下使用新语言特性（例如 ``yield from`` ）时"
"，代码在使用 Python 2.7 的 ODPS Worker 上执行时会发生错误。因而建议在 "
"Python 3 下使用 MapReduce API 编写生产作业前，先确认相关代码是否能正常 "
"执行。"
msgstr ""
"Due to the difference in bytecode definition, when you write code with "
"new language features such as ``yield from`` in Python 3, the system "
"generates an error when running the code in the MaxCompute Worker of "
"Python 2.7. Therefore, we recommend that you make sure that the code runs"
" normally before you implement the production operations using the "
"MapReduce API (application program interface) of Python 3."

#: ../../source/df-sort-distinct-apply.rst:584
msgid "MapReduce API"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:585
msgid ""
"PyODPS DataFrame也支持MapReduce API，用户可以分别编写map和reduce函数（map"
"_reduce可以只有mapper或者reducer过程）。 我们来看个简单的wordcount的例子"
"。"
msgstr ""
"PyODPS DataFrame supports the MapReduce API. You can write the map and "
"reduce functions respectively, because map_reduce may include the mapper "
"or reducer process only. The following code is an example of a wordcount:"

#: ../../source/df-sort-distinct-apply.rst:589
msgid ""
">>> def mapper(row):\n"
">>>     for word in row[0].split():\n"
">>>         yield word.lower(), 1\n"
">>>\n"
">>> def reducer(keys):\n"
">>>     # 这里使用 list 而不是 cnt = 0，否则 h 内的 cnt 会被认为是"
"局部变量，其中的赋值无法输出\n"
">>>     cnt = [0]\n"
">>>     def h(row, done):  # done表示这个key已经迭代结束\n"
">>>         cnt[0] += row[1]\n"
">>>         if done:\n"
">>>             yield keys[0], cnt[0]\n"
">>>     return h\n"
">>>\n"
">>> words_df.map_reduce(mapper, reducer, group=['word', ],\n"
">>>                     mapper_output_names=['word', 'cnt'],\n"
">>>                     mapper_output_types=['string', 'int'],\n"
">>>                     reducer_output_names=['word', 'cnt'],\n"
">>>                     reducer_output_types=['string', 'int'])\n"
"     word  cnt\n"
"0   hello    2\n"
"1       i    1\n"
"2      is    1\n"
"3    life    1\n"
"4  python    2\n"
"5   short    1\n"
"6     use    1\n"
"7   world    1"
msgstr ""
">>> def mapper(row):\n"
">>>     for word in row[0].split():\n"
">>>         yield word.lower(), 1\n"
">>>\n"
">>> def reducer(keys):\n"
">>>     # we use a list here instead of letting cnt = 0, or cnt in "
"function h will be treated as a local variable whose value cannot be used"
" outside the function\n"
">>>     cnt = [0]\n"
">>>     def h(row, done):  # done == True indicates that all rows with "
"current key are visited\n"
">>>         cnt[0] += row[1]\n"
">>>         if done:\n"
">>>             yield keys[0], cnt[0]\n"
">>>     return h\n"
">>>\n"
">>> words_df.map_reduce(mapper, reducer, group=['word', ],\n"
">>>                     mapper_output_names=['word', 'cnt'],\n"
">>>                     mapper_output_types=['string', 'int'],\n"
">>>                     reducer_output_names=['word', 'cnt'],\n"
">>>                     reducer_output_types=['string', 'int'])\n"
"     word  cnt\n"
"0   hello    2\n"
"1       i    1\n"
"2      is    1\n"
"3    life    1\n"
"4  python    2\n"
"5   short    1\n"
"6     use    1\n"
"7   world    1"

#: ../../source/df-sort-distinct-apply.rst:619
msgid "group参数用来指定reduce按哪些字段做分组，如果不指定，会按全部字段做分组。"
msgstr ""
"Use the group parameter to specify the fields that you want to group with"
" the reduce function. If you do not specify the fields, all fields are "
"grouped."

#: ../../source/df-sort-distinct-apply.rst:621
msgid ""
"其中对于reducer来说，会稍微有些不同。它需要接收聚合的keys初始化，并能继续"
"处理按这些keys聚合的每行数据。 第2个参数表示这些keys相关的所有行是不是都"
"迭代完成。"
msgstr ""
"The reducer process is different. The reducer receives aggregated initial"
" values of keys, and then processes each row aggregated by keys. The "
"second parameter done indicates whether all the rows related to these "
"keys have been completely iterated."

#: ../../source/df-sort-distinct-apply.rst:624
msgid "这里写成函数闭包的方式，主要为了方便，当然我们也能写成callable的类。"
msgstr "The function closure is convenient, and the callable is also allowed."

#: ../../source/df-sort-distinct-apply.rst:626
msgid ""
"class reducer(object):\n"
"    def __init__(self, keys):\n"
"        self.cnt = 0\n"
"\n"
"    def __call__(self, row, done):  # done表示这个key已经迭代结束\n"
"        self.cnt += row.cnt\n"
"        if done:\n"
"            yield row.word, self.cnt"
msgstr ""
"class reducer(object):\n"
"    def __init__(self, keys):\n"
"        self.cnt = 0\n"
"\n"
"    def __call__(self, row, done):  # # done == True indicates that all "
"rows with current key are visited\n"
"        self.cnt += row.cnt\n"
"        if done:\n"
"            yield row.word, self.cnt"

#: ../../source/df-sort-distinct-apply.rst:637
msgid "使用 ``output``\\ 来注释会让代码更简单些。"
msgstr "You can annotate with ``output``\\ to simplify the code."

#: ../../source/df-sort-distinct-apply.rst:639
msgid ""
">>> from odps.df import output\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def mapper(row):\n"
">>>     for word in row[0].split():\n"
">>>         yield word.lower(), 1\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def reducer(keys):\n"
">>>     # 这里使用 list 而不是 cnt = 0，否则 h 内的 cnt 会被认为是"
"局部变量，其中的赋值无法输出\n"
">>>     cnt = [0]\n"
">>>     def h(row, done):  # done表示这个key已经迭代结束\n"
">>>         cnt[0] += row.cnt\n"
">>>         if done:\n"
">>>             yield keys.word, cnt[0]\n"
">>>     return h\n"
">>>\n"
">>> words_df.map_reduce(mapper, reducer, group='word')\n"
"     word  cnt\n"
"0   hello    2\n"
"1       i    1\n"
"2      is    1\n"
"3    life    1\n"
"4  python    2\n"
"5   short    1\n"
"6     use    1\n"
"7   world    1"
msgstr ""
">>> from odps.df import output\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def mapper(row):\n"
">>>     for word in row[0].split():\n"
">>>         yield word.lower(), 1\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def reducer(keys):\n"
">>>     # we use a list here instead of letting cnt = 0, or cnt in "
"function h will be treated as a local variable whose value cannot be used"
" outside the function\n"
">>>     cnt = [0]\n"
">>>     def h(row, done):  # done == True indicates that all rows with "
"current key are visited\n"
">>>         cnt[0] += row.cnt\n"
">>>         if done:\n"
">>>             yield keys.word, cnt[0]\n"
">>>     return h\n"
">>>\n"
">>> words_df.map_reduce(mapper, reducer, group='word')\n"
"     word  cnt\n"
"0   hello    2\n"
"1       i    1\n"
"2      is    1\n"
"3    life    1\n"
"4  python    2\n"
"5   short    1\n"
"6     use    1\n"
"7   world    1"

#: ../../source/df-sort-distinct-apply.rst:669
msgid ""
"有时候我们在迭代的时候需要按某些列排序，则可以使用 ``sort``\\ 参数，来"
"指定按哪些列排序，升序降序则通过 ``ascending``\\ 参数指定。 ``ascending``"
" 参数可以是一个bool值，表示所有的 ``sort``\\ 字段是相同升序或降序， 也"
"可以是一个列表，长度必须和 ``sort``\\ 字段长度相同。"
msgstr ""
"Sometimes, when you need to sort the data by column during iteration, you"
" can use the ``sort``\\ parameter to specify columns for sorting, and set"
" the ``ascending``\\ parameter for ascending or descending. The "
"``ascending`` parameter can be a Boolean value to indicate that all the "
"``sort``\\ fields are in an ascending or descending order,  or it may be "
"a list, whose length must be the same as that of the ``sort``\\ fields."

#: ../../source/df-sort-distinct-apply.rst:675
msgid "指定combiner"
msgstr "Specifying the combiner"

#: ../../source/df-sort-distinct-apply.rst:677
msgid ""
"combiner表示在map_reduce API里表示在mapper端，就先对数据进行聚合操作，它"
"的用法和reducer是完全一致的，但不能引用资源。 并且，combiner的输出的"
"字段名和字段类型必须和mapper完全一致。"
msgstr ""
"If the combiner has been expressed in the map_reduce API, the mapper "
"aggregates data first. The reducer has the same usage but cannot "
"reference resources. The field name and type that the combiner outputs "
"must be consistent with the mapper."

#: ../../source/df-sort-distinct-apply.rst:680
msgid ""
"上面的例子，我们就可以使用reducer作为combiner来先在mapper端对数据做初步的"
"聚合，减少shuffle出去的数据量。"
msgstr ""
"In the preceding example, you can use the reducer as the combiner to "
"aggregate data in the mapper to reduce shuffled data."

#: ../../source/df-sort-distinct-apply.rst:682
msgid ">>> words_df.map_reduce(mapper, reducer, combiner=reducer, group='word')"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:704
msgid ""
"由于 PyODPS DataFrame 默认 Collection / Sequence 等对象均为分布式对象，故"
"不支持在自定义函数内部引用这些对象。 请考虑改用 :ref:`Join 等方法 <"
"dfmerge>` 引用多个 DataFrame 的数据，或者引用 Collection 作为资源。"
msgstr ""
"PyODPS DataFrame recognizes all collections and sequences as distributed "
"objects, and does not support referencing these objects inside user-"
"defined functions. Please consider using :ref:`methods like join "
"<dfmerge>` to reference data in multiple DataFrames, or referencing "
"collections as resources."

#: ../../source/df-sort-distinct-apply.rst:710
msgid "在MapReduce API里，我们能分别指定mapper和reducer所要引用的资源。"
msgstr ""
"You can respectively specify resources referenced by the mapper and the "
"reducer in MapReduce API."

#: ../../source/df-sort-distinct-apply.rst:712
msgid ""
"如下面的例子，我们对mapper里的单词做停词过滤，在reducer里对白名单的单词"
"数量加5。"
msgstr ""
"In the following example, you filter stop words in the mapper, and the "
"number of whitelisted words in the reducer is plus 5."

#: ../../source/df-sort-distinct-apply.rst:714
msgid ""
">>> white_list_file = o.create_resource('pyodps_white_list_words', "
"'file', file_obj='Python\\nWorld')\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def mapper(resources):\n"
">>>     stop_words = set(r[0].strip() for r in resources[0])\n"
">>>     def h(row):\n"
">>>         for word in row[0].split():\n"
">>>             if word not in stop_words:\n"
">>>                 yield word, 1\n"
">>>     return h\n"
">>>\n"
">>> @output(['word', 'cnt'], ['string', 'int'])\n"
">>> def reducer(resources):\n"
">>>     d = dict()\n"
">>>     d['white_list'] = set(word.strip() for word in resources[0])\n"
">>>     d['cnt'] = 0\n"
">>>     def inner(keys):\n"
">>>         d['cnt'] = 0\n"
">>>         def h(row, done):\n"
">>>             d['cnt'] += row.cnt\n"
">>>             if done:\n"
">>>                 if row.word in d['white_list']:\n"
">>>                     d['cnt'] += 5\n"
">>>                 yield keys.word, d['cnt']\n"
">>>         return h\n"
">>>     return inner\n"
">>>\n"
">>> words_df.map_reduce(mapper, reducer, group='word',\n"
">>>                     mapper_resources=[stop_words], "
"reducer_resources=[white_list_file])\n"
"     word  cnt\n"
"0   hello    2\n"
"1    life    1\n"
"2  python    7\n"
"3   world    6\n"
"4   short    1\n"
"5     use    1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:755
msgid "重排数据"
msgstr "Reshuffling data"

#: ../../source/df-sort-distinct-apply.rst:757
msgid ""
"有时候我们的数据在集群上分布可能是不均匀的，我们需要对数据重排。调用 ``"
"reshuffle`` 接口即可。"
msgstr ""
"If data is unevenly distributed in a cluster, you need to reshuffle the "
"data by using the ``reshuffle`` API."

#: ../../source/df-sort-distinct-apply.rst:760
msgid ">>> df1 = df.reshuffle()"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:765
msgid ""
"默认会按随机数做哈希来分布。也可以指定按那些列做分布，且可以指定重排后的"
"排序顺序。"
msgstr ""
"By default, data is hashed as random numbers. You can also distribute the"
" data by a specified column, and sort the reshuffled data in a specified "
"order."

#: ../../source/df-sort-distinct-apply.rst:768
msgid ">>> df1.reshuffle('name', sort='id', ascending=False)"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:774
msgid "布隆过滤器"
msgstr "Bloom filter"

#: ../../source/df-sort-distinct-apply.rst:777
msgid "PyODPS DataFrame提供了 ``bloom_filter`` 接口来进行布隆过滤器的计算。"
msgstr ""
"PyODPS DataFrame provides the ``bloom_filter`` API to calculate with a "
"Bloom filter."

#: ../../source/df-sort-distinct-apply.rst:779
msgid ""
"给定某个collection，和它的某个列计算的sequence1，我们能对另外一个"
"sequence2进行布隆过滤，sequence1不在sequence2中的一定会过滤， 但可能不能"
"完全过滤掉不存在于sequence2中的数据，这也是一种近似的方法。"
msgstr ""
"When you have specified a Collection object and its sequence1 for column "
"calculation, you can apply the Bloom filter to sequence2. Therefore, the "
"data in sequence1 instead of in sequence2 must be filtered out, but the "
"data that is not in sequence2 may not be filtered out completely. This is"
" an approximate method."

#: ../../source/df-sort-distinct-apply.rst:782
msgid "这样的好处是能快速对collection进行快速过滤一些无用数据。"
msgstr ""
"This method can quickly filter out some useless data from the Collection "
"object."

#: ../../source/df-sort-distinct-apply.rst:784
msgid ""
"这在大规模join的时候，一边数据量远大过另一边数据，而大部分并不会join上的"
"场景很有用。 比如，我们在join用户的浏览数据和交易数据时，用户的浏览大部分"
"不会带来交易，我们可以利用交易数据先对浏览数据进行布隆过滤， 然后再join能"
"很好提升性能。"
msgstr ""
"The Bloom filter is particularly suitable for the large-scale join "
"operation used between large volumes of data and small amounts of data. "
"Most data is not joined in this scenario. For example, most user visits "
"do not create transactions during the join operation between visit data "
"and transaction data. Therefore, you can apply the Bloom filter to visit "
"data before joining visit data with transaction data to enhance operation"
" performance."

#: ../../source/df-sort-distinct-apply.rst:788
msgid ""
">>> df1 = DataFrame(pd.DataFrame({'a': ['name1', 'name2', 'name3', "
"'name1'], 'b': [1, 2, 3, 4]}))\n"
">>> df1\n"
"       a  b\n"
"0  name1  1\n"
"1  name2  2\n"
"2  name3  3\n"
"3  name1  4\n"
">>> df2 = DataFrame(pd.DataFrame({'a': ['name1']}))\n"
">>> df2\n"
"       a\n"
"0  name1\n"
">>> df1.bloom_filter('a', df2.a) # 这里第0个参数可以是个计算表达式如: df1"
".a + '1'\n"
"       a  b\n"
"0  name1  1\n"
"1  name1  4"
msgstr ""
">>> df1 = DataFrame(pd.DataFrame({'a': ['name1', 'name2', 'name3', "
"'name1'], 'b': [1, 2, 3, 4]}))\n"
">>> df1\n"
"       a  b\n"
"0  name1  1\n"
"1  name2  2\n"
"2  name3  3\n"
"3  name1  4\n"
">>> df2 = DataFrame(pd.DataFrame({'a': ['name1']}))\n"
">>> df2\n"
"       a\n"
"0  name1\n"
">>> df1.bloom_filter('a', df2.a) # the 0th argument can also be an "
"expression, for instance, df1.a + '1'\n"
"       a  b\n"
"0  name1  1\n"
"1  name1  4"

#: ../../source/df-sort-distinct-apply.rst:806
msgid ""
"这里由于数据量很小，df1中的a为name2和name3的行都被正确过滤掉了，当数据量"
"很大的时候，可能会有一定的数据不能被过滤。"
msgstr ""
"The preceding code processes a small volume of data. Therefore, the rows "
"that contain name2 and name3 in column a of df1 are filtered out. "
"However, when processing large volumes of data, the system may not filter"
" out some data that meets the specified condition."

#: ../../source/df-sort-distinct-apply.rst:808
msgid ""
"如之前提的join场景中，少量不能过滤并不能并不会影响正确性，但能较大提升"
"join的性能。"
msgstr ""
"As shown in the preceding join operation, some data that cannot be "
"filtered out does not affect the program correctness, but can enhance the"
" operation performance."

#: ../../source/df-sort-distinct-apply.rst:810
msgid ""
"我们可以传入 ``capacity`` 和 ``error_rate`` 来设置数据的量以及错误率，"
"默认值是 ``3000`` 和 ``0.01``。"
msgstr ""
"You can use ``capacity`` and ``error_rate`` to specify the data capacity "
"and error rate, ``3000`` and ``0.01`` by default."

#: ../../source/df-sort-distinct-apply.rst:813
msgid ""
"要注意，调大 ``capacity`` 或者减小 ``error_rate`` 会增加内存的使用，所以"
"应当根据实际情况选择一个合理的值。"
msgstr ""
"Tuning the ``capacity`` value up or the ``error_rate`` value down "
"increases the usage of memory. Therefore, you need to select a proper "
"value as needed."

#: ../../source/df-sort-distinct-apply.rst:819
msgid "透视表（pivot_table）"
msgstr "Pivot table"

#: ../../source/df-sort-distinct-apply.rst:821
msgid "PyODPS DataFrame提供透视表的功能。我们通过几个例子来看使用。"
msgstr ""
"PyODPS DataFrame provides the pivot_table feature. You can use this "
"feature as shown in the following example."

#: ../../source/df-sort-distinct-apply.rst:824
msgid ""
">>> df\n"
"     A    B      C  D  E\n"
"0  foo  one  small  1  3\n"
"1  foo  one  large  2  4\n"
"2  foo  one  large  2  5\n"
"3  foo  two  small  3  6\n"
"4  foo  two  small  3  4\n"
"5  bar  one  large  4  5\n"
"6  bar  one  small  5  3\n"
"7  bar  two  small  6  2\n"
"8  bar  two  large  7  1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:839
msgid ""
"最简单的透视表必须提供一个 ``rows`` 参数，表示按一个或者多个字段做取"
"平均值的操作。"
msgstr ""
"The simplest pivot_table must provide the ``rows`` parameter to retrieve "
"the mean from one or more fields."

#: ../../source/df-sort-distinct-apply.rst:841
msgid ""
">>> df['A', 'D', 'E'].pivot_table(rows='A')\n"
"     A  D_mean  E_mean\n"
"0  bar     5.5    2.75\n"
"1  foo     2.2    4.40"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:848
msgid "rows可以提供多个，表示按多个字段做聚合。"
msgstr "You can specify multiple rows to aggregate multiple fields."

#: ../../source/df-sort-distinct-apply.rst:850
msgid ""
">>> df.pivot_table(rows=['A', 'B', 'C'])\n"
"     A    B      C  D_mean  E_mean\n"
"0  bar  one  large     4.0     5.0\n"
"1  bar  one  small     5.0     3.0\n"
"2  bar  two  large     7.0     1.0\n"
"3  bar  two  small     6.0     2.0\n"
"4  foo  one  large     2.0     4.5\n"
"5  foo  one  small     1.0     3.0\n"
"6  foo  two  small     3.0     5.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:862
msgid "我们可以指定 ``values`` 来显示指定要计算的列。"
msgstr ""
"Specify the ``values`` parameter to display the column that you want to "
"calculate."

#: ../../source/df-sort-distinct-apply.rst:864
msgid ""
">>> df.pivot_table(rows=['A', 'B'], values='D')\n"
"     A    B    D_mean\n"
"0  bar  one  4.500000\n"
"1  bar  two  6.500000\n"
"2  foo  one  1.666667\n"
"3  foo  two  3.000000"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:873
msgid "计算值列时，默认会计算平均值，用户可以指定一个或者多个聚合函数。"
msgstr ""
"By default, the system calculates the mean of the column specified in "
"values. You can specify one or more aggregate functions."

#: ../../source/df-sort-distinct-apply.rst:875
msgid ""
">>> df.pivot_table(rows=['A', 'B'], values=['D'], aggfunc=['mean', "
"'count', 'sum'])\n"
"     A    B    D_mean  D_count  D_sum\n"
"0  bar  one  4.500000        2      9\n"
"1  bar  two  6.500000        2     13\n"
"2  foo  one  1.666667        3      5\n"
"3  foo  two  3.000000        2      6"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:884
msgid ""
"我们也可以把原始数据的某一列的值，作为新的collection的列。 **这也是透视表"
"最强大的地方。**"
msgstr ""
"You can also use the values of a column in original data as a new "
"Collection column. **This is the most powerful benefit of pivot_table.**"

#: ../../source/df-sort-distinct-apply.rst:886
msgid ""
">>> df.pivot_table(rows=['A', 'B'], values='D', columns='C')\n"
"     A    B  large_D_mean  small_D_mean\n"
"0  bar  one           4.0           5.0\n"
"1  bar  two           7.0           6.0\n"
"2  foo  one           2.0           1.0\n"
"3  foo  two           NaN           3.0"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:895
msgid "我们可以提供 ``fill_value`` 来填充空值。"
msgstr "The system provides ``fill_value`` to replace null values."

#: ../../source/df-sort-distinct-apply.rst:897
msgid ""
">>> df.pivot_table(rows=['A', 'B'], values='D', columns='C', "
"fill_value=0)\n"
"     A    B  large_D_mean  small_D_mean\n"
"0  bar  one             4             5\n"
"1  bar  two             7             6\n"
"2  foo  one             2             1\n"
"3  foo  two             0             3"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:908
msgid "Key-Value 字符串转换"
msgstr "Key-value string transformation"

#: ../../source/df-sort-distinct-apply.rst:910
msgid ""
"DataFrame 提供了将 Key-Value 对展开为列，以及将普通列转换为 Key-Value 列"
"的功能。"
msgstr ""
"DataFrame can extract key-value pairs into a column, and transform a "
"common column to a key-value column."

#: ../../source/df-sort-distinct-apply.rst:912
msgid "我们的数据为"
msgstr "The following code is the example for transformation:"

#: ../../source/df-sort-distinct-apply.rst:914
msgid ""
">>> df\n"
"    name               kv\n"
"0  name1  k1=1,k2=3,k5=10\n"
"1  name1    k1=7.1,k7=8.2\n"
"2  name2    k2=1.2,k3=1.5\n"
"3  name2      k9=1.1,k2=1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:923
msgid "可以通过 extract_kv 方法将 Key-Value 字段展开："
msgstr "Use the extract_kv method to extract key-value fields:"

#: ../../source/df-sort-distinct-apply.rst:925
msgid ""
">>> df.extract_kv(columns=['kv'], kv_delim='=', item_delim=',')\n"
"   name   kv_k1  kv_k2  kv_k3  kv_k5  kv_k7  kv_k9\n"
"0  name1    1.0    3.0    NaN   10.0    NaN    NaN\n"
"1  name1    7.0    NaN    NaN    NaN    8.2    NaN\n"
"2  name2    NaN    1.2    1.5    NaN    NaN    NaN\n"
"3  name2    NaN    1.0    NaN    NaN    NaN    1.1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:934
msgid ""
"其中，需要展开的字段名由 columns 指定，Key 和 Value 之间的分隔符，以及 "
"Key-Value 对之间的分隔符分别由 kv_delim 和 item_delim 这两个参数指定，"
"默认分别为半角冒号和半角逗号。输出的字段名为原字段名和 Key 值的组合，通过"
"“_”相连。缺失值默认为 None，可通过 ``fill_value`` 选择需要填充的值。"
"例如，相同的 df，"
msgstr ""
"In this code, use columns to specify the field name that you want to "
"extract. The separators for Key, Value, and key-value pairs are specified"
" by the following two parameters, kv_delim and item_delim. The default "
"separators are colons and commas. The output field name is the "
"combination of the original field name and the Key value connected by "
"underscores (_). For the default None value, you can use ``fill_value`` "
"to specify the value to replace None. The following example is the "
"processing result of the preceding df data."

#: ../../source/df-sort-distinct-apply.rst:938
msgid ""
">>> df.extract_kv(columns=['kv'], kv_delim='=', fill_value=0)\n"
"   name   kv_k1  kv_k2  kv_k3  kv_k5  kv_k7  kv_k9\n"
"0  name1    1.0    3.0    0.0   10.0    0.0    0.0\n"
"1  name1    7.0    0.0    0.0    0.0    8.2    0.0\n"
"2  name2    0.0    1.2    1.5    0.0    0.0    0.0\n"
"3  name2    0.0    1.0    0.0    0.0    0.0    1.1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:947
msgid ""
"extract_kv 默认输出类型为 ``float``\\ 。如果需要输出其他类型，可以指定 ``"
"dtype`` 参数，例如"
msgstr ""
"The default output type of ``extract_kv`` is ``float``\\ . If you need to"
" output with other data types, please specify ``dtype`` argument. For "
"instance,"

#: ../../source/df-sort-distinct-apply.rst:949
msgid "df.extract_kv(columns=['kv'], kv_delim='=', fill_value=0, dtype='string')"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:953
msgid "DataFrame 也支持将多列数据转换为一个 Key-Value 列。例如，"
msgstr ""
"DataFrame can also transform multiple columns to one key-value column. "
"The following code is an example for transformation:"

#: ../../source/df-sort-distinct-apply.rst:955
msgid ""
">>> df\n"
"   name    k1   k2   k3    k5   k7   k9\n"
"0  name1  1.0  3.0  NaN  10.0  NaN  NaN\n"
"1  name1  7.0  NaN  NaN   NaN  8.2  NaN\n"
"2  name2  NaN  1.2  1.5   NaN  NaN  NaN\n"
"3  name2  NaN  1.0  NaN   NaN  NaN  1.1"
msgstr ""

#: ../../source/df-sort-distinct-apply.rst:964
msgid "可通过 to_kv 方法转换为 Key-Value 表示的格式："
msgstr "You can use the to_kv method to transform data to the key-value format:"

#: ../../source/df-sort-distinct-apply.rst:966
msgid ""
">>> df.to_kv(columns=['k1', 'k2', 'k3', 'k5', 'k7', 'k9'], kv_delim='=')\n"
"    name               kv\n"
"0  name1  k1=1,k2=3,k5=10\n"
"1  name1    k1=7.1,k7=8.2\n"
"2  name2    k2=1.2,k3=1.5\n"
"3  name2      k9=1.1,k2=1"
msgstr ""

