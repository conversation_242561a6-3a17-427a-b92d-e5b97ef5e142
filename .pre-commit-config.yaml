minimum_pre_commit_version: 2.15.0
# we use *| at the beginning of lines to make sure patterns can be written
#  in multiple lines with added spaces ignored
exclude: "^benchmarks/.*
*|^bin/.*
*|^cupid/.*
*|^docs/.*
*|^misc/.*
*|^odps/mars_extension/.*
*|^odps/df/.*
*|^odps/lib/.*
*|^odps/ml/.*
*|^odps/static/.*"
# reserve "manual" for relatively slow hooks which we still want to run in CI
default_stages: [
  commit,
  merge-commit,
  push,
  prepare-commit-msg,
  commit-msg,
  post-checkout,
  post-commit,
  post-merge,
  post-rewrite
]
ci:
    autofix_prs: false
repos:
- repo: local
  hooks:
  - id: check-yaml
    name: check-yaml
    entry: check-yaml
    language: python
    exclude: ^operators/
    types_or: [yaml]
    additional_dependencies: [pre-commit-hooks==4.4.0]
  - id: end-of-file-fixer
    name: end-of-file-fixer
    entry: end-of-file-fixer
    language: python
    additional_dependencies: [pre-commit-hooks==4.4.0]
  - id: trailing-whitespace
    name: trailing-whitespace
    entry: trailing-whitespace-fixer
    language: python
    additional_dependencies: [pre-commit-hooks==4.4.0]
  - id: check-illegal-windows-names
    name: check illegal windows names
    entry: Illegal windows filenames detected
    language: fail
    files: '(?i)((^|/)(\\|CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|/|$)|:)'
- repo: local
  hooks:
  # Check that the copyright is configured
  - id: copyright-check
    name: copyright-check
    entry: python bin/copyright.py --required
    language: python
    files: 'odps/.*'
    exclude: '^odps/lib/.*|^odps/tunnel/pb/.*\.py$'
    types_or: [python, pyi, cython]
    additional_dependencies: [pyyaml]
- repo: local
  hooks:
  # Replace quote replacer for black (which only supports Python >= 3.4)
  - id: double-quote-string-fixer
    name: double-quote-string-fixer
    entry: python bin/string_fixer.py
    language: python
    files: 'odps/.*'
    exclude: '^odps/lib/.*'
    types_or: [python, pyi, cython]
    args: [--replace-single]
- repo: local
  hooks:
  - id: flake8
    name: flake8
    description: "Flake8"
    entry: flake8
    language: python
    require_serial: true
    types_or: [python, pyi]
    additional_dependencies: [flake8==5.0.4]
- repo: local
  hooks:
  # NOTE: we make `black` a local hook because if it's installed from
  # PyPI (rather than from source) then it'll run twice as fast thanks to mypyc
  - id: black
    name: black
    description: "Black"
    entry: black
    language: python
    require_serial: true
    types_or: [python, pyi]
    additional_dependencies: [black==23.3.0]
- repo: local
  hooks:
    - id: isort
      name: isort
      description: "isort"
      entry: isort
      language: python
      require_serial: true
      types_or: [python, pyi]
      args: [--profile, black]
      additional_dependencies: [isort==5.11.0]
