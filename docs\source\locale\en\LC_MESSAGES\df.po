# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-01 11:04+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/df.rst:5
msgid "DataFrame"
msgstr "DataFrame"

#: ../../source/df.rst:8
msgid ""
"PyODPS 提供了 DataFrame API，它提供了类似 pandas 的接口，但是能充分利用 "
"ODPS 的计算能力； 同时能在本地使用同样的接口，用 pandas 进行计算。"
msgstr ""
"PyODPS provides a pandas-like interface, PyODPS DataFrame, which operates"
" on MaxCompute tables and can make full use of MaxCompute's computing "
"power. You can also change the data source from MaxCompute tables to "
"pandas DataFrame, so that the same code can be executed on pandas."

#: ../../source/df.rst:13
msgid ""
"PyODPS DataFrame 未来将停止维护。对于新项目，建议使用 `MaxFrame <https://"
"maxframe.readthedocs.io/en/latest/index.html>`_\\ 。"
msgstr ""
"**Maintenance of PyODPS DataFrame is discontinued. Please do not use this"
" feature in new projects.**"

#: ../../source/df.rst:16
msgid ""
"PyODPS DataFrame 尽管看起来和 pandas 形似，但并不是 pandas。pandas 的功能"
"，例如完整的 Series 支持、Index 支持、按行读取数据、多 DataFrame 按 iloc "
"横向合并等，PyODPS DataFrame 并不支持。因而使用前请参考文档确定你的写法"
"是否被支持。"
msgstr ""
"Though PyODPS DataFrame looks like pandas DataFrame, it is not pandas "
"though. Features of pandas, such as full supports for Series and Index, "
"reading data by rows, concatenation of multiple DataFrame by columns are "
"not supported in PyODPS DataFrame. Therefore please check this document "
"before code to make sure methods are supported."

