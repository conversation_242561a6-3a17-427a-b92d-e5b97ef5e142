# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-25 16:07+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-merge.rst:4
msgid "数据合并"
msgstr "Data merging"

#: ../../source/df-merge.rst:6
msgid "from odps.df import DataFrame"
msgstr ""

#: ../../source/df-merge.rst:10
msgid ""
"movies = DataFrame(o.get_table('pyodps_ml_100k_movies'))\n"
"ratings = DataFrame(o.get_table('pyodps_ml_100k_ratings'))"
msgstr ""

#: ../../source/df-merge.rst:15
msgid "movies.dtypes"
msgstr ""

#: ../../source/df-merge.rst:21
msgid ""
"odps.Schema {\n"
"  movie_id                            int64\n"
"  title                               string\n"
"  release_date                        string\n"
"  video_release_date                  string\n"
"  imdb_url                            string\n"
"}"
msgstr ""

#: ../../source/df-merge.rst:31
msgid "ratings.dtypes"
msgstr ""

#: ../../source/df-merge.rst:40
msgid ""
"odps.Schema {\n"
"  user_id                     int64\n"
"  movie_id                    int64\n"
"  rating                      int64\n"
"  unix_timestamp              int64\n"
"}"
msgstr ""

#: ../../source/df-merge.rst:50
msgid "Join 操作"
msgstr "Join operation"

#: ../../source/df-merge.rst:52
msgid ""
"DataFrame 也支持对两个 Collection 执行 join 的操作，如果不指定 join 的"
"条件，那么 DataFrame API会寻找名字相同的列，并作为 join 的条件。"
msgstr ""
"DataFrame supports the join operation for two Collection objects. If you "
"do not specify the join conditions, the DataFrame application program "
"interface (API) uses columns of the same name to join them."

#: ../../source/df-merge.rst:55
msgid ""
">>> movies.join(ratings).head(3)\n"
"   movie_id              title  release_date  video_release_date"
"                                           imdb_url  user_id  rating  "
"unix_timestamp\n"
"0         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...       49       3       "
"888068877\n"
"1         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      621       5       "
"881444887\n"
"2         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      291       3       "
"874833936"
msgstr ""

#: ../../source/df-merge.rst:63
msgid "我们也可以显式指定join的条件。有以下几种方式："
msgstr "You can also explicitly specify the join conditions in the following ways:"

#: ../../source/df-merge.rst:65
msgid ""
">>> movies.join(ratings, on='movie_id').head(3)\n"
"   movie_id              title  release_date  video_release_date"
"                                           imdb_url  user_id  rating  "
"unix_timestamp\n"
"0         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...       49       3       "
"888068877\n"
"1         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      621       5       "
"881444887\n"
"2         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      291       3       "
"874833936"
msgstr ""

#: ../../source/df-merge.rst:73
msgid ""
"在join时，on条件两边的字段名称相同时，只会选择一个，其他类型的join则会被"
"重命名。"
msgstr ""
"During the join operation, if the field name in the on condition for both"
" Collection objects is the same, the system selects one field name. In "
"other types of join operations such as left_join, right_join, and "
"outer_join, one of the field names in the on condition is renamed."

#: ../../source/df-merge.rst:76
msgid ""
">>> movies.left_join(ratings, on='movie_id').head(3)\n"
"   movie_id_x              title  release_date  video_release_date"
"                                           imdb_url  user_id  movie_id_y"
"  rating  unix_timestamp\n"
"0           3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...       49           3"
"       3       888068877\n"
"1           3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      621           3"
"       5       881444887\n"
"2           3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      291           3"
"       3       874833936"
msgstr ""

#: ../../source/df-merge.rst:84
msgid ""
"可以看到，\\ ``movie_id``\\ 被重命名为movie\\_id\\_x，以及movie\\_id\\_y"
"，这和\\ ``suffixes``\\ 参数有关（默认是\\ ``('_x', '_y')``\\ ）， 当遇到"
"重名的列时，就会被重命名为指定的后缀。"
msgstr ""
"In this code, \\ ``movie_id``\\ is renamed as movie\\_id\\_x and "
"movie\\_id\\_y. This renaming rule depends on the \\ ``suffixes``\\ "
"parameter (\\ ``('_x', '_y')``\\ as the default). The fields of the same "
"name are renamed using the specified suffix."

#: ../../source/df-merge.rst:87
msgid ""
">>> ratings2 = ratings[ratings.exclude('movie_id'), "
"ratings.movie_id.rename('movie_id2')]\n"
">>> ratings2.dtypes\n"
"odps.Schema {\n"
"  user_id                     int64\n"
"  rating                      int64\n"
"  unix_timestamp              int64\n"
"  movie_id2                   int64\n"
"}\n"
">>> movies.join(ratings2, on=[('movie_id', 'movie_id2')]).head(3)\n"
"   movie_id              title  release_date  video_release_date"
"                                           imdb_url  user_id  rating  "
"unix_timestamp  movie_id2\n"
"0         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...       49       3       "
"888068877          3\n"
"1         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      621       5       "
"881444887          3\n"
"2         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      291       3       "
"874833936          3"
msgstr ""

#: ../../source/df-merge.rst:103
msgid "也可以直接写等于表达式。"
msgstr "You can also write the equality operators to indicate renaming."

#: ../../source/df-merge.rst:105
msgid ""
">>> movies.join(ratings2, on=[movies.movie_id == "
"ratings2.movie_id2]).head(3)\n"
"   movie_id              title  release_date  video_release_date"
"                                           imdb_url  user_id  rating  "
"unix_timestamp  movie_id2\n"
"0         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...       49       3       "
"888068877          3\n"
"1         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      621       5       "
"881444887          3\n"
"2         3  Four Rooms (1995)   01-Jan-1995                      "
"http://us.imdb.com/M/title-exact?Four%20Rooms%...      291       3       "
"874833936          3"
msgstr ""

#: ../../source/df-merge.rst:113
msgid "self-join的时候，可以调用\\ ``view``\\ 方法，这样就可以分别取字段。"
msgstr ""
"During the self-join operation, you can use the \\ ``view``\\ method to "
"retrieve fields respectively from the left and right DataFrame objects."

#: ../../source/df-merge.rst:115
msgid ""
">>> movies2 = movies.view()\n"
">>> movies.join(movies2, movies.movie_id == movies2.movie_id)[movies, "
"movies2.movie_id.rename('movie_id2')].head(3)\n"
"   movie_id            title_x release_date_x video_release_date_x  \\\n"
"0         2   GoldenEye (1995)    01-Jan-1995                 True\n"
"1         3  Four Rooms (1995)    01-Jan-1995                 True\n"
"2         4  Get Shorty (1995)    01-Jan-1995                 True\n"
"\n"
"                                          imdb_url_x  movie_id2\n"
"0  http://us.imdb.com/M/title-exact?GoldenEye%20(...          2\n"
"1  http://us.imdb.com/M/title-exact?Four%20Rooms%...          3\n"
"2  http://us.imdb.com/M/title-exact?Get%20Shorty%...          4"
msgstr ""

#: ../../source/df-merge.rst:129
msgid ""
"除了\\ ``join``\\ 以外，DataFrame还支持\\ ``left_join``\\ ，\\ ``right_"
"join``\\ ，和\\ ``outer_join``\\ 。在执行上述外连接操作时， 默认会将重"
"名列加上 _x 和 _y 后缀，可通过在 suffixes 参数中传入一个二元 tuple 来自"
"定义后缀。"
msgstr ""
"In addition to \\ ``join``\\, DataFrame supports \\ ``left_join``\\ , \\ "
"``right_join``\\ , and \\ ``outer_join``\\ . In these join operations, "
"renamed columns are suffixed with _x and _y by default. You can use a 2‑"
"tuple to define the suffix in the suffixes parameter."

#: ../../source/df-merge.rst:132
msgid ""
"如果需要在外连接中避免对谓词中相等的列取重复列，可以指定 merge_columns "
"选项，该选项会自动选择两列中的非空值作为新列的值："
msgstr ""
"During the outer_join operation, to avoid repeated columns of the same "
"equal predicate, set the merge_columns option to True. Therefore, the "
"system selects non-null values from these columns as the values in a new "
"column:"

#: ../../source/df-merge.rst:134
msgid ">>> movies.left_join(ratings, on='movie_id', merge_columns=True)"
msgstr ""

#: ../../source/df-merge.rst:138
msgid ""
"要使用 **mapjoin**\\ 需要将 mapjoin 设为 True ，执行时会对右表做 mapjoin "
"操作，例如"
msgstr ""
"To use the **mapjoin** operation, set mapjoin to True. Therefore, the "
"system executes the mapjoin operation for the right DataFrame object."

#: ../../source/df-merge.rst:140
msgid ">>> movies.left_join(ratings, on='movie_id', mapjoin=True)"
msgstr ""

#: ../../source/df-merge.rst:144
msgid ""
"要使用 **skewjoin**\\ 需要将 skewjoin 设为 True ，执行时会对右表做 "
"skewjoin 操作，例如"
msgstr ""
"To use the **skewjoin** operation, set skewjoin to True. Therefore, the "
"system executes the skewjoin operation for the right DataFrame object."

#: ../../source/df-merge.rst:146
msgid ">>> movies.left_join(ratings, on='movie_id', skewjoin=True)"
msgstr ""

#: ../../source/df-merge.rst:150
msgid ""
"如果需要指定特定列有偏斜，可以将 skewjoin 设为对应列，如果有多列应指定为 "
"list，例如："
msgstr ""
"To specify skewness on specific columns, you may set skewjoin with a list"
" of names of these columns."

#: ../../source/df-merge.rst:152
msgid ""
">>> movies.left_join(ratings, on=['user_id', 'movie_id'], "
"skewjoin=['user_id', 'movie_id'])"
msgstr ""

#: ../../source/df-merge.rst:156
msgid ""
"如果已知列上的某些值（或者组合）有偏斜，可以将 skewjoin 设为列上对应值"
"组成的 dict，如有多个值应当指定为这些 dict 组成的 list，例如"
msgstr ""
"When you are aware of the skew values, you may specify combination of "
"these values by passing a list of dict of values to skewjoin."

#: ../../source/df-merge.rst:158
msgid ""
">>> movies.left_join(\n"
"    ratings, on=['user_id', 'movie_id'],\n"
"    skewjoin=[{'user_id': 0, 'movie_id': 0}, {'user_id': 1, 'movie_id': "
"1}],\n"
")"
msgstr ""

#: ../../source/df-merge.rst:165
msgid ""
"MaxCompute 推荐为偏斜列指定具体的值，如不指定会带来额外的统计开销。在指定"
"值时，需要保证列每组数据的列都相同。"
msgstr ""
"It is recommended by MaxCompute to set specific values for skew columns, "
"or extra computation cost for column statistics might be introduced. When"
" specifying skew values, make sure all columns are included for every "
"pair of values."

#: ../../source/df-merge.rst:167
msgid ""
"用户也能join分别来自ODPS和pandas的Collection，或者join分别来自ODPS和"
"数据库的Collection，此时计算会在ODPS上执行。"
msgstr ""
"You can also join Collection objects respectively from MaxCompute and "
"pandas, or join those respectively from MaxCompute and a database. "
"MaxCompute rather than pandas or the database executes the computation."

#: ../../source/df-merge.rst:170
msgid "Union操作"
msgstr "Union operation"

#: ../../source/df-merge.rst:172
msgid ""
"现在有两张表，字段和类型都一致（可以顺序不同），我们可以使用union或者"
"concat来把它们合并成一张表。"
msgstr ""
"For two tables of consistent fields and types, which are automatically "
"ordered, you can use the union or concat operation to combine both tables"
" into a single table. "

#: ../../source/df-merge.rst:174
msgid ""
">>> mov1 = movies[movies.movie_id < 3]['movie_id', 'title']\n"
">>> mov2 = movies[(movies.movie_id > 3) & (movies.movie_id < 6)]['title',"
" 'movie_id']\n"
">>> mov1.union(mov2)\n"
"   movie_id              title\n"
"0         1   Toy Story (1995)\n"
"1         2   GoldenEye (1995)\n"
"2         4  Get Shorty (1995)\n"
"3         5     Copycat (1995)"
msgstr ""

#: ../../source/df-merge.rst:185
msgid ""
"用户也能union分别来自ODPS和pandas的Collection，或者union分别来自ODPS和"
"数据库的Collection，此时计算会在ODPS上执行。"
msgstr ""
"You can execute the union operation for Collection objects respectively "
"from MaxCompute and pandas, or for those respectively from MaxCompute and"
" a database. MaxCompute rather than pandas or the database executes the "
"computation."

