---
description: Repository Information Overview
alwaysApply: true
---

# PyODPS Information

## Summary
PyODPS is the Python SDK for Alibaba Cloud ODPS (Open Data Processing Service), providing an elegant way to access ODPS API. It offers data analysis capabilities, SQL execution, data upload/download, and resource management functionalities for ODPS/MaxCompute.

## Structure
- **odps/**: Main package containing core functionality, models, and utilities
- **odps_scripts/**: Command-line tools for PyODPS
- **examples/**: Example code demonstrating usage patterns
- **docs/**: Documentation source files
- **tests/**: Test suite for the package
- **benchmarks/**: Performance benchmarking code

## Language & Runtime
**Language**: Python
**Version**: Python 2.7+ and Python 3.4+ (Python 3.7+ recommended)
**Build System**: setuptools
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- requests>=2.4.0
- pyarrow>=0.16.0 (Python 2) or pyarrow>=2.0.0 (Python 3)

**Optional Dependencies**:
- jupyter>=1.0.0
- ipython>=4.0.0
- numpy>=1.6.0
- pandas>=0.17.0
- matplotlib>=1.4
- pymars>=0.5.4 (for Mars integration)
- protobuf>=3.6,<4.0 (for Mars integration)

## Build & Installation
```bash
# Basic installation
pip install pyodps

# Full installation with all dependencies
pip install pyodps[full]

# Development installation
git clone https://github.com/aliyun/aliyun-odps-python-sdk.git
cd aliyun-odps-python-sdk
pip install -r requirements.txt -e .
```

## Testing
**Framework**: pytest
**Test Location**: odps/tests/
**Configuration**: Copy conf/test.conf.template to odps/tests/test.conf and configure with account details
**Run Command**:
```bash
pytest odps
```

## Command-line Tools
**PyOU**: Python UDF debugging tool
```bash
pyou <udf_class> < input_file
```

**PyODPS-Pack**: Packaging utility for PyODPS
```bash
pyodps-pack
```

## Integration Points
- **SQLAlchemy**: Provides SQLAlchemy dialect for ODPS/MaxCompute
- **Superset**: Includes integration with Apache Superset
- **Jupyter/IPython**: Offers enhanced notebook experience with magic commands
- **Mars**: Integration with Mars distributed computing framework