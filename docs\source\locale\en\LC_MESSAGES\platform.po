# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-04-26 11:34+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/platform.rst:5
msgid "工具平台使用指南"
msgstr "Platform instructions"

#: ../../source/platform.rst:7
msgid ""
"PyODPS 可在 DataWorks 等数据开发平台中作为节点调用。这些平台提供了 PyODPS"
" 运行环境，**不需要** 手动创建 ODPS 入口对象，免除了手动配置的麻烦，而且"
"还提供了调度执行的能力。对于想从平台迁移到自行部署 PyODPS 环境的用户，"
"下面也提供了迁移注意事项。"
msgstr ""
"Python on MaxCompute (PyODPS) can be used as a node on data development "
"platforms such as DataWorks. These platforms provide the PyODPS running "
"environment and allow scheduling and execution. Therefore, you **do not "
"need** to manually create the ODPS object. To migrate from these "
"platforms to local PyODPS, see the following migration instructions:"

