# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-24 16:57+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/faq.rst:5
msgid "常见问题"
msgstr "Frequently asked questions"

#: ../../source/faq.rst:8
msgid "如何查看当前使用的 PyODPS 版本"
msgstr "How to look for the version of PyODPS you are using"

#: ../../source/faq.rst:9
msgid ""
"import odps\n"
"print(odps.__version__)"
msgstr ""

#: ../../source/faq.rst:-1
msgid "怎么配置 SQL / DataFrame 的执行选项？"
msgstr "How to configure execution options in SQL or DataFrame"

#: ../../source/faq.rst:20
msgid ""
"ODPS SQL 的执行选项可在 `这里 <https://help.aliyun.com/apsara/enterprise/"
"v_3_12_0_20200630/odps/enterprise-ascm-user-guide/common-maxcompute-sql-"
"parameter-settings.html>`_ 找到。设置时，可将该选项设置到 ``options.sql."
"settings``，即"
msgstr ""
"You can find a list of options for MaxCompute SQL `here "
"<https://help.aliyun.com/apsara/enterprise/v_3_12_0_20200630/odps"
"/enterprise-ascm-user-guide/common-maxcompute-sql-parameter-"
"settings.html>`_ .These settings can be configured at "
"``options.sql.settings``. For instance,"

#: ../../source/faq.rst:22
msgid ""
"from odps import options\n"
"# 将 <option_name> 和 <option_value> 替换为选项名和选项值\n"
"options.sql.settings = {'<option_name>': '<option_value>'}"
msgstr ""
"from odps import options\n"
"# replace <option_name> and <option_value> with true option names and "
"values\n"
"options.sql.settings = {'<option_name>': '<option_value>'}"

#: ../../source/faq.rst:28
msgid "也可在每次调用执行时即席配置，该配置中的配置项会覆盖全局配置。"
msgstr ""
"You may also configure these options at every execution, which will "
"override the global ones."

#: ../../source/faq.rst:30
msgid "当使用 ``odps.execute_sql`` 时，可以使用"
msgstr ""
"When you are using ``odps.execute_sql``, you can configure these options "
"via"

#: ../../source/faq.rst:32
msgid ""
"from odps import options\n"
"# 将 <option_name> 和 <option_value> 替换为选项名和选项值\n"
"o.execute_sql('<sql_statement>', hints={'<option_name>': "
"'<option_value>'})"
msgstr ""
"from odps import options\n"
"# replace <option_name> and <option_value> with true option names and "
"values\n"
"o.execute_sql('<sql_statement>', hints={'<option_name>': "
"'<option_value>'})"

#: ../../source/faq.rst:38
msgid "当使用 ``dataframe.execute`` 或 ``dataframe.persist`` 时，可以使用"
msgstr ""
"When using ``DataFrame.execute`` or ``DataFrame.persist``, you can "
"configure these options via"

#: ../../source/faq.rst:40
msgid ""
"from odps import options\n"
"# 将 <option_name> 和 <option_value> 替换为选项名和选项值\n"
"df.persist('<table_name>', hints={'<option_name>': '<option_value>'})"
msgstr ""
"from odps import options\n"
"# replace <option_name> and <option_value> with true option names and "
"values\n"
"df.persist('<table_name>', hints={'<option_name>': '<option_value>'})"

#: ../../source/faq.rst:-1
msgid "读取数据时报\"project is protected\""
msgstr ""
"An error occurred while reading data: \"project is protected\". How can I"
" deal with this error?"

#: ../../source/faq.rst:49
msgid ""
"Project 上的安全策略禁止读取表中的数据，此时，如果想使用全部数据，有以下"
"选项可用："
msgstr ""
"The project security policy disables reading data from tables. To "
"retrieve all the data, you can apply the following solutions:"

#: ../../source/faq.rst:51
msgid "联系 Project Owner 增加例外规则"
msgstr "Contact the Project Owner to add exceptions."

#: ../../source/faq.rst:52
msgid ""
"使用 DataWorks 或其他脱敏工具先对数据进行脱敏，导出到非保护 Project，再"
"进行读取"
msgstr ""
"Use DataWorks or other masking tool to mask the data and export the data "
"as an unprotected project before reading."

#: ../../source/faq.rst:54
msgid "如果只想查看部分数据，有以下选项"
msgstr "To retrieve part of the data, you can apply the following solutions:"

#: ../../source/faq.rst:56
msgid "改用 ``o.execute_sql('select * from <table_name>').open_reader()``"
msgstr "Use ``o.execute_sql('select * from <table_name>').open_reader()``"

#: ../../source/faq.rst:57
msgid "改用 :ref:`DataFrame <df>`，``o.get_table('<table_name>').to_df()``"
msgstr "Use :ref:`DataFrame <df>`, ``o.get_table('<table_name>').to_df()``"

#: ../../source/faq.rst:60
msgid "出现 ImportError，并且在 ipython 或者 jupyter 下使用"
msgstr ""
"An error occurred while using IPython and Jupyter: ImportError. How can I"
" deal with this error?"

#: ../../source/faq.rst:61
msgid ""
"如果 ``from odps import errors`` 也不行，则是缺少 ipython 组件，执行 ``"
"pip install -U jupyter`` 解决。"
msgstr ""
"If running ``from odps import errors`` does not fix the error, you need "
"to execute ``pip install -U jupyter`` to install the ipython component."

#: ../../source/faq.rst:64
msgid "执行 SQL 通过 open_reader 只能取到最多1万条记录，如何取多余1万条？"
msgstr ""
"I can only retrieve a maximum of 10,000 items of data by executing SQL "
"command open_reader. How can I retrieve more than 10,000 items of data?"

#: ../../source/faq.rst:65
msgid ""
"使用 ``create table as select ...`` 把SQL的结果保存成表，再使用 :ref:`"
"table.open_reader <table_open_reader>` 来读取。"
msgstr ""
"Use ``create table as select ...`` to save the SQL execution result to a "
"table, and use :ref:`table.open_reader <table_open_reader>` to read data."

#: ../../source/faq.rst:68
msgid "执行 SQL 很慢，如何排查？"
msgstr "How can I diagnose the cause when SQL execution is slow?"

#: ../../source/faq.rst:69
msgid ""
"PyODPS 提交 SQL 任务前，并没有进行重度操作。因此，绝大多数情形下，导致"
"提交任务变慢的原因与 PyODPS 没有关系。 你可以考虑排查下面的原因。"
msgstr ""
"PyODPS does not perform heavy operation before submitting a SQL task. "
"Hence in most of the cases, the reason tasks are becoming slow is not "
"related to PyODPS itself. You can consider investigating the issue in "
"aspects below."

#: ../../source/faq.rst:72
msgid "提交任务经过的代理服务器或者网络链路是否存在延迟；"
msgstr "If the proxy server or network chain has long latency."

#: ../../source/faq.rst:73
msgid "服务端是否存在任务排队延迟等情况；"
msgstr "If the task is queued or delayed at the server end."

#: ../../source/faq.rst:74
msgid ""
"如果执行 SQL 的过程包括了拉取数据，是否数据规模过大或者数据分片过多导致"
"拉取数据缓慢；"
msgstr ""
"If your code includes data downloading, whether the data size is too "
"large or the data is split into too many chunks."

#: ../../source/faq.rst:75
msgid ""
"如果是 DataWorks 作业，确认是否存在提交的任务（\\ ``run_sql`` / ``execute"
"_sql``）没有输出 Logview，尤其当 PyODPS \\< 0.11.6 时。"
msgstr ""
"If your cude is executed with DataWorks, make sure that if there are "
"tasks submitted with ``run_sql`` or ``execute_sql`` but without printted "
"logview addresses expecially when PyODPS \\< 0.11.6."

#: ../../source/faq.rst:77
msgid ""
"如果你需要确认提交任务变慢是否由本地环境造成，可以尝试开启调试日志。"
"PyODPS 将会把每个请求及返回都打印出来，可以根据\\ 请求和返回的日志确定"
"延迟发生的位置。"
msgstr ""
"If you need to check whether the delay is caused by your local "
"environment, you can enable debug logs. PyODPS will log all requests and "
"responses. You can use the logs to determine where the delay occurs."

#: ../../source/faq.rst:80
#, python-format
msgid ""
"import datetime\n"
"import logging\n"
"from odps import ODPS\n"
"\n"
"logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s -"
" %(levelname)s - %(message)s')\n"
"o = ODPS(...)  #  此处填入账号，如果环境已提供 MaxCompute Entry 则忽略\n"
"# 打印本地时间以确定本地操作发起的时间\n"
"print(\"Check time:\", datetime.datetime.now())\n"
"# 提交任务\n"
"inst = o.run_sql(\"select * from your_table\")"
msgstr ""
"import datetime\n"
"import logging\n"
"from odps import ODPS\n"
"\n"
"logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s -"
" %(levelname)s - %(message)s')\n"
"o = ODPS(...)  #  fill your account here. skip if MaxCompute Entry is "
"already provided by the environment.\n"
"# print local time to determine the initial time of operation\n"
"print(\"Check time:\", datetime.datetime.now())\n"
"# submit job\n"
"inst = o.run_sql(\"select * from your_table\")"

#: ../../source/faq.rst:94
msgid "此时，你的标准输出应当输出的内容应当形似"
msgstr "Your standard output should look like"

#: ../../source/faq.rst:96
msgid ""
"Check time: 2025-01-24 15:34:21.531330\n"
"2025-01-24 15:34:21,532 - odps.rest - DEBUG - Start request.\n"
"2025-01-24 15:34:21,532 - odps.rest - DEBUG - POST: "
"http://service.<region>.maxcompute.aliyun.com/api/projects/<project>/instances"
"\n"
"2025-01-24 15:34:21,532 - odps.rest - DEBUG - data: b'<?xml "
"version=\"1.0\" encoding=\"utf-8\"?>\\n<Instance>\\n  <Job>\\n    "
"<Priority>9</Priority>\\n    <Tasks>\\n      <SQL>\\n        ....\n"
"2025-01-24 15:34:21,532 - odps.rest - DEBUG - headers: {'Content-Type': "
"'application/xml'}\n"
"2025-01-24 15:34:21,533 - odps.rest - DEBUG - request url + params "
"/api/projects/<project>/instances?curr_project=<project>\n"
"2025-01-24 15:34:21,533 - odps.accounts - DEBUG - headers before signing:"
" {'Content-Type': 'application/xml', 'User-Agent': 'pyodps/0.12.2 "
"CPython/3.7.12', 'Content-Length': '736'}\n"
"2025-01-24 15:34:21,533 - odps.accounts - DEBUG - headers to sign: "
"OrderedDict([('content-md5', ''), ('content-type', 'application/xml'), "
"('date', 'Fri, 24 Jan 2025 07:34:21 GMT')])\n"
"2025-01-24 15:34:21,533 - odps.accounts - DEBUG - canonical string: POST\n"
"\n"
"application/xml\n"
"Fri, 24 Jan 2025 07:34:21 GMT\n"
"/projects/maxframe_ci_cd/instances?curr_project=maxframe_ci_cd\n"
"2025-01-24 15:34:21,533 - odps.accounts - DEBUG - headers after signing: "
"{'Content-Type': 'application/xml', 'User-Agent': 'pyodps/0.12.2 "
"CPython/3.7.12', 'Content-Length': '736', ....\n"
"2025-01-24 15:34:21,533 - urllib3.connectionpool - DEBUG - Resetting "
"dropped connection: service.<region>.maxcompute.aliyun.com\n"
"2025-01-24 15:34:22,027 - urllib3.connectionpool - DEBUG - "
"http://service.<region>.maxcompute.aliyun.com:80 \"POST "
"/api/projects/<project>/instances?curr_project=<project> HTTP/1.1\" 201 0"
"\n"
"2025-01-24 15:34:22,027 - odps.rest - DEBUG - response.status_code 201\n"
"2025-01-24 15:34:22,027 - odps.rest - DEBUG - response.headers:\n"
"{'Server': '<Server>', 'Date': 'Fri, 24 Jan 2025 07:34:22 GMT', 'Content-"
"Type': 'text/plain;charset=utf-8', 'Content-Length': '0', 'Connection': "
"'close', 'Location': ....\n"
"2025-01-24 15:34:22,027 - odps.rest - DEBUG - response.content: b''"
msgstr ""

#: ../../source/faq.rst:119
msgid ""
"从上面的输出中，可以知道代码启动任务的时间（2025-01-24 15:34:21,531）、"
"请求发起时间（2025-01-24 15:34:21,533）以及\\ 服务端返回的时间（2025-01-"
"24 15:34:22,027）获知各个阶段的时间开销。"
msgstr ""
"From the output above, you can know that the code starts execution at "
"2025-01-24 15:34:21.531, request is sent at 2025-01-24 15:34:21.533, and "
"the server returns at 2025-01-24 15:34:22.027."

#: ../../source/faq.rst:122
msgid ""
"如果你需要确认执行变慢是否是由拉取数据造成，可以先尝试将提交执行与拉取"
"数据拆开，也就是说，使用 ``run_sql`` 提交任务，\\ 并使用 ``instance.wait_"
"for_success`` 等待任务结束，再使用 ``instance.open_reader`` 读取数据。"
"也就是说，\\ 将"
msgstr ""
"If you need to check whether the delay is caused by data downloading, you"
" can try spliting the code of SQL execution and data downloading. That "
"is, use ``run_sql`` to submit the SQL task, and use "
"``instance.wait_for_success`` to wait for the task to finish, and then "
"use ``instance.open_reader`` to read the data. That is, change"

#: ../../source/faq.rst:126
msgid ""
"with o.execute_sql('select * from your_table').open_reader() as reader:\n"
"    for row in reader:\n"
"        print(row)"
msgstr ""

#: ../../source/faq.rst:132
msgid "改写为"
msgstr "into"

#: ../../source/faq.rst:134
msgid ""
"inst = o.run_sql('select * from your_table')\n"
"inst.wait_for_success()\n"
"with inst.open_reader() as reader:\n"
"    for row in reader:\n"
"        print(row)"
msgstr ""

#: ../../source/faq.rst:142
msgid "然后再确认各语句造成的延迟。"
msgstr "and then check the latency of every statement above."

#: ../../source/faq.rst:145
msgid ""
"上传 pandas DataFrame 到 ODPS 时报错：ODPSError: ODPS entrance should be "
"provided"
msgstr ""
"An error occurred while uploading pandas DataFrame to MaxCompute ODPS: "
"ODPSError: ODPS entrance should be provided. How can I deal with this "
"error?"

#: ../../source/faq.rst:146
msgid "原因是没有找到全局的ODPS入口，有三个方法："
msgstr ""
"You need to set the ODPS object to global in one of the three following "
"ways:"

#: ../../source/faq.rst:148
#, python-format
msgid "使用 :ref:`room 机制 <cl>` ，``%enter`` 的时候，会配置全局入口"
msgstr ""
"When using :ref:`room mechanism <cl>` , ``%enter`` , configure the global"
" ODPS object."

#: ../../source/faq.rst:149
msgid "对odps入口调用 ``to_global`` 方法"
msgstr "Call the ``to_global`` method when using the ODPS object."

#: ../../source/faq.rst:150
msgid "使用odps参数，``DataFrame(pd_df).persist('your_table', odps=odps)``"
msgstr ""
"Use the MaxCompute parameter ``DataFrame(pd_df).persist('your_table', "
"odps=odps)``."

#: ../../source/faq.rst:153
msgid "在 DataFrame 中如何使用 max_pt ？"
msgstr "How can I use max_pt in DataFrame?"

#: ../../source/faq.rst:154
msgid "使用 ``odps.df.func`` 模块来调用 ODPS 内建函数"
msgstr ""
"Use the ``odps.df.func`` module to call the built-in functions of "
"MaxCompute."

#: ../../source/faq.rst:156
msgid ""
"from odps.df import func\n"
"df = o.get_table('your_table').to_df()\n"
"df[df.ds == func.max_pt('your_project.your_table')]  # ds 是分区字段"
msgstr ""
"from odps.df import func\n"
"df = o.get_table('your_table').to_df()\n"
"df[df.ds == func.max_pt('your_project.your_table')]  # ds is a partition "
"column"

#: ../../source/faq.rst:163
msgid "通过 DataFrame 写表时报 table lifecycle is not specified in mandatory mode"
msgstr ""
"Error \"table lifecycle is not specified in mandatory mode\" occurred "
"when persisting DataFrame to table"

#: ../../source/faq.rst:164
msgid "Project 要求对每张表设置 lifecycle，因而需要在每次执行时设置"
msgstr ""
"Your project requires that every table should be created with a "
"lifecycle. Thus you should run the code below every time you run your own"
" code."

#: ../../source/faq.rst:166
msgid ""
"from odps import options\n"
"options.lifecycle = 7  # 或者你期望的 lifecycle 整数值，单位为天"
msgstr ""
"from odps import options\n"
"options.lifecycle = 7  # or your expected lifecycle in days"

#: ../../source/faq.rst:172
msgid ""
"执行 SQL 时报 Please add put { \"odps.sql.submit.mode\" : \"script\"} for"
" multi-statement query in settings"
msgstr ""
"Error \"Please add put { \"odps.sql.submit.mode\" : \"script\"} for "
"multi-statement query in settings\" occurred when executing SQL scripts"

#: ../../source/faq.rst:173
msgid "请参考 :ref:`SQL设置运行参数 <sql_hints>` 。"
msgstr ""
"Please read :ref:`set runtime parameters <sql_hints>` for more "
"information."

#: ../../source/faq.rst:-1
msgid "如何遍历 PyODPS DataFrame 中的每行数据"
msgstr "How to enumerate rows in PyODPS DataFrame"

#: ../../source/faq.rst:178
msgid ""
"PyODPS DataFrame 不支持遍历每行数据。这样设计的原因是由于 PyODPS "
"DataFrame 面向大规模数据设计，在这种场景下， 数据遍历是非常低效的做法。"
"我们建议使用 DataFrame 提供的 ``apply`` 或 ``map_reduce`` 接口将原本串行"
"的遍历操作并行化， 具体可参见 `这篇文章 <https://yq.aliyun.com/articles/"
"138752>`_ 。如果确认你的场景必须要使用数据遍历， 而且遍历的代价可以接受，"
"可以使用 ``to_pandas`` 方法将 DataFrame 转换为 Pandas DataFrame，或者将 "
"DataFrame 存储为表后使用 ``read_table`` 或者 Tunnel 读取数据。"
msgstr ""
"We do not support enumerating over every row in PyODPS DataFrame. As "
"PyODPS DataFrame mainly focuses on handling huge amount of data, "
"enumerating over every row means low efficiency and is discouraged. We "
"recommend using ```apply``` or ```map_reduce``` methods of DataFrame to "
"parallelize your enumerations. Details can be found in `this article "
"<https://yq.aliyun.com/articles/138752>`_ . If you are sure that your "
"code cannot be parallelized using methods listed above, and the cost of "
"enumeration is tolerable, you may use ```to_pandas``` to convert your "
"DataFrame into Pandas, or persist your DataFrame into a MaxCompute table "
"and read it via ```read_table``` method or table tunnel."

#: ../../source/faq.rst:-1
msgid "为何调用 to_pandas 后内存使用显著大于表的大小？"
msgstr ""
"Why memory usage after calling to_pandas is significantly larger than the"
" size of the table?"

#: ../../source/faq.rst:187
msgid ""
"有两个原因可能导致这个现象发生。首先，MaxCompute 在存储数据时会对数据进行"
"压缩，你看到的表大小应当是压缩后的大小。 其次，Python 中的值存在额外的"
"存储开销。例如，对于字符串类型而言，每个 Python 字符串都会额外占用近 40 "
"字节空间， 即便该字符串为空串，这可以通过调用 ``sys.getsizeof(\"\")`` "
"发现。"
msgstr ""
"Two possible reasons might cause this issue. First, MaxCompute compresses"
" table data, and the size you see is the size after compression. Second, "
"variables are stored in Python with extra overhead. For instance, for "
"every Python string, an overhead of approximately 40 bytes will be taken "
"even if the string is empty. You may get the size by calling "
"``sys.getsizeof(\"\")``."

#: ../../source/faq.rst:191
msgid ""
"需要注意的是，使用 Pandas 的 ``info`` 或者 ``memory_usage`` 方法获得的 "
"Pandas DataFrame 内存使用可能是不准确的，因为这些方法默认不计算 string "
"或者其他 object 类型对象的实际内存占用。使用 ``df.memory_usage(deep=True)"
".sum()`` 获得的大小更接近实际内存使用，具体可参考 `这篇 Pandas 文档 <"
"https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.memory_"
"usage.html>`_ 。"
msgstr ""
"Note that when using ``info`` or ``memory_usage`` of Pandas to calculate "
"the size of your DataFrame might not be accurate, as these methods does "
"not take string types or objects into account by default. To get sizes of"
" DataFrames with more accuracy, ``df.memory_usage(deep=True).sum()`` "
"might be used. Details can be seen `in this Pandas document "
"<https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.memory_usage.html>`_."

#: ../../source/faq.rst:196
msgid ""
"为减小读取数据时的内存开销，可以考虑使用 Arrow 格式，具体可以参考 :ref:`"
"这里 <table_read>`。"
msgstr ""
"To reduce memory usage when reading data, you might try Arrow format. "
"Details can be found :ref:`here <table_read>`."

