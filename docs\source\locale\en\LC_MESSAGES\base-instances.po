# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-19 21:33+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-instances.rst:4
msgid "任务实例"
msgstr "Instance"

#: ../../source/base-instances.rst:6
msgid ""
"Task如SQLTask是ODPS的基本计算单元，当一个Task在执行时会被实例化， 以 `"
"ODPS实例 <https://help.aliyun.com/document_detail/27825.html>`_ 的形式"
"存在。"
msgstr ""
"Tasks such as SQLTask are the basic computing units in MaxCompute. When "
"executed, a Task is instantiated as a `MaxCompute instance "
"<https://www.alibabacloud.com/help/en/doc-detail/27825.htm>`_."

#: ../../source/base-instances.rst:10
msgid "基本操作"
msgstr "Basic operations"

#: ../../source/base-instances.rst:12
msgid ""
"可以调用 :meth:`~odps.ODPS.list_instances` 来获取项目空间下的所有 "
"instance，\\ :meth:`~odps.ODPS.exist_instance` 能判断是否存在某instance，"
"\\ :meth:`~odps.ODPS.get_instance` 能获取实例。"
msgstr ""
"You can call :meth:`~odps.ODPS.list_instances` to retrieve all the "
"instances in the project. You can use :meth:`~odps.ODPS.exist_instance` "
"to determine if an instance exists, and use "
":meth:`~odps.ODPS.get_instance` to retrieve instances."

#: ../../source/base-instances.rst:16
msgid ""
">>> for instance in o.list_instances():\n"
">>>     print(instance.id)\n"
">>> if o.exist_instance('<my_instance_id>'):\n"
">>>     print(\"Instance <my_instance_id> exists!\")"
msgstr ""

#: ../../source/base-instances.rst:24
msgid ""
"停止一个instance可以在odps入口使用 :meth:`~odps.ODPS.stop_instance`\\ ，"
"或者对 :class:`~odps.models.Instance` 对象调用 :meth:`~odps.models."
"Instance.stop` 方法："
msgstr ""
"You can call :meth:`~odps.ODPS.stop_instance` on an odps object to stop "
"an instance, or call the :meth:`~odps.models.Instance.stop` method on an "
":class:`~odps.models.Instance` object."

#: ../../source/base-instances.rst:27
msgid ""
">>> # 方法1：使用 stop_instance\n"
">>> o.exist_instance('<my_instance_id>')\n"
">>> # 方法2：使用 instance 的 stop 方法\n"
">>> instance = o.get_instance('<my_instance_id>')\n"
">>> instance.stop()"
msgstr ""
">>> # Method 1: use stop_instance to stop an instance\n"
">>> o.exist_instance('<my_instance_id>')\n"
">>> # Method 2: use stop method of instance object to stop an instance\n"
">>> instance = o.get_instance('<my_instance_id>')\n"
">>> instance.stop()"

#: ../../source/base-instances.rst:38
msgid "获取 LogView 地址"
msgstr "Retrieve LogView address"

#: ../../source/base-instances.rst:40
msgid ""
"对于 SQL 等任务，通过调用 :meth:`~odps.ODPS.get_logview_address` 方法即可"
"。"
msgstr ""
"For a SQL task, you can call the :meth:`~odps.ODPS.get_logview_address` "
"method to retrieve the LogView address."

#: ../../source/base-instances.rst:42
msgid ""
">>> # 从已有的 instance 对象\n"
">>> instance = o.run_sql('desc pyodps_iris')\n"
">>> print(instance.get_logview_address())\n"
">>> # 从 instance id\n"
">>> instance = o.get_instance('2016042605520945g9k5pvyi2')\n"
">>> print(instance.get_logview_address())"
msgstr ""
">>> # from an existing instance object\n"
">>> instance = o.run_sql('desc pyodps_iris')\n"
">>> print(instance.get_logview_address())\n"
">>> # from an instance id\n"
">>> instance = o.get_instance('2016042605520945g9k5pvyi2')\n"
">>> print(instance.get_logview_address())"

#: ../../source/base-instances.rst:51
msgid ""
"对于 XFlow 任务，需要枚举其子任务，再获取子任务的 LogView。更多细节可以"
"参考 :ref:`XFlow 和模型 <models>` 。"
msgstr ""
"For an XFlow task, you need to enumerate its subtasks and retrieve their "
"LogView as follows. More details can be seen at :ref:`XFlow and models "
"<models>`."

#: ../../source/base-instances.rst:53
#, python-format
msgid ""
">>> instance = o.run_xflow('AppendID', 'algo_public',\n"
"                           {'inputTableName': 'input_table', "
"'outputTableName': 'output_table'})\n"
">>> for sub_inst_name, sub_inst in "
"o.get_xflow_sub_instances(instance).items():\n"
">>>     print('%s: %s' % (sub_inst_name, sub_inst.get_logview_address()))"
msgstr ""

#: ../../source/base-instances.rst:61
msgid "任务实例状态"
msgstr "Instance status"

#: ../../source/base-instances.rst:63
msgid ""
"一个instance的状态可以是 ``Running``、``Suspended`` 或者 ``Terminated``，"
"用户可以通过 :attr:`~odps.models.Instance.status` 属性来获取状态。:meth:`"
"~odps.models.Instance.is_terminated` 方法返回当前instance是否已经执行完成"
"，:meth:`~odps.models.Instance.is_successful` 方法返回当前instance是否"
"正确完成执行，任务处于运行中或者执行失败都会返回False。"
msgstr ""
"The status of an instance can be ``Running``, ``Suspended`` or "
"``Terminated``. You can retrieve the status of an instance by using the "
":attr:`~odps.models.Instance.status` attribute. The "
":meth:`~odps.models.Instance.is_terminated` method returns whether the "
"execution of the current instance has been completed. The "
":meth:`~odps.models.Instance.is_successful` method returns whether the "
"execution of the current instance has been successful. A False is "
"returned if the instance is still running or if the execution has failed."

#: ../../source/base-instances.rst:68
msgid ""
">>> instance = o.get_instance('2016042605520945g9k5pvyi2')\n"
">>> instance.status\n"
"<Status.TERMINATED: 'Terminated'>\n"
">>> from odps.models import Instance\n"
">>> instance.status == Instance.Status.TERMINATED\n"
"True\n"
">>> instance.status.value\n"
"'Terminated'"
msgstr ""

#: ../../source/base-instances.rst:80
msgid ""
"调用 :meth:`~odps.models.Instance.wait_for_completion` 方法会阻塞直到"
"instance执行完成。\\ :meth:`~odps.models.Instance.wait_for_success` 方法"
"同样会阻塞，不同的是，\\ 如果最终任务执行失败，则会抛出相关异常。"
msgstr ""
"The :meth:`~odps.models.Instance.wait_for_completion` method will block "
"your thread until the execution of the current instance has been "
"completed. The :meth:`~odps.models.Instance.wait_for_success` method will"
" also block until the execution of the current instance has been "
"successful. Otherwise, an exception is thrown."

#: ../../source/base-instances.rst:85
msgid "子任务操作"
msgstr "Subtask operations"

#: ../../source/base-instances.rst:87
msgid ""
"一个Instance真正运行时，可能包含一个或者多个子任务，我们称为Task，要注意"
"这个Task不同于ODPS的计算单元。"
msgstr ""
"When an instance is running, it may contain one or several subtasks, "
"which are called Tasks. Note that these Tasks are different from the "
"computing units in MaxCompute."

#: ../../source/base-instances.rst:89
msgid ""
"我们可以通过 :meth:`~odps.models.Instance.get_task_names` 来获取所有的"
"Task任务，它返回一个所有子任务的名称列表。"
msgstr ""
"You can call :meth:`~odps.models.Instance.get_task_names` to retrieve all"
" Tasks. This method returns the Task names in a list type."

#: ../../source/base-instances.rst:91
msgid ""
">>> instance.get_task_names()\n"
"['SQLDropTableTask']"
msgstr ""

#: ../../source/base-instances.rst:96
msgid ""
"拿到Task的名称，我们就可以通过 :meth:`~odps.models.Instance.get_task_"
"result` 来获取这个Task的执行结果。\\ :meth:`~odps.models.Instance.get_"
"task_results` 以字典的形式返回每个Task的执行结果"
msgstr ""
"After getting the Task names, you can use "
":meth:`~odps.models.Instance.get_task_result` to retrieve the execution "
"results of these tasks. The "
":meth:`~odps.models.Instance.get_task_results` method returns a dict "
"type."

#: ../../source/base-instances.rst:99
msgid ""
">>> instance = o.execute_sql('select * from pyodps_iris limit 1')\n"
">>> instance.get_task_names()\n"
"['AnonymousSQLTask']\n"
">>> instance.get_task_result('AnonymousSQLTask')\n"
"'\"sepallength\",\"sepalwidth\",\"petallength\",\"petalwidth\",\"name\"\\n5.1,3.5,1.4,0.2"
",\"Iris-setosa\"\\n'\n"
">>> instance.get_task_results()\n"
"OrderedDict([('AnonymousSQLTask',\n"
"           "
"'\"sepallength\",\"sepalwidth\",\"petallength\",\"petalwidth\",\"name\"\\n5.1,3.5,1.4,0.2"
",\"Iris-setosa\"\\n')])"
msgstr ""

#: ../../source/base-instances.rst:110
msgid ""
"有时候我们需要在任务实例运行时显示所有子任务的运行进程。使用 :meth:`~odps"
".models.Instance.get_task_progress` 能获得Task当前的运行进度。"
msgstr ""
"You can use :meth:`~odps.models.Instance.get_task_progress` to retrieve "
"the running progress of a Task."

#: ../../source/base-instances.rst:113
msgid ""
">>> while not instance.is_terminated():\n"
">>>     for task_name in instance.get_task_names():\n"
">>>         print(instance.id, "
"instance.get_task_progress(task_name).get_stage_progress_formatted_string())"
"\n"
">>>     time.sleep(10)\n"
"20160519101349613gzbzufck2 2016-05-19 18:14:03 M1_Stg1_job0:0/1/1[100%]"
msgstr ""

