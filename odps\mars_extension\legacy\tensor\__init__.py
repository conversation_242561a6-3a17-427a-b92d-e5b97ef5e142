#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Copyright 1999-2022 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import pkgutil
import sys

try:
    from .datasource import read_coo
    from .datastore import write_coo
except ImportError as e:
    if "CI_MODE" in os.environ and pkgutil.find_loader("mars") is not None:
        if (
            sys.version_info[0] > 2
        ):  # todo remove this check when mars package under py27mu is fixed
            raise
    read_coo, write_coo = None, None

__all__ = ["read_coo", "write_coo"]
