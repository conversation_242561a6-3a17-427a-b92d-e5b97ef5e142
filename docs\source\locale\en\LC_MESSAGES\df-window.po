# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-07-10 10:42+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-window.rst:6
msgid "from odps.df import DataFrame"
msgstr ""

#: ../../source/df-window.rst:10
msgid "iris = DataFrame(o.get_table('pyodps_iris'))"
msgstr ""

#: ../../source/df-window.rst:12 ../../source/df-window.rst:55
msgid "窗口函数"
msgstr "Window functions"

#: ../../source/df-window.rst:14
msgid "DataFrame API也支持使用窗口函数："
msgstr ""
"The DataFrame application program interface (API) supports window "
"functions:"

#: ../../source/df-window.rst:31
msgid ""
">>> grouped = iris.groupby('name')\n"
">>> grouped.mutate(grouped.sepallength.cumsum(), "
"grouped.sort('sepallength').row_number()).head(10)\n"
"          name  sepallength_sum  row_number\n"
"0  Iris-setosa            250.3           1\n"
"1  Iris-setosa            250.3           2\n"
"2  Iris-setosa            250.3           3\n"
"3  Iris-setosa            250.3           4\n"
"4  Iris-setosa            250.3           5\n"
"5  Iris-setosa            250.3           6\n"
"6  Iris-setosa            250.3           7\n"
"7  Iris-setosa            250.3           8\n"
"8  Iris-setosa            250.3           9\n"
"9  Iris-setosa            250.3          10"
msgstr ""

#: ../../source/df-window.rst:32
msgid "窗口函数可以使用在列选择中："
msgstr "You can use Window functions to select columns:"

#: ../../source/df-window.rst:43
msgid ""
">>> iris['name', 'sepallength', "
"iris.groupby('name').sort('sepallength').sepallength.cumcount()].head(5)"
"\n"
"          name  sepallength  sepallength_count\n"
"0  Iris-setosa          4.3                  1\n"
"1  Iris-setosa          4.4                  2\n"
"2  Iris-setosa          4.4                  3\n"
"3  Iris-setosa          4.4                  4\n"
"4  Iris-setosa          4.5                  5"
msgstr ""

#: ../../source/df-window.rst:44
msgid "窗口函数按标量聚合时，和分组聚合的处理方式一致。"
msgstr ""
"To use window functions to aggregate by scalar, follow the processing "
"method of grouping and aggregation."

#: ../../source/df-window.rst:51
msgid ""
">>> from odps.df import Scalar\n"
">>> iris.groupby(Scalar(1)).sort('sepallength').sepallength.cumcount()"
msgstr ""

#: ../../source/df-window.rst:52
msgid "DataFrame API支持的窗口函数包括："
msgstr "The DataFrame API supports the following window functions:"

#: ../../source/df-window.rst:55 ../../source/df-window.rst:78
#: ../../source/df-window.rst:87 ../../source/df-window.rst:96
msgid "说明"
msgstr "Description"

#: ../../source/df-window.rst:57
msgid "cumsum"
msgstr ""

#: ../../source/df-window.rst:57 ../../source/df-window.rst:63
msgid "计算累积和"
msgstr "Calculates the cumulative sum."

#: ../../source/df-window.rst:58
msgid "cummean"
msgstr ""

#: ../../source/df-window.rst:58
msgid "计算累积均值"
msgstr "Calculates the cumulative mean."

#: ../../source/df-window.rst:59
msgid "cummedian"
msgstr ""

#: ../../source/df-window.rst:59
msgid "计算累积中位数"
msgstr "Calculates the cumulative median."

#: ../../source/df-window.rst:60
msgid "cumstd"
msgstr ""

#: ../../source/df-window.rst:60
msgid "计算累积标准差"
msgstr "Calculates the cumulative standard deviation."

#: ../../source/df-window.rst:61
msgid "cummax"
msgstr ""

#: ../../source/df-window.rst:61
msgid "计算累积最大值"
msgstr "Calculates the cumulative maximum."

#: ../../source/df-window.rst:62
msgid "cummin"
msgstr ""

#: ../../source/df-window.rst:62
msgid "计算累积最小值"
msgstr "Calculates the cumulative minimum."

#: ../../source/df-window.rst:63
msgid "cumcount"
msgstr ""

#: ../../source/df-window.rst:64
msgid "lag"
msgstr ""

#: ../../source/df-window.rst:64
msgid "按偏移量取当前行之前第几行的值，如当前行号为rn，则取行号为rn-offset的值"
msgstr ""
"Retrieves the value in the row with the offset before the current row. "
"For example, you can retrieve the value in the row rn-offset when the "
"current row is rn."

#: ../../source/df-window.rst:65
msgid "lead"
msgstr ""

#: ../../source/df-window.rst:65
msgid "按偏移量取当前行之后第几行的值，如当前行号为rn则取行号为rn+offset的值"
msgstr ""
"Retrieves the value in the row with the offset following the current row."
" For example, you can retrieve the value in the row rn+offset when the "
"current row is rn."

#: ../../source/df-window.rst:66
msgid "rank"
msgstr ""

#: ../../source/df-window.rst:66
msgid "计算排名"
msgstr "Calculates the rank."

#: ../../source/df-window.rst:67
msgid "dense_rank"
msgstr ""

#: ../../source/df-window.rst:67
msgid "计算连续排名"
msgstr "Calculates the dense rank."

#: ../../source/df-window.rst:68
msgid "percent_rank"
msgstr ""

#: ../../source/df-window.rst:68
msgid "计算一组数据中某行的相对排名"
msgstr "Calculates the relative rank."

#: ../../source/df-window.rst:69
msgid "row_number"
msgstr ""

#: ../../source/df-window.rst:69
msgid "计算行号，从1开始"
msgstr "Calculates the row number, starting from 1."

#: ../../source/df-window.rst:70
msgid "qcut"
msgstr ""

#: ../../source/df-window.rst:70
msgid ""
"将分组数据按顺序切分成n片，并返回当前切片值，如果切片不均匀，默认增加"
"第一个切片的分布"
msgstr ""
"Cuts data in the group into n slices in order and returns the number of "
"the cut containing the current data. If data are not distributed evenly "
"in cuts, extra data will be put in the first cut."

#: ../../source/df-window.rst:71
msgid "nth_value"
msgstr ""

#: ../../source/df-window.rst:71
msgid "返回分组中的第n个值"
msgstr "Returns the nth value in the group"

#: ../../source/df-window.rst:72
msgid "cume_dist"
msgstr ""

#: ../../source/df-window.rst:72
msgid "计算分组中值小于等于当前值的行数占分组总行数的比例"
msgstr ""
"Calculates the ratio of lines whose line numbers are less than the "
"current line"

#: ../../source/df-window.rst:75
msgid "其中，rank、dense_rank、percent_rank 和 row_number 支持下列参数："
msgstr ""
"In the preceding window functions, rank, dense_rank, percent_rank, and "
"row_number support the following parameters:"

#: ../../source/df-window.rst:78 ../../source/df-window.rst:87
#: ../../source/df-window.rst:96
msgid "参数名"
msgstr "Parameter"

#: ../../source/df-window.rst:80
msgid "sort"
msgstr ""

#: ../../source/df-window.rst:80
msgid "排序关键字，默认为空"
msgstr "This is the sorting keyword, which is null by default."

#: ../../source/df-window.rst:81
msgid "ascending"
msgstr ""

#: ../../source/df-window.rst:81
msgid "排序时，是否依照升序排序，默认 True"
msgstr ""
"You can specify this parameter to enable or disable the ascending order "
"in sorting. This parameter is set to True by default."

#: ../../source/df-window.rst:84
msgid "lag 和 lead 除 rank 的参数外，还支持下列参数："
msgstr ""
"In addition to the rank parameter, the lag and lead window functions "
"support the following parameters:"

#: ../../source/df-window.rst:89
msgid "offset"
msgstr ""

#: ../../source/df-window.rst:89
msgid "取数据的行距离当前行的行数"
msgstr ""
"Indicates the number of rows that are between the current row and the row"
" where you retrieve data."

#: ../../source/df-window.rst:90
msgid "default"
msgstr ""

#: ../../source/df-window.rst:90
msgid "当 offset 指定的行不存在时的返回值"
msgstr ""
"Returns this value when the row that has been specified in the offset "
"does not exist."

#: ../../source/df-window.rst:93
msgid ""
"而 cumsum、cummax、cummin、cummean、cummedian、cumcount 和 cumstd 除 rank"
" 的上述参数外，还支持下列参数："
msgstr ""
"In addition to the rank parameter, the cumsum, cummax, cummin, cummean, "
"cummedian, cumcount, and cumstd window functions support the following "
"parameters:"

#: ../../source/df-window.rst:98
msgid "unique"
msgstr ""

#: ../../source/df-window.rst:98
msgid "是否排除重复值，默认 False"
msgstr ""
"Enables or disables deduplication of data. This parameter is set to False"
" by default."

#: ../../source/df-window.rst:99
msgid "preceding"
msgstr ""

#: ../../source/df-window.rst:99
msgid "窗口范围起点"
msgstr "Specifies the start of windowing."

#: ../../source/df-window.rst:100
msgid "following"
msgstr ""

#: ../../source/df-window.rst:100
msgid "窗口范围终点"
msgstr "Specifies the end of windowing."

