# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-11 00:03+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-agg.rst:6
msgid "from odps.df import DataFrame"
msgstr ""

#: ../../source/df-agg.rst:10
msgid "iris = DataFrame(o.get_table('pyodps_iris'))"
msgstr ""

#: ../../source/df-agg.rst:12 ../../source/df-agg.rst:59
msgid "聚合操作"
msgstr "Aggregation"

#: ../../source/df-agg.rst:14
msgid ""
"首先，我们可以使用\\ ``describe``\\ 函数，来查看DataFrame里数字列的数量、"
"最大值、最小值、平均值以及标准差是多少。"
msgstr ""
"First, use the \\ ``describe``\\ function to view the quantity, maximum, "
"minimum, mean, and standard deviation in number columns of DataFrame."

#: ../../source/df-agg.rst:25
msgid ""
">>> print(iris.describe())\n"
"    type  sepal_length  sepal_width  petal_length  petal_width\n"
"0  count    150.000000   150.000000    150.000000   150.000000\n"
"1   mean      5.843333     3.054000      3.758667     1.198667\n"
"2    std      0.828066     0.433594      1.764420     0.763161\n"
"3    min      4.300000     2.000000      1.000000     0.100000\n"
"4    max      7.900000     4.400000      6.900000     2.500000"
msgstr ""

#: ../../source/df-agg.rst:26
msgid "我们可以使用单列来执行聚合操作："
msgstr "You can execute aggregation operations in one column:"

#: ../../source/df-agg.rst:32
msgid ""
">>> iris.sepallength.max()\n"
"7.9"
msgstr ""

#: ../../source/df-agg.rst:33
msgid ""
"如果要在消除重复后的列上进行聚合，可以先调用 ``unique`` 方法，再调用相应"
"的聚合函数："
msgstr ""
"You can aggregate over a distinct sequence, you can call ``unique`` "
"method on the sequence before calling actual aggregation methods."

#: ../../source/df-agg.rst:40
msgid ""
">>> iris.name.unique().cat(sep=',')\n"
"u'Iris-setosa,Iris-versicolor,Iris-virginica'"
msgstr ""

#: ../../source/df-agg.rst:41
msgid "如果所有列支持同一种聚合操作，也可以直接在整个 DataFrame 上执行聚合操作："
msgstr ""
"If all columns support the same aggregation operation, you can execute "
"this aggregation operation in the entire DataFrame:"

#: ../../source/df-agg.rst:48
msgid ""
">>> iris.exclude('category').mean()\n"
"   sepal_length  sepal_width  petal_length  petal_width\n"
"1      5.843333     3.054000      3.758667     1.198667"
msgstr ""

#: ../../source/df-agg.rst:49
msgid "需要注意的是，在 DataFrame 上执行 count 获取的是 DataFrame 的总行数："
msgstr ""
"Note that you can obtain the total number of DataFrame rows by executing "
"the count operation:"

#: ../../source/df-agg.rst:55
msgid ""
">>> iris.count()\n"
"150"
msgstr ""

#: ../../source/df-agg.rst:56
msgid "PyODPS 支持的聚合操作包括："
msgstr ""
"Python on MaxCompute (PyODPS) includes the following aggregation "
"operations:"

#: ../../source/df-agg.rst:59
msgid "说明"
msgstr "Description"

#: ../../source/df-agg.rst:61
msgid "count（或size）"
msgstr "count (or size)"

#: ../../source/df-agg.rst:61
msgid "数量"
msgstr "Quantity"

#: ../../source/df-agg.rst:62
msgid "nunique"
msgstr ""

#: ../../source/df-agg.rst:62
msgid "不重复值数量"
msgstr "Number of unique values"

#: ../../source/df-agg.rst:63
msgid "min"
msgstr ""

#: ../../source/df-agg.rst:63
msgid "最小值"
msgstr "Minimum"

#: ../../source/df-agg.rst:64
msgid "max"
msgstr ""

#: ../../source/df-agg.rst:64
msgid "最大值"
msgstr "Maximum"

#: ../../source/df-agg.rst:65
msgid "sum"
msgstr ""

#: ../../source/df-agg.rst:65
msgid "求和"
msgstr "Summation"

#: ../../source/df-agg.rst:66
msgid "mean"
msgstr ""

#: ../../source/df-agg.rst:66
msgid "均值"
msgstr "Mean value"

#: ../../source/df-agg.rst:67
msgid "median"
msgstr ""

#: ../../source/df-agg.rst:67
msgid "中位数"
msgstr "Median value"

#: ../../source/df-agg.rst:68
msgid "quantile(p)"
msgstr ""

#: ../../source/df-agg.rst:68
msgid "p分位数，仅在整数值下可取得准确值"
msgstr "P-quantile, accurate only for integers"

#: ../../source/df-agg.rst:69
msgid "var"
msgstr ""

#: ../../source/df-agg.rst:69
msgid "方差"
msgstr "Variance"

#: ../../source/df-agg.rst:70
msgid "std"
msgstr ""

#: ../../source/df-agg.rst:70
msgid "标准差"
msgstr "Standard deviation"

#: ../../source/df-agg.rst:71
msgid "moment"
msgstr ""

#: ../../source/df-agg.rst:71
msgid "n 阶中心矩（或 n 阶矩）"
msgstr "Nth central moment"

#: ../../source/df-agg.rst:72
msgid "skew"
msgstr ""

#: ../../source/df-agg.rst:72
msgid "样本偏度（无偏估计）"
msgstr "Sample skewness (unbiased estimation)"

#: ../../source/df-agg.rst:73
msgid "kurtosis"
msgstr ""

#: ../../source/df-agg.rst:73
msgid "样本峰度（无偏估计）"
msgstr "Sample kurtosis (unbiased estimation)"

#: ../../source/df-agg.rst:74
msgid "cat"
msgstr ""

#: ../../source/df-agg.rst:74
msgid "按sep做字符串连接操作"
msgstr "Operation of concatenating character strings with a separator"

#: ../../source/df-agg.rst:75
msgid "tolist"
msgstr ""

#: ../../source/df-agg.rst:75
msgid "组合为 list"
msgstr "Operation of aggregating a column into a list"

#: ../../source/df-agg.rst:78
msgid ""
"需要注意的是，与 Pandas 不同，对于列上的聚合操作，不论是在 ODPS 还是 "
"Pandas 后端下，PyODPS DataFrame 都会忽略空值。这一逻辑与 SQL 类似。"
msgstr ""
"Note that different from Pandas, aggregations in PyODPS DataFrame, "
"whenever under MaxCompute or Pandas backend, neglect null values. This "
"behavior can also be seen in SQL."

#: ../../source/df-agg.rst:82
msgid "分组聚合"
msgstr "Group and aggregate"

#: ../../source/df-agg.rst:84
msgid ""
"DataFrame API提供了groupby来执行分组操作，分组后的一个主要操作就是通过"
"调用agg或者aggregate方法，来执行聚合操作。"
msgstr ""
"The DataFrame application program interface (API) provides the groupby "
"method to execute grouping. One of the main operations after grouping is "
"to use the agg or aggregate method to execute aggregation operations."

#: ../../source/df-agg.rst:94
msgid ""
">>> iris.groupby('name').agg(iris.sepallength.max(), "
"smin=iris.sepallength.min())\n"
"              name  sepallength_max  smin\n"
"0      Iris-setosa              5.8   4.3\n"
"1  Iris-versicolor              7.0   4.9\n"
"2   Iris-virginica              7.9   4.9"
msgstr ""

#: ../../source/df-agg.rst:95
msgid "最终的结果列中会包含分组的列，以及聚合的列。"
msgstr "The result columns include the group column and the aggregated column."

#: ../../source/df-agg.rst:97
msgid ""
"DataFrame 提供了一个\\ ``value_counts``\\ 操作，能返回按某列分组后，每个"
"组的个数从大到小排列的操作。"
msgstr ""
"DataFrame provides the \\ ``value_counts``\\ operation to sort the unique"
" data quantity in a descending order in a group after grouping by column."

#: ../../source/df-agg.rst:99
msgid "我们使用 groupby 表达式可以写成："
msgstr "You can use the groupby expression in the following code:"

#: ../../source/df-agg.rst:108
msgid ""
">>> iris.groupby('name').agg(count=iris.name.count()).sort('count', "
"ascending=False).head(5)\n"
"              name  count\n"
"0   Iris-virginica     50\n"
"1  Iris-versicolor     50\n"
"2      Iris-setosa     50"
msgstr ""

#: ../../source/df-agg.rst:109
msgid "使用value\\_counts就很简单了："
msgstr "Simplify the code by using value\\_counts:"

#: ../../source/df-agg.rst:118
msgid ""
">>> iris['name'].value_counts().head(5)\n"
"              name  count\n"
"0   Iris-virginica     50\n"
"1  Iris-versicolor     50\n"
"2      Iris-setosa     50"
msgstr ""

#: ../../source/df-agg.rst:119
msgid ""
"需要注意的是，该方法返回的行数大小受到 ODPS 排序返回结果大小的限制，默认"
"为 10000 行，可通过 ``options.df.odps.sort.limit`` 配置，详见 :ref:`配置"
"选项 <options>` 。"
msgstr ""
"Note that the number of lines returned by ``value_counts`` is limited due"
" to limitations on ORDER BY clause of MaxCompute SQL. The default "
"limitation is 10,000, which can be changed via "
"``options.df.odps.sort.limit``. More details can be seen in "
":ref:`configuration section <options>`."

#: ../../source/df-agg.rst:122
msgid "对于聚合后的单列操作，我们也可以直接取出列名。但此时只能使用聚合函数。"
msgstr ""
"Only aggregate functions can be used after you retrieve the column name "
"in a single aggregated column."

#: ../../source/df-agg.rst:131
msgid ""
">>> iris.groupby('name').petallength.sum()\n"
"   petallength_sum\n"
"0             73.2\n"
"1            213.0\n"
"2            277.6"
msgstr ""

#: ../../source/df-agg.rst:140
msgid ""
">>> iris.groupby('name').agg(iris.petallength.notnull().sum())\n"
"              name  petallength_sum\n"
"0      Iris-setosa               50\n"
"1  Iris-versicolor               50\n"
"2   Iris-virginica               50"
msgstr ""

#: ../../source/df-agg.rst:141
msgid "分组时也支持对常量进行分组，但是需要使用Scalar初始化。"
msgstr ""
"The system also supports grouping by constants. This requires Scalar "
"initialization."

#: ../../source/df-agg.rst:149
msgid ""
">>> from odps.df import Scalar\n"
">>> iris.groupby(Scalar(1)).petallength.sum()\n"
"   petallength_sum\n"
"0            563.8"
msgstr ""

#: ../../source/df-agg.rst:151
msgid "编写自定义聚合"
msgstr "Write custom aggregations"

#: ../../source/df-agg.rst:153
msgid ""
"对字段调用agg或者aggregate方法来调用自定义聚合。自定义聚合需要提供一个类"
"，这个类需要提供以下方法："
msgstr ""
"Use the agg or aggregate method on fields with a custom aggregation. The "
"custom aggregation requires a class, which provides the following "
"methods:"

#: ../../source/df-agg.rst:155
msgid ""
"buffer()：返回一个mutable的object（比如 list、dict），buffer大小不应随"
"数据而递增。"
msgstr ""
"buffer(): returns a mutable object such as list and dict. The buffer size"
" should not increase during data processing."

#: ../../source/df-agg.rst:156
msgid "__call__(buffer, *val)：将值聚合到中间 buffer。"
msgstr "__call__(buffer, *val): aggregates values to buffer."

#: ../../source/df-agg.rst:157
msgid "merge(buffer, pbuffer)：将 pbuffer 聚合到 buffer 中。"
msgstr "merge(buffer, pbuffer): aggregates pbuffer to buffer."

#: ../../source/df-agg.rst:158
msgid "getvalue(buffer)：返回最终值。"
msgstr "getvalue(buffer): returns the final value."

#: ../../source/df-agg.rst:160
msgid "让我们看一个计算平均值的例子。"
msgstr "Calculate a mean in the following example:"

#: ../../source/df-agg.rst:162
msgid ""
"class Agg(object):\n"
"\n"
"    def buffer(self):\n"
"        return [0.0, 0]\n"
"\n"
"    def __call__(self, buffer, val):\n"
"        buffer[0] += val\n"
"        buffer[1] += 1\n"
"\n"
"    def merge(self, buffer, pbuffer):\n"
"        buffer[0] += pbuffer[0]\n"
"        buffer[1] += pbuffer[1]\n"
"\n"
"    def getvalue(self, buffer):\n"
"        if buffer[1] == 0:\n"
"            return 0.0\n"
"        return buffer[0] / buffer[1]"
msgstr ""

#: ../../source/df-agg.rst:186
msgid ""
">>> iris.sepalwidth.agg(Agg)\n"
"3.0540000000000007"
msgstr ""

#: ../../source/df-agg.rst:187
msgid "如果最终类型和输入类型发生了变化，则需要指定类型。"
msgstr ""
"If the output type is different from the input type, you need to specify "
"the output type."

#: ../../source/df-agg.rst:193
msgid ">>> iris.sepalwidth.agg(Agg, 'float')"
msgstr ""

#: ../../source/df-agg.rst:194
msgid "自定义聚合也可以用在分组聚合中。"
msgstr "Custom aggregations support grouping and aggregation."

#: ../../source/df-agg.rst:203
msgid ""
">>> iris.groupby('name').sepalwidth.agg(Agg)\n"
"   petallength_aggregation\n"
"0                    3.418\n"
"1                    2.770\n"
"2                    2.974"
msgstr ""

#: ../../source/df-agg.rst:204
msgid "当对多列调用自定义聚合，可以使用agg方法。"
msgstr "You can use the agg method in your aggregation for multiple columns."

#: ../../source/df-agg.rst:206
msgid ""
"class Agg(object):\n"
"\n"
"    def buffer(self):\n"
"        return [0.0, 0.0]\n"
"\n"
"    def __call__(self, buffer, val1, val2):\n"
"        buffer[0] += val1\n"
"        buffer[1] += val2\n"
"\n"
"    def merge(self, buffer, pbuffer):\n"
"        buffer[0] += pbuffer[0]\n"
"        buffer[1] += pbuffer[1]\n"
"\n"
"    def getvalue(self, buffer):\n"
"        if buffer[1] == 0:\n"
"            return 0.0\n"
"        return buffer[0] / buffer[1]"
msgstr ""

#: ../../source/df-agg.rst:235
msgid ""
">>> from odps.df import agg\n"
">>> to_agg = agg([iris.sepalwidth, iris.sepallength], Agg, rtype='float')"
"  # 对两列调用自定义聚合\n"
">>> iris.groupby('name').agg(val=to_agg)\n"
"              name       val\n"
"0      Iris-setosa  0.682781\n"
"1  Iris-versicolor  0.466644\n"
"2   Iris-virginica  0.451427"
msgstr ""
">>> from odps.df import agg\n"
">>> to_agg = agg([iris.sepalwidth, iris.sepallength], Agg, rtype='float')"
"  # call custom aggregation on two columns\n"
">>> iris.groupby('name').agg(val=to_agg)\n"
"              name       val\n"
"0      Iris-setosa  0.682781\n"
"1  Iris-versicolor  0.466644\n"
"2   Iris-virginica  0.451427"

#: ../../source/df-agg.rst:236
msgid "要调用 ODPS 上已经存在的 UDAF，指定函数名即可。"
msgstr "Specify the function name to use an existing UDAF in MaxCompute."

#: ../../source/df-agg.rst:243
msgid ""
">>> iris.groupby('name').agg(iris.sepalwidth.agg('your_func'))  # 对单列"
"聚合\n"
">>> to_agg = agg([iris.sepalwidth, iris.sepallength], 'your_func', "
"rtype='float')\n"
">>> iris.groupby('name').agg(to_agg.rename('val'))  # 对多列聚合"
msgstr ""
">>> iris.groupby('name').agg(iris.sepalwidth.agg('your_func'))  # "
"aggregate one column\n"
">>> to_agg = agg([iris.sepalwidth, iris.sepallength], 'your_func', "
"rtype='float')\n"
">>> iris.groupby('name').agg(to_agg.rename('val'))  # aggregate multiple "
"columns"

#: ../../source/df-agg.rst:245
msgid ""
"目前，受限于 Python UDF，自定义聚合无法支持将 list / dict 类型作为初始"
"输入或最终输出结果。"
msgstr ""
"Limited by Python user-defined functions (UDFs), custom aggregations "
"cannot specify the input or output result type as the list or dict type."

#: ../../source/df-agg.rst:248
msgid "HyperLogLog 计数"
msgstr "HyperLogLog counting"

#: ../../source/df-agg.rst:250
msgid ""
"DataFrame 提供了对列进行 HyperLogLog 计数的接口 ``hll_count``，这个接口是"
"个近似的估计接口， 当数据量很大时，能较快的对数据的唯一个数进行估计。"
msgstr ""
"DataFrame provides the ``hll_count`` API to use HyperLogLog counting for "
"columns. This API estimates the unique quantity among large volumes of "
"data in a short time."

#: ../../source/df-agg.rst:253
msgid "这个接口在对比如海量用户UV进行计算时，能很快得出估计值。"
msgstr ""
"This API can quickly calculate and provide an estimate of the unique "
"visitors (UVs)."

#: ../../source/df-agg.rst:262
msgid ""
">>> df = DataFrame(pd.DataFrame({'a': np.random.randint(100000, "
"size=100000)}))\n"
">>> df.a.hll_count()\n"
"63270\n"
">>> df.a.nunique()\n"
"63250"
msgstr ""

#: ../../source/df-agg.rst:263
msgid "提供 ``splitter`` 参数会对每个字段进行分隔，再计算唯一数。"
msgstr ""
"The system provides the ``splitter`` parameter to split fields and then "
"calculate the unique quantity."

