name: PyODPS CD for PyPI

on:
  push:
    tags:
      - '*'

jobs:
  build:
    name: Build wheels on ${{ matrix.os }} for ${{ matrix.arch }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-13, windows-latest]
        arch: [auto]
        include:
          - os: ubuntu-latest
            arch: aarch64
          - os: macos-13
            arch: universal2

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Set up QEMU
        if: ${{ matrix.arch == 'aarch64' }}
        uses: docker/setup-qemu-action@v1

      - name: Build wheels
        uses: pypa/cibuildwheel@v1.11.1
        if: ${{ matrix.arch != 'universal2' }}
        env:
          CIBW_BEFORE_BUILD: git reset --hard && pip install -r requirements-wheel.txt && rm -rf pyproject.toml
          CIBW_ARCHS: ${{ matrix.arch }}
          CIBW_SKIP: pp* *-musllinux* *i686 cp35-manylinux_aarch64 cp36-manylinux_aarch64 cp39-*
          CIBW_MANYLINUX_X86_64_IMAGE: manylinux1
          CIBW_MANYLINUX_AARCH64_IMAGE: manylinux2014

      - name: "Build wheels (Python >= 3.9)"
        uses: pypa/cibuildwheel@v2.16.5
        env:
          CIBW_BEFORE_BUILD: git reset --hard && pip install -r requirements-wheel.txt && rm -rf pyproject.toml
          CIBW_ARCHS: ${{ matrix.arch }}
          CIBW_SKIP: pp* *-musllinux* *i686 cp36-* cp37-* cp38-*
          CIBW_MANYLINUX_X86_64_IMAGE: manylinux2014
          CIBW_MANYLINUX_AARCH64_IMAGE: manylinux2014

      - name: Install Python
        uses: actions/setup-python@v4
        if: ${{ startsWith(matrix.os, 'macos-') }}
        with:
          python-version: '3.7'

      - name: Build source
        if: ${{ matrix.os == 'ubuntu-latest' && matrix.arch == 'auto'}}
        run: |
          pip install -r requirements-wheel.txt
          python setup.py sdist --formats=gztar --dist-dir=./wheelhouse

      - name: Release to pypi
        shell: bash
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USER }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          if [[ "$GITHUB_REPOSITORY" == "aliyun/aliyun-odps-python-sdk" ]]; then
            PYPI_REPO="https://upload.pypi.org/legacy/"
          else
            PYPI_REPO="https://test.pypi.org/legacy/"
          fi
          echo "[distutils]"             > ~/.pypirc
          echo "index-servers ="        >> ~/.pypirc
          echo "    pypi"               >> ~/.pypirc
          echo "[pypi]"                 >> ~/.pypirc
          echo "repository=$PYPI_REPO"  >> ~/.pypirc
          python -m pip install twine
          python -m twine upload -r pypi --skip-existing wheelhouse/*
