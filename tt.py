import re
import sys
import time
import logging
from odps import ODPS
from odps.df import DataFrame
from odps.df.expr import window
from odps.udf import annotate
from odps.df import output
from odps import options
import pandas as pd
from functools import reduce
import urllib.parse
from pprint import pformat
import json

def init_console_logging():
    pd.options.display.max_columns = None
    pd.options.display.max_rows = None
    pd.options.display.width = None
    pd.options.display.precision = 10
    pd.options.display.unicode.east_asian_width = True
    pd.options.display.max_colwidth = None

    log_format = "%(asctime)s.%(msecs)03d|%(levelname)-7s|%(funcName)s@%(lineno)d|%(message)s"

    if logging.root.handlers:
        logging.root.handlers.clear()

    formatter = logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S")

    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(formatter)

    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[handler]
    )

    # Ensure no conflicting handlers are added later
    logging.getLogger().addHandler(handler)

    logging.info("Console logging initialized~")


def villa_main():
    ds = args['ds_input']
    hh = args['hh_input']
    mi = args['mi_input']
    logging.info(f'got ds={ds}, hh={hh}, mi={mi}')

    rules_raw = [
        ('数帮帮',   {'host': [
            r'new.yxsj666.fun', # https://new.yxsj666.fun:3346/upload_pdd_task
            r'jd.yxsj666.fun', # https://jd.yxsj666.fun:3346/get_tb_task_1102?flag=mmx
        ]}),
        ('左姐',    {'ip_port': [
            '************:8888', '**************:8888', '**************:8888',
            '************:9999', '**************:8888', '************:8888',
            '*************:8888', '**************:8888', '*************:10012',
            '**************:8888'
        ]}),
        ('流量助手', {'ip_port': [
            '**************:80' # http://**************/elw/dfg/binls/gfg1
        ]}),
        ('全网舆情', {'ip_port': [
            '*************:8080',
        ]}),
        ('申小豹',  {'host': [
            'jd.jingkelm.cn',
        ]}),
    ]

    ip_port_to_name = {}
    host_to_name = {}

    ip_port_to_name = {ip_port: name for name, rule in rules_raw for ip_port in rule.get('ip_port', [])}
    logging.info(f'ip_port_to_name={ip_port_to_name}')
    host_to_name = {h: name for name, rule in rules_raw for h in rule.get('host', [])}
    logging.info(f'host_to_name={host_to_name}')

    def map_originate(row) -> str:
        addr = row.get('addr')
        host = row.get('host')
        logging.info(f'host={host}')

        # Match on addr (ip:port)
        if isinstance(addr, str) and addr in ip_port_to_name:
            return ip_port_to_name[addr]

        # Match on host (domain)
        if isinstance(host, str) and host in host_to_name:
            return host_to_name[host]

        return 'not-map'

    t1 = o.get_table('sec_wb.pc_web_random_association_15mi', project='sec_wb').to_df()
    t2_raw = o.get_table('security_eco.odl_pcs_sdk_t_user_track_network_split_mi', project='sec_wb').to_df()
    t2 = t2_raw[
        (t2_raw.ds == ds) & (t2_raw.hh == hh) & (t2_raw.mi == mi) & (t2_raw.new_umid != '000000000000000000000000000000000') &
        # (t2_raw.hh=='21') & (t2_raw.mi=='00') & (t2_raw.new_umid=='CV26001c46633a2e12021077965a90453') & (t2_raw.addr=='**************:3346') & # for faster debug: 20250819, 21, 00
        (t2_raw.addr != '')][['new_umid', 'addr', 'host']].distinct()
    logging.info(f'before map_originate~')
    t2 = t2[
        t2,  # keep all existing columns
        t2.apply(map_originate, axis=1, reduce=True, names=['originate'], types=['string'])
    ]
    logging.info(f'after map_originate~')
    t2 = t2[t2.originate != 'not-map']
    logging.info(f'not-map deleted~')

    t2_pandas = t2.to_pandas()
    logging.info(f't2.head(100) of total={len(t2_pandas)}:\n{t2_pandas}')

    sub = t1[
        (t1.ds == ds) & (t1.hh == hh) & (t1.mi == mi) & (t1.cv_new_umid != '000000000000000000000000000000000') &
        (t1.sgc_userid.notnull()) & (t1.sgc_userid != '') & (t1.sgc_userid != r'\N') &
        # (t1.hh=='21') & (t1.mi=='00') & (t1.cv_new_umid=='CV26001c46633a2e12021077965a90453') & (t1.sgc_userid=='138127416') & # for faster debug: 20250819, 21, 00
        (t1.cv_new_umid.isin(t2.new_umid))
        ][['ds', 'hh', 'mi', 'sgc_userid', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid', 'time']]
    logging.info(f'sub.head(3) before deduplication:\n{sub.head(3)}')
    sample_df = sub.limit(1).to_pandas()
    if len(sample_df) == 0:
        print('未发现风险用户')
        return

    touchq_devices_sql = f"""
        select * from sec_wb.etw_chrome_ext_events 
        where ds = '{ds}'
        and lower(extension_name) like '%touch%'
        and api='blinkRequestResource'
        and page_url rlike 'id=[0-9]{{12}}'
    """
    with o.execute_sql(touchq_devices_sql).open_reader() as reader:
        touchq_devices = reader.to_pandas()
    logging.info(f'touchq_devices items=`{len(touchq_devices)}`\n{touchq_devices}')
  
    def extract_userid(url):
        if not isinstance(url, str):
            return None, 'False'
        try:
            parsed = urllib.parse.urlparse(url)
            qs = urllib.parse.parse_qs(parsed.query)
            utparam_encoded = qs.get('utparam')
            if not utparam_encoded:
                return None, 'False'
            utparam_str = urllib.parse.unquote(utparam_encoded[0])
            utparam_json = json.loads(utparam_str)
            user_id = utparam_json.get('userId')
            if user_id is not None:
                return str(user_id), 'True'
            else:
                return None, 'False'
        except Exception as e:
            logging.warning(f"failed to extract userId from {url}: {e}")
            return None, 'False'

    def generate_track(row):
        if not isinstance(row['page_url'], str) or not isinstance(row['extension_name'], str) or not isinstance(row['uuid'], str):
            return None
        return f"{row['extension_name']}\n{row['uuid']}\n{row['page_url']}"

    if len(touchq_devices) > 0:
        touchq_devices[['sgc_userid', 'verified']] = touchq_devices['page_url'].apply(
            lambda url: pd.Series(extract_userid(url))
        )
        touchq_devices['track'] = touchq_devices.apply(generate_track, axis=1)
        touchq_devices['originate'] = touchq_devices['extension_name']  # Set originate to extension_name
        touchq_devices['addr'] = ''  # Add empty addr column for consistency
        # make sure to write to the `hh`, `mi` partitions
        touchq_devices['hh'] = hh
        touchq_devices['mi'] = mi

        # Rename columns in touchq_devices to match required schema
        touchq_devices = touchq_devices.rename(columns={'new_umid': 'cv_new_umid', 'umid': 'cv_old_umid'})

        touchq_devices_df = DataFrame(touchq_devices)[['ds', 'hh', 'mi', 'sgc_userid', 'originate', 'addr', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid', 'track', 'verified']]

        result = sub.inner_join(t2, (sub.cv_new_umid == t2.new_umid))[
            ['ds', 'hh', 'mi', 'sgc_userid', 'originate', 'addr', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid']
        ]
        result['track'] = r'NA'
        result['verified'] = r'NA'
        logging.info(f'result.head(3): \n{result.head(3)}')

        logging.info(f"touchq_devices_df schema:\n{touchq_devices_df.schema}")
        logging.info(f"result schema:\n{result.schema}")


        enriched = touchq_devices_df.union(result)[['ds', 'hh', 'mi', 'sgc_userid', 'originate', 'addr', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid', 'track', 'verified']].distinct()
    else: # Process result table when touchq_devices is empty
        result = sub.inner_join(t2, (sub.cv_new_umid == t2.new_umid))[
            ['ds', 'hh', 'mi', 'sgc_userid', 'originate', 'addr', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid']
        ]
        result['track'] = r'NA'
        result['verified'] = r'NA'
        logging.info(f'result.head(3): \n{result.head(3)}')

        # Define enriched as result when touchq_devices is empty
        enriched = result[['ds', 'hh', 'mi', 'sgc_userid', 'originate', 'addr', 'itn_run', 'cv_new_umid', 'cv_old_umid', 'csid', 'track', 'verified']].distinct()

    logging.info(f'enriched result with track:\n{enriched}')
    enriched.persist('potential_risk_users_66_r2', partitions=['ds', 'hh', 'mi'], overwrite=False, lifecycle=90)


if __name__ == "__main__":
    init_console_logging()

    options.tunnel.use_instance_tunnel = True
    options.tunnel.limit_instance_tunnel = False  # 关闭limit限制，读取全部数据
    options.use_legacy_parsedate = False
    options.sql.settings = {
        "odps.sql.select.output.showcolumntype": "true",
        "odps.sql.validate.orderby.limit": "false",
        "odps.task.sql.sqa.enable": "false",
        "odps.sql.type.system.odps2": "true",
        "odps.sql.submit.mode": "script",
        "odps.sql.task.sqa.enable": "false",
        "odps.sql.type.json.enable": "true",
        "odps.sql.udf.getjsonobj.new": "true",
        "odps.isolation.session.enable": "true",
        "odps.sql.hive.compatible": "true",
        "odps.stage.mapper.split.size": 4096,
        "odps.sql.job.max.time.hours": 72,
        "odps.sql.use_odps2_extension": "true",
        "odps.sql.python.version": "cp37",
        "odps.sql.enable.stats.collection": "false",
        "odps.merge.memory.quota.mb": 12286,
    }

    start_time = time.perf_counter()
    logging.info("Starting main function execution")

    try:
        logging.info('$' * 200)
        villa_main()
        logging.info('$' * 200)
        execution_time = time.perf_counter() - start_time
        logging.info(f"Main function completed in {execution_time:.3f} seconds")
    except Exception as e:
        execution_time = time.perf_counter() - start_time
        logging.error(f"Main function failed after {execution_time:.3f} seconds, error: {e}")
        raise
