# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cupid_task_service.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import service as _service
from google.protobuf import service_reflection
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import cupidtaskparam_pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='cupid_task_service.proto',
  package='apsara.odps.cupid.protocol',
  serialized_pb=_b('\n\x18\x63upid_task_service.proto\x12\x1a\x61psara.odps.cupid.protocol\x1a\x14\x63upidtaskparam.proto\"m\n\x0eTableInputInfo\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x11\n\ttableName\x18\x02 \x01(\t\x12\x0f\n\x07\x63olumns\x18\x03 \x01(\t\x12\x10\n\x08partSpec\x18\x04 \x01(\t\x12\x10\n\x08\x62ucketId\x18\x05 \x01(\x05\"\xd4\x01\n\x12SplitTablesRequest\x12\x12\n\nlookupName\x18\x01 \x01(\t\x12\x11\n\tsplitSize\x18\x02 \x01(\x05\x12\x12\n\nsplitCount\x18\x03 \x01(\x05\x12\x43\n\x0ftableInputInfos\x18\x04 \x03(\x0b\x32*.apsara.odps.cupid.protocol.TableInputInfo\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x16\n\x0e\x61llowNoColumns\x18\x06 \x01(\x08\x12\x18\n\x10requireSplitMeta\x18\x07 \x01(\x08\"/\n\x13SplitTablesResponse\x12\x18\n\x10inputTableHandle\x18\x01 \x01(\t\"]\n\x11WriteTableRequest\x12\x12\n\nlookupName\x18\x01 \x01(\t\x12\x13\n\x0bprojectName\x18\x02 \x01(\t\x12\x11\n\ttableName\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\"/\n\x12WriteTableResponse\x12\x19\n\x11outputTableHandle\x18\x01 \x01(\t\"\xd8\x01\n\x12\x43ommitTableRequest\x12\x12\n\nlookupName\x18\x01 \x01(\t\x12\x19\n\x11outputTableHandle\x18\x02 \x01(\t\x12\x13\n\x0bprojectName\x18\x03 \x01(\t\x12\x11\n\ttableName\x18\x04 \x01(\t\x12\x13\n\x0bisOverWrite\x18\x05 \x01(\x08\x12\x11\n\tpartSpecs\x18\x06 \x03(\t\x12\x43\n\x0f\x63ommitFileLists\x18\x07 \x03(\x0b\x32*.apsara.odps.cupid.protocol.CommitFileList\"\x15\n\x13\x43ommitTableResponse\"3\n\tTableInfo\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x11\n\ttableName\x18\x02 \x01(\t\"\x8c\x01\n\x13GetTableMetaRequest\x12\x12\n\nlookupName\x18\x01 \x01(\t\x12\x38\n\ttableInfo\x18\x02 \x01(\x0b\x32%.apsara.odps.cupid.protocol.TableInfo\x12\x13\n\x0bneedContent\x18\x03 \x01(\x08\x12\x12\n\nuploadFile\x18\x04 \x01(\t\"O\n\x14GetTableMetaResponse\x12\x1a\n\x12getTableMetaHandle\x18\x01 \x01(\t\x12\x1b\n\x13getTableMetaContent\x18\x02 \x01(\t\"I\n\x18\x43loseOutputHandleRequest\x12\x12\n\nlookupName\x18\x01 \x01(\t\x12\x19\n\x11outputTableHandle\x18\x02 \x01(\t\"\x1b\n\x19\x43loseOutputHandleResponse\"\'\n\tClusterKv\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"N\n\x1bPutOrCreateClusterKvRequest\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"\x1e\n\x1cPutOrCreateClusterKvResponse\":\n\x16\x44\x65leteClusterKvRequest\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\"\x19\n\x17\x44\x65leteClusterKvResponse\"7\n\x13GetClusterKvRequest\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\"%\n\x14GetClusterKvResponse\x12\r\n\x05value\x18\x01 \x01(\t\"C\n\x1cListByPrefixClusterKvRequest\x12\x13\n\x0bprojectName\x18\x01 \x01(\t\x12\x0e\n\x06prefix\x18\x02 \x01(\t\"Y\n\x1dListByPrefixClusterKvResponse\x12\x38\n\tclusterKv\x18\x01 \x03(\x0b\x32%.apsara.odps.cupid.protocol.ClusterKv\"(\n\x17\x43upidProxyTokenResponse\x12\r\n\x05token\x18\x01 \x01(\t\"/\n\x19\x43upidProxyAppNamesRequest\x12\x12\n\ninstanceId\x18\x01 \x01(\t\"+\n\x1a\x43upidProxyAppNamesResponse\x12\r\n\x05names\x18\x01 \x03(\t2\xe7\n\n\x10\x43upidTaskService\x12n\n\x0bSplitTables\x12..apsara.odps.cupid.protocol.SplitTablesRequest\x1a/.apsara.odps.cupid.protocol.SplitTablesResponse\x12k\n\nWriteTable\x12-.apsara.odps.cupid.protocol.WriteTableRequest\x1a..apsara.odps.cupid.protocol.WriteTableResponse\x12n\n\x0b\x43ommitTable\x12..apsara.odps.cupid.protocol.CommitTableRequest\x1a/.apsara.odps.cupid.protocol.CommitTableResponse\x12\x80\x01\n\x11\x43loseOutputHandle\x12\x34.apsara.odps.cupid.protocol.CloseOutputHandleRequest\x1a\x35.apsara.odps.cupid.protocol.CloseOutputHandleResponse\x12\x89\x01\n\x14PutOrCreateClusterKv\x12\x37.apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest\x1a\x38.apsara.odps.cupid.protocol.PutOrCreateClusterKvResponse\x12z\n\x0f\x44\x65leteClusterKv\x12\x32.apsara.odps.cupid.protocol.DeleteClusterKvRequest\x1a\x33.apsara.odps.cupid.protocol.DeleteClusterKvResponse\x12q\n\x0cGetClusterKv\x12/.apsara.odps.cupid.protocol.GetClusterKvRequest\x1a\x30.apsara.odps.cupid.protocol.GetClusterKvResponse\x12\x8c\x01\n\x15ListByPrefixClusterKv\x12\x38.apsara.odps.cupid.protocol.ListByPrefixClusterKvRequest\x1a\x39.apsara.odps.cupid.protocol.ListByPrefixClusterKvResponse\x12q\n\x0cGetTableMeta\x12/.apsara.odps.cupid.protocol.GetTableMetaRequest\x1a\x30.apsara.odps.cupid.protocol.GetTableMetaResponse\x12}\n\x12GetCupidProxyToken\x12\x32.apsara.odps.cupid.protocol.CupidProxyTokenRequest\x1a\x33.apsara.odps.cupid.protocol.CupidProxyTokenResponse\x12\x86\x01\n\x15GetCupidProxyAppNames\x12\x35.apsara.odps.cupid.protocol.CupidProxyAppNamesRequest\x1a\x36.apsara.odps.cupid.protocol.CupidProxyAppNamesResponseB B\x15\x43upidTaskServiceProto\x80\x01\x01\x88\x01\x01\x90\x01\x01')
  ,
  dependencies=[cupidtaskparam_pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_TABLEINPUTINFO = _descriptor.Descriptor(
  name='TableInputInfo',
  full_name='apsara.odps.cupid.protocol.TableInputInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.TableInputInfo.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.TableInputInfo.tableName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='columns', full_name='apsara.odps.cupid.protocol.TableInputInfo.columns', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpec', full_name='apsara.odps.cupid.protocol.TableInputInfo.partSpec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='bucketId', full_name='apsara.odps.cupid.protocol.TableInputInfo.bucketId', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=78,
  serialized_end=187,
)


_SPLITTABLESREQUEST = _descriptor.Descriptor(
  name='SplitTablesRequest',
  full_name='apsara.odps.cupid.protocol.SplitTablesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lookupName', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.lookupName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitSize', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.splitSize', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitCount', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.splitCount', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableInputInfos', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.tableInputInfos', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='allowNoColumns', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.allowNoColumns', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='requireSplitMeta', full_name='apsara.odps.cupid.protocol.SplitTablesRequest.requireSplitMeta', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=190,
  serialized_end=402,
)


_SPLITTABLESRESPONSE = _descriptor.Descriptor(
  name='SplitTablesResponse',
  full_name='apsara.odps.cupid.protocol.SplitTablesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inputTableHandle', full_name='apsara.odps.cupid.protocol.SplitTablesResponse.inputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=404,
  serialized_end=451,
)


_WRITETABLEREQUEST = _descriptor.Descriptor(
  name='WriteTableRequest',
  full_name='apsara.odps.cupid.protocol.WriteTableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lookupName', full_name='apsara.odps.cupid.protocol.WriteTableRequest.lookupName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.WriteTableRequest.projectName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.WriteTableRequest.tableName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='apsara.odps.cupid.protocol.WriteTableRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=546,
)


_WRITETABLERESPONSE = _descriptor.Descriptor(
  name='WriteTableResponse',
  full_name='apsara.odps.cupid.protocol.WriteTableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='outputTableHandle', full_name='apsara.odps.cupid.protocol.WriteTableResponse.outputTableHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=548,
  serialized_end=595,
)


_COMMITTABLEREQUEST = _descriptor.Descriptor(
  name='CommitTableRequest',
  full_name='apsara.odps.cupid.protocol.CommitTableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lookupName', full_name='apsara.odps.cupid.protocol.CommitTableRequest.lookupName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='outputTableHandle', full_name='apsara.odps.cupid.protocol.CommitTableRequest.outputTableHandle', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.CommitTableRequest.projectName', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.CommitTableRequest.tableName', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isOverWrite', full_name='apsara.odps.cupid.protocol.CommitTableRequest.isOverWrite', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpecs', full_name='apsara.odps.cupid.protocol.CommitTableRequest.partSpecs', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='commitFileLists', full_name='apsara.odps.cupid.protocol.CommitTableRequest.commitFileLists', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=598,
  serialized_end=814,
)


_COMMITTABLERESPONSE = _descriptor.Descriptor(
  name='CommitTableResponse',
  full_name='apsara.odps.cupid.protocol.CommitTableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=816,
  serialized_end=837,
)


_TABLEINFO = _descriptor.Descriptor(
  name='TableInfo',
  full_name='apsara.odps.cupid.protocol.TableInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.TableInfo.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableName', full_name='apsara.odps.cupid.protocol.TableInfo.tableName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=839,
  serialized_end=890,
)


_GETTABLEMETAREQUEST = _descriptor.Descriptor(
  name='GetTableMetaRequest',
  full_name='apsara.odps.cupid.protocol.GetTableMetaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lookupName', full_name='apsara.odps.cupid.protocol.GetTableMetaRequest.lookupName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tableInfo', full_name='apsara.odps.cupid.protocol.GetTableMetaRequest.tableInfo', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='needContent', full_name='apsara.odps.cupid.protocol.GetTableMetaRequest.needContent', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='uploadFile', full_name='apsara.odps.cupid.protocol.GetTableMetaRequest.uploadFile', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=893,
  serialized_end=1033,
)


_GETTABLEMETARESPONSE = _descriptor.Descriptor(
  name='GetTableMetaResponse',
  full_name='apsara.odps.cupid.protocol.GetTableMetaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='getTableMetaHandle', full_name='apsara.odps.cupid.protocol.GetTableMetaResponse.getTableMetaHandle', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='getTableMetaContent', full_name='apsara.odps.cupid.protocol.GetTableMetaResponse.getTableMetaContent', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1035,
  serialized_end=1114,
)


_CLOSEOUTPUTHANDLEREQUEST = _descriptor.Descriptor(
  name='CloseOutputHandleRequest',
  full_name='apsara.odps.cupid.protocol.CloseOutputHandleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lookupName', full_name='apsara.odps.cupid.protocol.CloseOutputHandleRequest.lookupName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='outputTableHandle', full_name='apsara.odps.cupid.protocol.CloseOutputHandleRequest.outputTableHandle', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1116,
  serialized_end=1189,
)


_CLOSEOUTPUTHANDLERESPONSE = _descriptor.Descriptor(
  name='CloseOutputHandleResponse',
  full_name='apsara.odps.cupid.protocol.CloseOutputHandleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1191,
  serialized_end=1218,
)


_CLUSTERKV = _descriptor.Descriptor(
  name='ClusterKv',
  full_name='apsara.odps.cupid.protocol.ClusterKv',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.ClusterKv.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.ClusterKv.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1220,
  serialized_end=1259,
)


_PUTORCREATECLUSTERKVREQUEST = _descriptor.Descriptor(
  name='PutOrCreateClusterKvRequest',
  full_name='apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest.value', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1261,
  serialized_end=1339,
)


_PUTORCREATECLUSTERKVRESPONSE = _descriptor.Descriptor(
  name='PutOrCreateClusterKvResponse',
  full_name='apsara.odps.cupid.protocol.PutOrCreateClusterKvResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1341,
  serialized_end=1371,
)


_DELETECLUSTERKVREQUEST = _descriptor.Descriptor(
  name='DeleteClusterKvRequest',
  full_name='apsara.odps.cupid.protocol.DeleteClusterKvRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.DeleteClusterKvRequest.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.DeleteClusterKvRequest.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1373,
  serialized_end=1431,
)


_DELETECLUSTERKVRESPONSE = _descriptor.Descriptor(
  name='DeleteClusterKvResponse',
  full_name='apsara.odps.cupid.protocol.DeleteClusterKvResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1433,
  serialized_end=1458,
)


_GETCLUSTERKVREQUEST = _descriptor.Descriptor(
  name='GetClusterKvRequest',
  full_name='apsara.odps.cupid.protocol.GetClusterKvRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.GetClusterKvRequest.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.GetClusterKvRequest.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1460,
  serialized_end=1515,
)


_GETCLUSTERKVRESPONSE = _descriptor.Descriptor(
  name='GetClusterKvResponse',
  full_name='apsara.odps.cupid.protocol.GetClusterKvResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.GetClusterKvResponse.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1517,
  serialized_end=1554,
)


_LISTBYPREFIXCLUSTERKVREQUEST = _descriptor.Descriptor(
  name='ListByPrefixClusterKvRequest',
  full_name='apsara.odps.cupid.protocol.ListByPrefixClusterKvRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectName', full_name='apsara.odps.cupid.protocol.ListByPrefixClusterKvRequest.projectName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='prefix', full_name='apsara.odps.cupid.protocol.ListByPrefixClusterKvRequest.prefix', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1556,
  serialized_end=1623,
)


_LISTBYPREFIXCLUSTERKVRESPONSE = _descriptor.Descriptor(
  name='ListByPrefixClusterKvResponse',
  full_name='apsara.odps.cupid.protocol.ListByPrefixClusterKvResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='clusterKv', full_name='apsara.odps.cupid.protocol.ListByPrefixClusterKvResponse.clusterKv', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1625,
  serialized_end=1714,
)


_CUPIDPROXYTOKENRESPONSE = _descriptor.Descriptor(
  name='CupidProxyTokenResponse',
  full_name='apsara.odps.cupid.protocol.CupidProxyTokenResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='token', full_name='apsara.odps.cupid.protocol.CupidProxyTokenResponse.token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1716,
  serialized_end=1756,
)


_CUPIDPROXYAPPNAMESREQUEST = _descriptor.Descriptor(
  name='CupidProxyAppNamesRequest',
  full_name='apsara.odps.cupid.protocol.CupidProxyAppNamesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.CupidProxyAppNamesRequest.instanceId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1758,
  serialized_end=1805,
)


_CUPIDPROXYAPPNAMESRESPONSE = _descriptor.Descriptor(
  name='CupidProxyAppNamesResponse',
  full_name='apsara.odps.cupid.protocol.CupidProxyAppNamesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='names', full_name='apsara.odps.cupid.protocol.CupidProxyAppNamesResponse.names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1807,
  serialized_end=1850,
)

_SPLITTABLESREQUEST.fields_by_name['tableInputInfos'].message_type = _TABLEINPUTINFO
_COMMITTABLEREQUEST.fields_by_name['commitFileLists'].message_type = cupidtaskparam_pb2._COMMITFILELIST
_GETTABLEMETAREQUEST.fields_by_name['tableInfo'].message_type = _TABLEINFO
_LISTBYPREFIXCLUSTERKVRESPONSE.fields_by_name['clusterKv'].message_type = _CLUSTERKV
DESCRIPTOR.message_types_by_name['TableInputInfo'] = _TABLEINPUTINFO
DESCRIPTOR.message_types_by_name['SplitTablesRequest'] = _SPLITTABLESREQUEST
DESCRIPTOR.message_types_by_name['SplitTablesResponse'] = _SPLITTABLESRESPONSE
DESCRIPTOR.message_types_by_name['WriteTableRequest'] = _WRITETABLEREQUEST
DESCRIPTOR.message_types_by_name['WriteTableResponse'] = _WRITETABLERESPONSE
DESCRIPTOR.message_types_by_name['CommitTableRequest'] = _COMMITTABLEREQUEST
DESCRIPTOR.message_types_by_name['CommitTableResponse'] = _COMMITTABLERESPONSE
DESCRIPTOR.message_types_by_name['TableInfo'] = _TABLEINFO
DESCRIPTOR.message_types_by_name['GetTableMetaRequest'] = _GETTABLEMETAREQUEST
DESCRIPTOR.message_types_by_name['GetTableMetaResponse'] = _GETTABLEMETARESPONSE
DESCRIPTOR.message_types_by_name['CloseOutputHandleRequest'] = _CLOSEOUTPUTHANDLEREQUEST
DESCRIPTOR.message_types_by_name['CloseOutputHandleResponse'] = _CLOSEOUTPUTHANDLERESPONSE
DESCRIPTOR.message_types_by_name['ClusterKv'] = _CLUSTERKV
DESCRIPTOR.message_types_by_name['PutOrCreateClusterKvRequest'] = _PUTORCREATECLUSTERKVREQUEST
DESCRIPTOR.message_types_by_name['PutOrCreateClusterKvResponse'] = _PUTORCREATECLUSTERKVRESPONSE
DESCRIPTOR.message_types_by_name['DeleteClusterKvRequest'] = _DELETECLUSTERKVREQUEST
DESCRIPTOR.message_types_by_name['DeleteClusterKvResponse'] = _DELETECLUSTERKVRESPONSE
DESCRIPTOR.message_types_by_name['GetClusterKvRequest'] = _GETCLUSTERKVREQUEST
DESCRIPTOR.message_types_by_name['GetClusterKvResponse'] = _GETCLUSTERKVRESPONSE
DESCRIPTOR.message_types_by_name['ListByPrefixClusterKvRequest'] = _LISTBYPREFIXCLUSTERKVREQUEST
DESCRIPTOR.message_types_by_name['ListByPrefixClusterKvResponse'] = _LISTBYPREFIXCLUSTERKVRESPONSE
DESCRIPTOR.message_types_by_name['CupidProxyTokenResponse'] = _CUPIDPROXYTOKENRESPONSE
DESCRIPTOR.message_types_by_name['CupidProxyAppNamesRequest'] = _CUPIDPROXYAPPNAMESREQUEST
DESCRIPTOR.message_types_by_name['CupidProxyAppNamesResponse'] = _CUPIDPROXYAPPNAMESRESPONSE

TableInputInfo = _reflection.GeneratedProtocolMessageType('TableInputInfo', (_message.Message,), dict(
  DESCRIPTOR = _TABLEINPUTINFO,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.TableInputInfo)
  ))
_sym_db.RegisterMessage(TableInputInfo)

SplitTablesRequest = _reflection.GeneratedProtocolMessageType('SplitTablesRequest', (_message.Message,), dict(
  DESCRIPTOR = _SPLITTABLESREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.SplitTablesRequest)
  ))
_sym_db.RegisterMessage(SplitTablesRequest)

SplitTablesResponse = _reflection.GeneratedProtocolMessageType('SplitTablesResponse', (_message.Message,), dict(
  DESCRIPTOR = _SPLITTABLESRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.SplitTablesResponse)
  ))
_sym_db.RegisterMessage(SplitTablesResponse)

WriteTableRequest = _reflection.GeneratedProtocolMessageType('WriteTableRequest', (_message.Message,), dict(
  DESCRIPTOR = _WRITETABLEREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.WriteTableRequest)
  ))
_sym_db.RegisterMessage(WriteTableRequest)

WriteTableResponse = _reflection.GeneratedProtocolMessageType('WriteTableResponse', (_message.Message,), dict(
  DESCRIPTOR = _WRITETABLERESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.WriteTableResponse)
  ))
_sym_db.RegisterMessage(WriteTableResponse)

CommitTableRequest = _reflection.GeneratedProtocolMessageType('CommitTableRequest', (_message.Message,), dict(
  DESCRIPTOR = _COMMITTABLEREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitTableRequest)
  ))
_sym_db.RegisterMessage(CommitTableRequest)

CommitTableResponse = _reflection.GeneratedProtocolMessageType('CommitTableResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMITTABLERESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitTableResponse)
  ))
_sym_db.RegisterMessage(CommitTableResponse)

TableInfo = _reflection.GeneratedProtocolMessageType('TableInfo', (_message.Message,), dict(
  DESCRIPTOR = _TABLEINFO,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.TableInfo)
  ))
_sym_db.RegisterMessage(TableInfo)

GetTableMetaRequest = _reflection.GeneratedProtocolMessageType('GetTableMetaRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTABLEMETAREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetTableMetaRequest)
  ))
_sym_db.RegisterMessage(GetTableMetaRequest)

GetTableMetaResponse = _reflection.GeneratedProtocolMessageType('GetTableMetaResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTABLEMETARESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetTableMetaResponse)
  ))
_sym_db.RegisterMessage(GetTableMetaResponse)

CloseOutputHandleRequest = _reflection.GeneratedProtocolMessageType('CloseOutputHandleRequest', (_message.Message,), dict(
  DESCRIPTOR = _CLOSEOUTPUTHANDLEREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CloseOutputHandleRequest)
  ))
_sym_db.RegisterMessage(CloseOutputHandleRequest)

CloseOutputHandleResponse = _reflection.GeneratedProtocolMessageType('CloseOutputHandleResponse', (_message.Message,), dict(
  DESCRIPTOR = _CLOSEOUTPUTHANDLERESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CloseOutputHandleResponse)
  ))
_sym_db.RegisterMessage(CloseOutputHandleResponse)

ClusterKv = _reflection.GeneratedProtocolMessageType('ClusterKv', (_message.Message,), dict(
  DESCRIPTOR = _CLUSTERKV,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ClusterKv)
  ))
_sym_db.RegisterMessage(ClusterKv)

PutOrCreateClusterKvRequest = _reflection.GeneratedProtocolMessageType('PutOrCreateClusterKvRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTORCREATECLUSTERKVREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.PutOrCreateClusterKvRequest)
  ))
_sym_db.RegisterMessage(PutOrCreateClusterKvRequest)

PutOrCreateClusterKvResponse = _reflection.GeneratedProtocolMessageType('PutOrCreateClusterKvResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTORCREATECLUSTERKVRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.PutOrCreateClusterKvResponse)
  ))
_sym_db.RegisterMessage(PutOrCreateClusterKvResponse)

DeleteClusterKvRequest = _reflection.GeneratedProtocolMessageType('DeleteClusterKvRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETECLUSTERKVREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DeleteClusterKvRequest)
  ))
_sym_db.RegisterMessage(DeleteClusterKvRequest)

DeleteClusterKvResponse = _reflection.GeneratedProtocolMessageType('DeleteClusterKvResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETECLUSTERKVRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DeleteClusterKvResponse)
  ))
_sym_db.RegisterMessage(DeleteClusterKvResponse)

GetClusterKvRequest = _reflection.GeneratedProtocolMessageType('GetClusterKvRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCLUSTERKVREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetClusterKvRequest)
  ))
_sym_db.RegisterMessage(GetClusterKvRequest)

GetClusterKvResponse = _reflection.GeneratedProtocolMessageType('GetClusterKvResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCLUSTERKVRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetClusterKvResponse)
  ))
_sym_db.RegisterMessage(GetClusterKvResponse)

ListByPrefixClusterKvRequest = _reflection.GeneratedProtocolMessageType('ListByPrefixClusterKvRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTBYPREFIXCLUSTERKVREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ListByPrefixClusterKvRequest)
  ))
_sym_db.RegisterMessage(ListByPrefixClusterKvRequest)

ListByPrefixClusterKvResponse = _reflection.GeneratedProtocolMessageType('ListByPrefixClusterKvResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTBYPREFIXCLUSTERKVRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ListByPrefixClusterKvResponse)
  ))
_sym_db.RegisterMessage(ListByPrefixClusterKvResponse)

CupidProxyTokenResponse = _reflection.GeneratedProtocolMessageType('CupidProxyTokenResponse', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDPROXYTOKENRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidProxyTokenResponse)
  ))
_sym_db.RegisterMessage(CupidProxyTokenResponse)

CupidProxyAppNamesRequest = _reflection.GeneratedProtocolMessageType('CupidProxyAppNamesRequest', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDPROXYAPPNAMESREQUEST,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidProxyAppNamesRequest)
  ))
_sym_db.RegisterMessage(CupidProxyAppNamesRequest)

CupidProxyAppNamesResponse = _reflection.GeneratedProtocolMessageType('CupidProxyAppNamesResponse', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDPROXYAPPNAMESRESPONSE,
  __module__ = 'cupid_task_service_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidProxyAppNamesResponse)
  ))
_sym_db.RegisterMessage(CupidProxyAppNamesResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('B\025CupidTaskServiceProto\200\001\001\210\001\001\220\001\001'))

_CUPIDTASKSERVICE = _descriptor.ServiceDescriptor(
  name='CupidTaskService',
  full_name='apsara.odps.cupid.protocol.CupidTaskService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=1853,
  serialized_end=3236,
  methods=[
  _descriptor.MethodDescriptor(
    name='SplitTables',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.SplitTables',
    index=0,
    containing_service=None,
    input_type=_SPLITTABLESREQUEST,
    output_type=_SPLITTABLESRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='WriteTable',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.WriteTable',
    index=1,
    containing_service=None,
    input_type=_WRITETABLEREQUEST,
    output_type=_WRITETABLERESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CommitTable',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.CommitTable',
    index=2,
    containing_service=None,
    input_type=_COMMITTABLEREQUEST,
    output_type=_COMMITTABLERESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CloseOutputHandle',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.CloseOutputHandle',
    index=3,
    containing_service=None,
    input_type=_CLOSEOUTPUTHANDLEREQUEST,
    output_type=_CLOSEOUTPUTHANDLERESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='PutOrCreateClusterKv',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.PutOrCreateClusterKv',
    index=4,
    containing_service=None,
    input_type=_PUTORCREATECLUSTERKVREQUEST,
    output_type=_PUTORCREATECLUSTERKVRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteClusterKv',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.DeleteClusterKv',
    index=5,
    containing_service=None,
    input_type=_DELETECLUSTERKVREQUEST,
    output_type=_DELETECLUSTERKVRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetClusterKv',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.GetClusterKv',
    index=6,
    containing_service=None,
    input_type=_GETCLUSTERKVREQUEST,
    output_type=_GETCLUSTERKVRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListByPrefixClusterKv',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.ListByPrefixClusterKv',
    index=7,
    containing_service=None,
    input_type=_LISTBYPREFIXCLUSTERKVREQUEST,
    output_type=_LISTBYPREFIXCLUSTERKVRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetTableMeta',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.GetTableMeta',
    index=8,
    containing_service=None,
    input_type=_GETTABLEMETAREQUEST,
    output_type=_GETTABLEMETARESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetCupidProxyToken',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.GetCupidProxyToken',
    index=9,
    containing_service=None,
    input_type=cupidtaskparam_pb2._CUPIDPROXYTOKENREQUEST,
    output_type=_CUPIDPROXYTOKENRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetCupidProxyAppNames',
    full_name='apsara.odps.cupid.protocol.CupidTaskService.GetCupidProxyAppNames',
    index=10,
    containing_service=None,
    input_type=_CUPIDPROXYAPPNAMESREQUEST,
    output_type=_CUPIDPROXYAPPNAMESRESPONSE,
    options=None,
  ),
])

CupidTaskService = service_reflection.GeneratedServiceType('CupidTaskService', (_service.Service,), dict(
  DESCRIPTOR = _CUPIDTASKSERVICE,
  __module__ = 'cupid_task_service_pb2'
  ))

CupidTaskService_Stub = service_reflection.GeneratedServiceStubType('CupidTaskService_Stub', (CupidTaskService,), dict(
  DESCRIPTOR = _CUPIDTASKSERVICE,
  __module__ = 'cupid_task_service_pb2'
  ))


# @@protoc_insertion_point(module_scope)
