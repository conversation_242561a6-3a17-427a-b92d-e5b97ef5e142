# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-10 15:09+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-basic.rst:5
msgid "基本概念"
msgstr "Concepts"

#: ../../source/df-basic.rst:7
msgid ""
"在使用 DataFrame 时，你需要了解三个对象上的操作：\\ ``Collection``\\ (``"
"DataFrame``) ，\\ ``Sequence``\\ ，\\ ``Scalar``\\ 。 这三个对象分别表示"
"表结构（或者二维结构）、列（一维结构）、标量。需要注意的是，这些对象仅在"
"使用 Pandas 数据创建后会包含实际数据， 而在 ODPS 表上创建的对象中并不包含"
"实际的数据，而仅仅包含对这些数据的操作，实质的存储和计算会在 ODPS 中进行"
"。"
msgstr ""
"When using DataFrame, you need to know the operations on the following "
"objects: \\ ``Collection``\\ (``DataFrame``), \\ ``Sequence``\\, and \\ "
"``Scalar``\\. These three objects respectively represent table structures"
" (two-dimensional structures), columns (one-dimensional structures), and "
"scalars, and are also known as expr objects in PyODPS. Note that all "
"storage and calculations are performed in MaxCompute for objects created "
"from MaxCompute tables."

#: ../../source/df-basic.rst:12
msgid "创建 DataFrame"
msgstr "Create a DataFrame object"

#: ../../source/df-basic.rst:14
msgid ""
"通常情况下，你唯一需要直接创建的 Collection 对象是 :class:`DataFrame`，"
"这一对象用于引用数据源，可能是一个 ODPS 表， ODPS 分区，Pandas DataFrame"
"或sqlalchemy.Table（数据库表）。 使用这几种数据源时，相关的操作相同，这"
"意味着你可以不更改数据处理的代码，仅仅修改输入/输出的指向， 便可以简单地"
"将小数据量上本地测试运行的代码迁移到 ODPS 上，而迁移的正确性由 PyODPS 来"
"保证。"
msgstr ""
"The only Collection object you need to directly create is "
":class:`DataFrame`. This object is used to reference the data source, "
"which may be a MaxCompute table, MaxCompute partition, pandas DataFrame, "
"or sqalchemy.Table (database table). The data processing code can be "
"exactly the same when performing similar operations on different data "
"sources. This means you can migrate your local test code to MaxCompute by"
" only changing the data source used for creating DataFrame objects."

#: ../../source/df-basic.rst:19
msgid ""
"创建 DataFrame 非常简单，只需将 Table 对象、 pandas DataFrame 对象或者 "
"sqlalchemy Table 对象传入即可。"
msgstr ""
"Creating a DataFrame object is as simple as passing in a Table object, a "
"pandas DataFrame object, or a sqlalchemy Table object."

#: ../../source/df-basic.rst:21
msgid ""
">>> from odps.df import DataFrame\n"
">>>\n"
">>> # 从 ODPS 表创建\n"
">>> iris = DataFrame(o.get_table('pyodps_iris'))\n"
">>> iris2 = o.get_table('pyodps_iris').to_df()  # 使用表的to_df方法\n"
">>>\n"
">>> # 从 ODPS 分区创建\n"
">>> pt_df = "
"DataFrame(o.get_table('partitioned_table').get_partition('pt=20171111'))"
"\n"
">>> pt_df2 = o.get_table('partitioned_table').get_partition('pt=20171111'"
").to_df()  # 使用分区的to_df方法\n"
">>>\n"
">>> # 从 Pandas DataFrame 创建\n"
">>> import pandas as pd\n"
">>> import numpy as np\n"
">>> df = DataFrame(pd.DataFrame(np.arange(9).reshape(3, 3), "
"columns=list('abc')))\n"
">>>\n"
">>> # 从 sqlalchemy Table 创建\n"
">>> engine = "
"sqlalchemy.create_engine('mysql://root:123456@localhost/movielens')\n"
">>> metadata = sqlalchemy.MetaData(bind=engine) # 需要绑定到engine\n"
">>> table = sqlalchemy.Table('top_users', metadata, extend_existing=True,"
" autoload=True)\n"
">>> users = DataFrame(table)"
msgstr ""
">>> from odps.df import DataFrame\n"
">>>\n"
">>> # create from a MaxCompute table\n"
">>> iris = DataFrame(o.get_table('pyodps_iris'))\n"
">>> iris2 = o.get_table('pyodps_iris').to_df()  # use to_df() method of a"
" table\n"
">>>\n"
">>> # create from a MaxCompute table partition\n"
">>> pt_df = "
"DataFrame(o.get_table('partitioned_table').get_partition('pt=20171111'))"
"\n"
">>> pt_df2 = "
"o.get_table('partitioned_table').get_partition('pt=20171111').to_df()  # "
"use to_df() method of a partition\n"
">>>\n"
">>> # create from a Pandas DataFrame\n"
">>> import pandas as pd\n"
">>> import numpy as np\n"
">>> df = DataFrame(pd.DataFrame(np.arange(9).reshape(3, 3), "
"columns=list('abc')))\n"
">>>\n"
">>> # create from a sqlalchemy table\n"
">>> engine = "
"sqlalchemy.create_engine('mysql://root:123456@localhost/movielens')\n"
">>> metadata = sqlalchemy.MetaData(bind=engine) # need to bind an engine\n"
">>> table = sqlalchemy.Table('top_users', metadata, extend_existing=True,"
" autoload=True)\n"
">>> users = DataFrame(table)"

#: ../../source/df-basic.rst:44
msgid ""
"在用 pandas DataFrame 初始化时，对于 numpy object 类型或者 string 类型，"
"PyODPS DataFrame 会尝试推断类型， 如果一整列都为空，则会报错。这时，用户"
"可以指定 `unknown_as_string` 为True，会将这些列指定为string类型。 用户也"
"可以指定 as_type 参数。若类型为基本类型，会在创建 PyODPS DataFrame 时进行"
"强制类型转换。 如果 Pandas DataFrame 中包含 list 或者 dict 列，该列的类型"
"不会被推断，必须手动使用 as_type 指定。 as_type 参数类型必须是dict。"
msgstr ""
"When initializing a DataFrame object by using a pandas DataFrame object, "
"PyODPS attempts to determine the type of the column if it is a numpy "
"object type or string sequence. If the value of an entire column is null,"
" an error occurs. You can set `unknown_as_string` to True, which sets the"
" columns to string type. You can also specify the as_type parameter. If "
"the specified type is a primitive type, type casting is performed when "
"the PyODPS DataFrame object is created. If the pandas DataFrame object "
"contains a list or dict column, the type of this column is not inferred "
"but must be manually specified using as_type. The as_type parameter must "
"be a dict type."

#: ../../source/df-basic.rst:50
msgid ""
">>> df2 = DataFrame(df, unknown_as_string=True, as_type={'null_col2': "
"'float'})\n"
">>> df2.dtypes\n"
"odps.Schema {\n"
"  sepallength           float64\n"
"  sepalwidth            float64\n"
"  petallength           float64\n"
"  petalwidth            float64\n"
"  name                  string\n"
"  null_col1             string   # 无法识别，通过unknown_as_string设置成"
"string类型\n"
"  null_col2             float64  # 强制转换成float类型\n"
"}\n"
">>> df4 = DataFrame(df3, as_type={'list_col': 'list<int64>'})\n"
">>> df4.dtypes\n"
"odps.Schema {\n"
"  id        int64\n"
"  list_col  list<int64>  # 无法识别且无法自动转换，通过 as_type 设置\n"
"}"
msgstr ""
">>> df2 = DataFrame(df, unknown_as_string=True, as_type={'null_col2': "
"'float'})\n"
">>> df2.dtypes\n"
"odps.Schema {\n"
"  sepallength           float64\n"
"  sepalwidth            float64\n"
"  petallength           float64\n"
"  petalwidth            float64\n"
"  name                  string\n"
"  null_col1             string   # cannot be identified, configured as "
"string via unknown_as_string argument\n"
"  null_col2             float64  # forcefully converted to float\n"
"}\n"
">>> df4 = DataFrame(df3, as_type={'list_col': 'list<int64>'})\n"
">>> df4.dtypes\n"
"odps.Schema {\n"
"  id        int64\n"
"  list_col  list<int64>  # cannot be identified or converted, configured "
"via as_type argument\n"
"}"

#: ../../source/df-basic.rst:71
msgid "Sequence"
msgstr "Sequence"

#: ../../source/df-basic.rst:73
msgid ""
":class:`SequenceExpr` 代表了二维数据集中的一列。你不应当手动创建 "
"SequenceExpr，而应当从一个 Collection 中获取。"
msgstr ""
":class:`SequenceExpr` represents a column in a two-dimensional data set. "
"You are not allowed to manually create SequenceExpr objects. Instead, you"
" can retrieve one from a Collection object."

#: ../../source/df-basic.rst:77
msgid "获取列"
msgstr "Retrieve a column"

#: ../../source/df-basic.rst:79
msgid "你可以使用 collection.column_name 取出一列，例如"
msgstr "You can use collection.column_name to retrieve a column. For example:"

#: ../../source/df-basic.rst:81
msgid ""
">>> iris.sepallength.head(5)\n"
"   sepallength\n"
"0          5.1\n"
"1          4.9\n"
"2          4.7\n"
"3          4.6\n"
"4          5.0"
msgstr ""

#: ../../source/df-basic.rst:92
msgid ""
"如果列名存储在一个字符串变量中，除了使用 getattr(df, 'column_name') 达到"
"相同的效果外，也可以使用 df[column_name] 的形式，例如"
msgstr ""
"If the column name is stored in a string variable, apart from using "
"getattr(df, 'column_name'), df[column_name] can also be used to achieve "
"the same effect. For example:"

#: ../../source/df-basic.rst:95
msgid ""
">>> iris['sepallength'].head(5)\n"
"   sepallength\n"
"0          5.1\n"
"1          4.9\n"
"2          4.7\n"
"3          4.6\n"
"4          5.0"
msgstr ""

#: ../../source/df-basic.rst:106
msgid "列类型"
msgstr "Column type"

#: ../../source/df-basic.rst:108
msgid ""
"DataFrame包括自己的类型系统，在使用Table初始化的时候，ODPS的类型会被进行"
"转换。这样做的好处是，能支持更多的计算后端。 目前，DataFrame的执行后端"
"支持ODPS SQL、pandas以及数据库（MySQL和Postgres）。"
msgstr ""
"DataFrame has its own type system. When performing the Table "
"initialization, the MaxCompute types are cast. This design provides "
"support for more execution backends. Currently, DataFrame's execution "
"backends include MaxCompute SQL, pandas, and databases (MySQL and "
"PostgresSQL)."

#: ../../source/df-basic.rst:111
msgid "PyODPS DataFrame 包括以下类型："
msgstr "PyODPS DataFrame includes the following types:"

#: ../../source/df-basic.rst:113
msgid ""
"``int8``\\ ，\\ ``int16``\\ ，\\ ``int32``\\ ，\\ ``int64``\\ ，\\ ``"
"float32``\\ ，\\ ``float64``\\ ，\\ ``boolean``\\ ，\\ ``string``\\ ，\\ "
"``decimal``\\ ，\\ ``datetime``\\ ，\\ ``list``\\ ，\\ ``dict``"
msgstr ""
"``int8``\\, \\ ``int16``\\, \\ ``int32``\\, \\ ``int64``\\, \\ "
"``float32``\\, \\ ``float64``\\, \\ ``boolean``\\, \\ ``string``\\, \\ "
"``decimal``\\, \\ ``datetime``\\, \\ ``list``\\. and \\ ``dict``."

#: ../../source/df-basic.rst:116
msgid "ODPS的字段和DataFrame的类型映射关系如下："
msgstr "The correspondence between MaxCompute and DataFrame types is as follows:"

#: ../../source/df-basic.rst:119
msgid "ODPS类型"
msgstr "MaxCompute type"

#: ../../source/df-basic.rst:119
msgid "DataFrame类型"
msgstr "DataFrame type"

#: ../../source/df-basic.rst:121
msgid "bigint"
msgstr ""

#: ../../source/df-basic.rst:121
msgid "int64"
msgstr ""

#: ../../source/df-basic.rst:122
msgid "double"
msgstr ""

#: ../../source/df-basic.rst:122
msgid "float64"
msgstr ""

#: ../../source/df-basic.rst:123
msgid "string"
msgstr ""

#: ../../source/df-basic.rst:124
msgid "datetime"
msgstr ""

#: ../../source/df-basic.rst:125
msgid "boolean"
msgstr ""

#: ../../source/df-basic.rst:126
msgid "decimal"
msgstr ""

#: ../../source/df-basic.rst:127
msgid "array<value_type>"
msgstr ""

#: ../../source/df-basic.rst:127
msgid "list<value_type>"
msgstr ""

#: ../../source/df-basic.rst:128
msgid "map<key_type, value_type>"
msgstr ""

#: ../../source/df-basic.rst:128
msgid "dict<key_type, value_type>"
msgstr ""

#: ../../source/df-basic.rst:131
msgid ""
"list 和 dict 必须填写其包含值的类型，否则会报错。目前 DataFrame 暂不支持 "
"MaxCompute 2.0 中新增的 Timestamp 及 Struct 类型，未来的版本会支持。"
msgstr ""
"For list and dict types, the types of their containing values must be "
"specified, or an error occurs. Currently, DataFrame does not support the "
"Timestamp type and Struct type that have been newly introduced in "
"MaxCompute 2.0. Support may be added in future releases."

#: ../../source/df-basic.rst:134
msgid "在 Sequence 中可以通过 sequence.dtype 获取数据类型："
msgstr "You can use sequence.dtype to retrieve the data types of Sequence objects:"

#: ../../source/df-basic.rst:136
msgid ""
">>> iris.sepallength.dtype\n"
"float64"
msgstr ""

#: ../../source/df-basic.rst:141
msgid ""
"如果要修改一列的类型，可以使用 astype 方法。该方法输入一个类型，并返回"
"类型转换后的 Sequence。例如，"
msgstr ""
"You can use the astype method to change the type of an entire column. "
"This method requires a type as input and returns the converted Sequence "
"object as output. For example:"

#: ../../source/df-basic.rst:143
msgid ""
">>> iris.sepallength.astype('int')\n"
"   sepallength\n"
"0            5\n"
"1            4\n"
"2            4\n"
"3            4\n"
"4            5"
msgstr ""

#: ../../source/df-basic.rst:155
msgid "列名"
msgstr "Column name"

#: ../../source/df-basic.rst:157
msgid ""
"在 DataFrame 的计算过程中，一个 Sequence 必须要有列名。在很多情况下，"
"DataFrame 会起一个名字。比如："
msgstr ""
"In the calculation of DataFrame, a Sequence object must have a column "
"name. At most times, DataFrame creates names automatically. For example: "

#: ../../source/df-basic.rst:159
msgid ""
">>> iris.groupby('name').sepalwidth.max()\n"
"   sepalwidth_max\n"
"0             4.4\n"
"1             3.4\n"
"2             3.8"
msgstr ""

#: ../../source/df-basic.rst:167
msgid ""
"可以看到，\\ ``sepalwidth``\\ 取最大值后被命名为\\ ``sepalwidth_max``\\ "
"。还有一些操作，比如一个 Sequence 做加法，加上一个 Scalar，这时，会被命名"
"为这个 Sequence 的名字。其它情况下，需要用户去自己命名。"
msgstr ""
"As you can see, \\ ``sepalwidth``\\ was named \\ ``sepalwidth_max``\\ "
"after obtaining the maximum value. Other operations, such as adding a "
"Sequence to a Scalar, name the result with the name of the Sequence. In "
"other cases, you need to name the column yourself."

#: ../../source/df-basic.rst:170
msgid "Sequence 提供 rename 方法为一列设置名字，用法示例如下："
msgstr "Sequence provides rename method to reset names of columns. For example:"

#: ../../source/df-basic.rst:172
msgid ""
">>> iris.sepalwidth.rename('sepal_width').head(5)\n"
"   sepal_width\n"
"0          3.5\n"
"1          3.0\n"
"2          3.2\n"
"3          3.1\n"
"4          3.6"
msgstr ""

#: ../../source/df-basic.rst:182
msgid ""
"需要注意的是，rename 操作并不是就地重命名。如果要在 Collection 上应用新的"
"列名，需要重新做列选择，例如"
msgstr ""
"Note that rename method does not actually rename the column in place. If "
"you want to apply new names in your collection, you need to select "
"columns again. For instance,"

#: ../../source/df-basic.rst:184
msgid ""
">>> iris[iris.exclude(\"sepalwidth\"), "
"iris.sepalwidth.rename('sepal_width')].head(5)\n"
"   sepallength  petallength  petalwidth         name  sepal_width\n"
"0          5.1          1.4         0.2  Iris-setosa          3.5\n"
"1          4.9          1.4         0.2  Iris-setosa          3.0\n"
"2          4.7          1.3         0.2  Iris-setosa          3.2\n"
"3          4.6          1.5         0.2  Iris-setosa          3.1\n"
"4          5.0          1.4         0.2  Iris-setosa          3.6"
msgstr ""

#: ../../source/df-basic.rst:196
msgid "简单的列变换"
msgstr "Simple column transformations"

#: ../../source/df-basic.rst:198
msgid ""
"你可以对一个 Sequence 进行运算，返回一个新的 Sequence，正如对简单的 "
"Python 变量进行运算一样。对数值列， Sequence 支持四则运算，而对字符串则"
"支持字符串相加等操作。例如，"
msgstr ""
"You can perform operations on a Sequence object to get a new Sequence, "
"just as you do with a simple Python variable. For numeric columns,  "
"arithmetic operations are supported. For string columns, operations such "
"as string concatenations are supported. For example:"

#: ../../source/df-basic.rst:201
msgid ""
">>> (iris.sepallength + 5).head(5)\n"
"   sepallength\n"
"0         10.1\n"
"1          9.9\n"
"2          9.7\n"
"3          9.6\n"
"4         10.0"
msgstr ""

#: ../../source/df-basic.rst:211
msgid "而"
msgstr "String addition:"

#: ../../source/df-basic.rst:213
msgid ""
">>> (iris.sepallength + iris.sepalwidth).rename('sum_sepal').head(5)\n"
"   sum_sepal\n"
"0        8.6\n"
"1        7.9\n"
"2        7.9\n"
"3        7.7\n"
"4        8.6"
msgstr ""

#: ../../source/df-basic.rst:223
msgid ""
"注意到两列参与运算，因而 PyODPS 无法确定最终显示的列名，需要手动指定。"
"详细的列变换说明，请参见 :ref:`dfelement`。"
msgstr ""
"Note that when two columns are involved in operations, you need to "
"manually specify the name of the result column. For details about column "
"transformations, see :ref:`dfelement`."

#: ../../source/df-basic.rst:226
msgid "Collection"
msgstr "Collection"

#: ../../source/df-basic.rst:227
msgid ""
"DataFrame 中所有二维数据集上的操作都属于 :class:`CollectionExpr`，可视为"
"一张 ODPS 表或一张电子表单，DataFrame 对象也是 CollectionExpr 的特例。"
"CollectionExpr 中包含针对二维数据集的列操作、筛选、变换等大量操作。"
msgstr ""
"CollectionExpr supports all operations on DataFrame two-dimensional "
"datasets. It can be seen as a MaxCompute table or a spreadsheet. "
"DataFrame objects are also CollectionExpr objects. Many column "
"operations, filter operations, and transform operations on two-"
"dimensional datasets are supported by CollectionExpr."

#: ../../source/df-basic.rst:231
msgid "获取类型"
msgstr "Retrieve types"

#: ../../source/df-basic.rst:233
msgid ""
"``dtypes``\\ 可以用来获取 CollectionExpr 中所有列的类型。``dtypes`` 返回"
"的是 :ref:`Schema类型 <table_schema>` 。"
msgstr ""
"You can use the ``dtypes`` method to retrieve the types of all columns in"
" a CollectionExpr object. ``dtypes`` returns a :ref:`Schema type."

#: ../../source/df-basic.rst:235
msgid ""
">>> iris.dtypes\n"
"odps.Schema {\n"
"  sepallength           float64\n"
"  sepalwidth            float64\n"
"  petallength           float64\n"
"  petalwidth            float64\n"
"  name                  string\n"
"}"
msgstr ""

#: ../../source/df-basic.rst:248
msgid "列选择和增删"
msgstr "Select, add, and delete columns"

#: ../../source/df-basic.rst:250
msgid ""
"如果要从一个 CollectionExpr 中选取部分列，产生新的数据集，可以使用 expr["
"columns] 语法。例如，"
msgstr ""
"You can use the expr[columns] syntax to select a certain amount of "
"columns from a CollectionExpr object and form a new dataset. For example:"

#: ../../source/df-basic.rst:252
msgid ""
">>> iris['name', 'sepallength'].head(5)\n"
"          name  sepallength\n"
"0  Iris-setosa          5.1\n"
"1  Iris-setosa          4.9\n"
"2  Iris-setosa          4.7\n"
"3  Iris-setosa          4.6\n"
"4  Iris-setosa          5.0"
msgstr ""

#: ../../source/df-basic.rst:264
msgid ""
"**注意**\\ ：如果需要选择的列只有一列，需要在 columns 后加上逗号或者显示"
"标记为列表，例如 df[df.sepal_length, ] 或 df[[df.sepal_length]]，否则返回"
"的将是一个 Sequence 对象，而不是 Collection。"
msgstr ""
"**Note**\\: If only one column is needed, you need to add a comma (,) "
"after the column name or explicitly mark the column as a list, for "
"example df[df.sepal_length, ] or df[[df.sepal_length]]. Otherwise, a "
"Sequence object, instead of a Collection object, is returned."

#: ../../source/df-basic.rst:267
msgid "如果想要在新的数据集中排除已有数据集的某些列，可使用 exclude 方法："
msgstr ""
"You can use the exclude method to exclude certain columns from the new "
"dataset:"

#: ../../source/df-basic.rst:269
msgid ""
">>> iris.exclude('sepallength', 'petallength')[:5]\n"
"   sepalwidth  petalwidth         name\n"
"0         3.5         0.2  Iris-setosa\n"
"1         3.0         0.2  Iris-setosa\n"
"2         3.2         0.2  Iris-setosa\n"
"3         3.1         0.2  Iris-setosa\n"
"4         3.6         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:279
msgid "0.7.2 以后的 PyODPS 支持另一种写法，即在数据集上直接排除相应的列："
msgstr ""
"In PyODPS versions 0.7.2 and higher, a new method is supported. You can "
"exclude certain columns of the dataset directly:"

#: ../../source/df-basic.rst:281
msgid ""
">>> del iris['sepallength']\n"
">>> del iris['petallength']\n"
">>> iris[:5]\n"
"   sepalwidth  petalwidth         name\n"
"0         3.5         0.2  Iris-setosa\n"
"1         3.0         0.2  Iris-setosa\n"
"2         3.2         0.2  Iris-setosa\n"
"3         3.1         0.2  Iris-setosa\n"
"4         3.6         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:293
msgid ""
"如果我们需要在已有 collection 中添加某一列变换的结果，也可以使用 expr["
"expr, new_sequence] 语法， 新增的列会作为新 collection 的一部分。"
msgstr ""
"You can also use the expr[expr, new_sequence] syntax to add a new "
"sequence transformation to the original collection. The new sequence is "
"part of the new collection."

#: ../../source/df-basic.rst:296
msgid ""
"下面的例子将 iris 中的 sepalwidth 列加一后重命名为 sepalwidthplus1 并追加"
"到数据集末尾，形成新的数据集："
msgstr ""
"The following example illustrates that, we create a new sequence by "
"adding one to each element in the original sepalwidth column of iris, "
"rename it to sepalwidthplus1, and add it to the iris collection as a new "
"sequence."

#: ../../source/df-basic.rst:298
msgid ""
">>> iris[iris, (iris.sepalwidth + 1).rename('sepalwidthplus1')].head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  \\\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa\n"
"\n"
"   sepalwidthplus1\n"
"0              4.5\n"
"1              4.0\n"
"2              4.2\n"
"3              4.1\n"
"4              4.6"
msgstr ""

#: ../../source/df-basic.rst:315
msgid ""
"使用 `df[df, new_sequence]` 需要注意的是，变换后的列名与原列名可能相同，"
"如果需要与原 collection 合并， 请将该列重命名。"
msgstr ""
"When using `df[df, new_sequence]`, note that the transformed column may "
"have the same name as the original column. Rename the new column if you "
"want to append it to the original collection."

#: ../../source/df-basic.rst:318
msgid "0.7.2 以后版本的 PyODPS 支持直接在当前数据集中追加，写法为"
msgstr ""
"In PyODPS versions 0.7.2 and higher, you can append a column to the "
"current dataset directly:"

#: ../../source/df-basic.rst:320
msgid ""
">>> iris['sepalwidthplus1'] = iris.sepalwidth + 1\n"
">>> iris.head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  \\\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa\n"
"\n"
"   sepalwidthplus1\n"
"0              4.5\n"
"1              4.0\n"
"2              4.2\n"
"3              4.1\n"
"4              4.6"
msgstr ""

#: ../../source/df-basic.rst:338
msgid ""
"我们也可以先将原列通过 exclude 方法进行排除，再将变换后的新列并入，而不必"
"担心重名。"
msgstr ""
"You can also use the exclude method to exclude the original column before"
" appending the new one to the dataset."

#: ../../source/df-basic.rst:340
msgid ""
">>> iris[iris.exclude('sepalwidth'), iris.sepalwidth * 2].head(5)\n"
"   sepallength  petallength  petalwidth         name  sepalwidth\n"
"0          5.1          1.4         0.2  Iris-setosa         7.0\n"
"1          4.9          1.4         0.2  Iris-setosa         6.0\n"
"2          4.7          1.3         0.2  Iris-setosa         6.4\n"
"3          4.6          1.5         0.2  Iris-setosa         6.2\n"
"4          5.0          1.4         0.2  Iris-setosa         7.2"
msgstr ""

#: ../../source/df-basic.rst:350
msgid "对于 0.7.2 以后版本的 PyODPS，如果想在当前数据集上直接覆盖，则可以写"
msgstr ""
"In PyODPS versions 0.7.2 and higher, you can also overwrite an existing "
"column:"

#: ../../source/df-basic.rst:352
msgid ""
">>> iris['sepalwidth'] = iris.sepalwidth * 2\n"
">>> iris.head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.1         7.0          1.4         0.2  Iris-setosa\n"
"1          4.9         6.0          1.4         0.2  Iris-setosa\n"
"2          4.7         6.4          1.3         0.2  Iris-setosa\n"
"3          4.6         6.2          1.5         0.2  Iris-setosa\n"
"4          5.0         7.2          1.4         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:363
msgid ""
"增删列以创建新 collection 的另一种方法是调用 select 方法，将需要选择的列"
"作为参数输入。如果需要重命名，使用 keyword 参数输入，并将新的列名作为参数"
"名即可。"
msgstr ""
"The select method provides another way to create a new collection, "
"keyword argument will rename the provided sequence to the given keyword."

#: ../../source/df-basic.rst:366
msgid ""
">>> iris.select('name', sepalwidthminus1=iris.sepalwidth - 1).head(5)\n"
"          name  sepalwidthminus1\n"
"0  Iris-setosa               2.5\n"
"1  Iris-setosa               2.0\n"
"2  Iris-setosa               2.2\n"
"3  Iris-setosa               2.1\n"
"4  Iris-setosa               2.6"
msgstr ""

#: ../../source/df-basic.rst:376
msgid ""
"此外，我们也可以传入一个 lambda 表达式，它接收一个参数，接收上一步的结果"
"。在执行时，PyODPS 会检查这些 lambda 表达式，传入上一步生成的 collection "
"并将其替换为正确的列。"
msgstr ""
"You can also pass in a lambda expression, which takes the result from the"
" previous operation as a parameter. When executed, PyODPS checks the "
"lambda expression and passes in the Collection object generated from the "
"previous operation to get new columns."

#: ../../source/df-basic.rst:379
msgid ""
">>> iris['name', 'petallength'][[lambda x: x.name]].head(5)\n"
"          name\n"
"0  Iris-setosa\n"
"1  Iris-setosa\n"
"2  Iris-setosa\n"
"3  Iris-setosa\n"
"4  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:389
msgid "此外，在 0.7.2 以后版本的 PyODPS 中，支持对数据进行条件赋值，例如"
msgstr ""
"In PyODPS versions 0.7.2 and higher, conditional assignments are "
"supported."

#: ../../source/df-basic.rst:391
msgid ""
">>> iris[iris.sepallength > 5.0, 'sepalwidth'] = iris.sepalwidth * 2\n"
">>> iris.head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.1        14.0          1.4         0.2  Iris-setosa\n"
"1          4.9         6.0          1.4         0.2  Iris-setosa\n"
"2          4.7         6.4          1.3         0.2  Iris-setosa\n"
"3          4.6         6.2          1.5         0.2  Iris-setosa\n"
"4          5.0         7.2          1.4         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:403
msgid "引入常数和随机数"
msgstr "Constants and random numbers"

#: ../../source/df-basic.rst:404
msgid ""
"DataFrame 支持在 collection 中追加一列常数。追加常数需要使用 :class:`"
"Scalar`，引入时需要手动指定列名，如"
msgstr ""
"DataFrame allows you to append a column of constants to a Collection "
"object. Scalar is required and you need to manually specify the column "
"name. For example:"

#: ../../source/df-basic.rst:406
msgid ""
">>> from odps.df import Scalar\n"
">>> iris[iris, Scalar(1).rename('id')][:5]\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  id\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa   1\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa   1\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa   1\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa   1\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa   1"
msgstr ""

#: ../../source/df-basic.rst:418
msgid "如果需要指定一个空值列，可以使用 :class:`NullScalar`，需要提供字段类型。"
msgstr ""
"You can use NullScalar to create a null column. The column type must be "
"specified."

#: ../../source/df-basic.rst:420
msgid ""
">>> from odps.df import NullScalar\n"
">>> iris[iris, NullScalar('float').rename('fid')][:5]\n"
"   sepal_length  sepal_width  petal_length  petal_width     category   "
"fid\n"
"0           5.1          3.5           1.4          0.2  Iris-setosa  "
"None\n"
"1           4.9          3.0           1.4          0.2  Iris-setosa  "
"None\n"
"2           4.7          3.2           1.3          0.2  Iris-setosa  "
"None\n"
"3           4.6          3.1           1.5          0.2  Iris-setosa  "
"None\n"
"4           5.0          3.6           1.4          0.2  Iris-setosa  None"
msgstr ""

#: ../../source/df-basic.rst:432
msgid "在 PyODPS 0.7.12 及以后版本中，引入了简化写法："
msgstr ""
"In PyODPS versions 0.7.12 and higher, a simpler method has been "
"introduced:"

#: ../../source/df-basic.rst:434
msgid ""
">>> iris['id'] = 1\n"
">>> iris\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  id\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa   1\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa   1\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa   1\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa   1\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa   1"
msgstr ""

#: ../../source/df-basic.rst:446
msgid ""
"需要注意的是，这种写法无法自动识别空值的类型，所以在增加空值列时，仍然要"
"使用"
msgstr ""
"Note that this method cannot automatically recognize the type of null "
"values. Add null columns as follows: "

#: ../../source/df-basic.rst:448
msgid ""
">>> iris['null_col'] = NullScalar('float')\n"
">>> iris\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  "
"null_col\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa      "
"None\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa      "
"None\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa      "
"None\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa      "
"None\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa      None"
msgstr ""

#: ../../source/df-basic.rst:460
msgid ""
"DataFrame 也支持在 collection 中增加一列随机数列，该列类型为 float，范围"
"为 0 - 1，每行数值均不同。 追加随机数列需要使用 :class:`RandomScalar`，"
"参数为随机数种子，可省略。"
msgstr ""
"DataFrame also allows you to append a column of random numbers to a "
"Collection object. The column type is float and the value range is 0-1. "
"Each number has a different value. :class:`RandomScalar` is required and "
"the parameter is an optional random seed."

#: ../../source/df-basic.rst:463
msgid ""
">>> from odps.df import RandomScalar\n"
">>> iris[iris, RandomScalar().rename('rand_val')][:5]\n"
"   sepallength  sepalwidth  petallength  petalwidth         name  "
"rand_val\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa  "
"0.000471\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa  "
"0.799520\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa  "
"0.834609\n"
"3          4.6         3.1          1.5         0.2  Iris-setosa  "
"0.106921\n"
"4          5.0         3.6          1.4         0.2  Iris-setosa  0.763442"
msgstr ""

#: ../../source/df-basic.rst:475
msgid "过滤数据"
msgstr "Filter data"

#: ../../source/df-basic.rst:477
msgid "Collection 提供了数据过滤的功能，"
msgstr "Collection provides different ways to filter data."

#: ../../source/df-basic.rst:479
msgid "我们试着查询\\ ``sepallength``\\ 大于5的几条数据。"
msgstr ""
"The following example finds records where \\ ``sepallength``\\ is greater"
" than 5."

#: ../../source/df-basic.rst:481
msgid ""
">>> iris[iris.sepallength > 5].head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa\n"
"1          5.4         3.9          1.7         0.4  Iris-setosa\n"
"2          5.4         3.7          1.5         0.2  Iris-setosa\n"
"3          5.8         4.0          1.2         0.2  Iris-setosa\n"
"4          5.7         4.4          1.5         0.4  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:491
msgid "多个查询条件："
msgstr "Multiple filter conditions:"

#: ../../source/df-basic.rst:493
msgid ""
">>> iris[(iris.sepallength < 5) & (iris['petallength'] > 1.5)].head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          4.8         3.4          1.6         0.2      Iris-setosa\n"
"1          4.8         3.4          1.9         0.2      Iris-setosa\n"
"2          4.7         3.2          1.6         0.2      Iris-setosa\n"
"3          4.8         3.1          1.6         0.2      Iris-setosa\n"
"4          4.9         2.4          3.3         1.0  Iris-versicolor"
msgstr ""

#: ../../source/df-basic.rst:504
msgid "或条件："
msgstr "OR operator:"

#: ../../source/df-basic.rst:506
msgid ""
">>> iris[(iris.sepalwidth < 2.5) | (iris.sepalwidth > 4)].head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.7         4.4          1.5         0.4      Iris-setosa\n"
"1          5.2         4.1          1.5         0.1      Iris-setosa\n"
"2          5.5         4.2          1.4         0.2      Iris-setosa\n"
"3          4.5         2.3          1.3         0.3      Iris-setosa\n"
"4          5.5         2.3          4.0         1.3  Iris-versicolor"
msgstr ""

#: ../../source/df-basic.rst:519
msgid "**记住，与和或条件必须使用&和|，不能使用and和or。**"
msgstr ""
"**Note that you must use ampersands (&) and vertical bars (|) to "
"represent the AND and OR operators respectively.**"

#: ../../source/df-basic.rst:522
msgid "非条件："
msgstr "NOT operator:"

#: ../../source/df-basic.rst:524
msgid ""
">>> iris[~(iris.sepalwidth > 3)].head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          4.9         3.0          1.4         0.2  Iris-setosa\n"
"1          4.4         2.9          1.4         0.2  Iris-setosa\n"
"2          4.8         3.0          1.4         0.1  Iris-setosa\n"
"3          4.3         3.0          1.1         0.1  Iris-setosa\n"
"4          5.0         3.0          1.6         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:535
msgid "我们也可以显式调用filter方法，提供多个与条件"
msgstr ""
"You can also explicitly call the filter method to specify multiple "
"conditions."

#: ../../source/df-basic.rst:537
msgid ""
">>> iris.filter(iris.sepalwidth > 3.5, iris.sepalwidth < 4).head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.0         3.6          1.4         0.2  Iris-setosa\n"
"1          5.4         3.9          1.7         0.4  Iris-setosa\n"
"2          5.4         3.7          1.5         0.2  Iris-setosa\n"
"3          5.4         3.9          1.3         0.4  Iris-setosa\n"
"4          5.7         3.8          1.7         0.3  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:548
msgid "同样对于连续的操作，我们可以使用lambda表达式"
msgstr "You can use lambda expressions to perform sequential operations."

#: ../../source/df-basic.rst:550
msgid ""
">>> iris[iris.sepalwidth > 3.8]['name', lambda x: x.sepallength + 1]\n"
"          name  sepallength\n"
"0  Iris-setosa          6.4\n"
"1  Iris-setosa          6.8\n"
"2  Iris-setosa          6.7\n"
"3  Iris-setosa          6.4\n"
"4  Iris-setosa          6.2\n"
"5  Iris-setosa          6.5"
msgstr ""

#: ../../source/df-basic.rst:561
msgid ""
"对于Collection，如果它包含一个列是boolean类型，则可以直接使用该列作为过滤"
"条件。"
msgstr ""
"If a Collection object contains a boolean column, you can use it directly"
" as a filter condition."

#: ../../source/df-basic.rst:564
msgid ""
">>> df.dtypes\n"
"odps.Schema {\n"
"  a boolean\n"
"  b int64\n"
"}\n"
">>> df[df.a]\n"
"      a  b\n"
"0  True  1\n"
"1  True  3"
msgstr ""

#: ../../source/df-basic.rst:576
msgid ""
"因此，记住对Collection取单个squence的操作时，只有boolean列是合法的，即对"
"Collection作过滤操作。"
msgstr ""
"When retrieving a single sequence from a Collection object, only the "
"boolean column can be used as a valid filter condition."

#: ../../source/df-basic.rst:579
msgid ""
">>> df[df.a, ]       # 取列操作\n"
">>> df[[df.a]]       # 取列操作\n"
">>> df.select(df.a)  # 显式取列\n"
">>> df[df.a]         # a列是boolean列，执行过滤操作\n"
">>> df.a             # 取单列\n"
">>> df['a']          # 取单列"
msgstr ""
">>> df[df.a, ]       # obtain a one-column collection via item syntax\n"
">>> df[[df.a]]       # obtain a one-column collection via item syntax\n"
">>> df.select(df.a)  # obtain a one-column collection explicitly with "
"select method\n"
">>> df[df.a]         # filter with a binary-typed column\n"
">>> df.a             # obtain a sequence from a collection\n"
">>> df['a']          # obtain a sequence from a collection"

#: ../../source/df-basic.rst:588
msgid ""
"同时，我们也支持Pandas中的\\ ``query``\\方法，用查询语句来做数据的筛选，"
"在表达式中直接使用列名如\\ ``sepallength``\\进行操作， 另外在查询语句中\\"
" ``&``\\和\\ ``and``\\都表示与操作，\\ ``|``\\和\\ ``or``\\都表示或操作。"
msgstr ""
"The \\ ``query``\\ method in pandas is also supported. You can write "
"query statements to filter data. Column names such as \\ "
"``sepallength``\\ can be used directly. Ampersands (\\``&``\\) and "
"\\``and``\\ both represent the AND operator. Vertical bars (\\``|``\\) "
"and \\ ``or``\\ both represent the OR operator."

#: ../../source/df-basic.rst:592
msgid ""
">>> iris.query(\"(sepallength < 5) and (petallength > 1.5)\").head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          4.8         3.4          1.6         0.2      Iris-setosa\n"
"1          4.8         3.4          1.9         0.2      Iris-setosa\n"
"2          4.7         3.2          1.6         0.2      Iris-setosa\n"
"3          4.8         3.1          1.6         0.2      Iris-setosa\n"
"4          4.9         2.4          3.3         1.0  Iris-versicolor"
msgstr ""

#: ../../source/df-basic.rst:602
msgid "当表达式中需要使用到本地变量时，需要在该变量前加一个\\ ``@``\\ 前缀。"
msgstr ""
"When local variables are needed in the expressions, add an at sign (@) "
"before the variable name."

#: ../../source/df-basic.rst:605
msgid ""
">>> var = 4\n"
">>> iris.query(\"(sepalwidth < 2.5) | (sepalwidth > @var)\").head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          5.7         4.4          1.5         0.4      Iris-setosa\n"
"1          5.2         4.1          1.5         0.1      Iris-setosa\n"
"2          5.5         4.2          1.4         0.2      Iris-setosa\n"
"3          4.5         2.3          1.3         0.3      Iris-setosa\n"
"4          5.5         2.3          4.0         1.3  Iris-versicolor"
msgstr ""

#: ../../source/df-basic.rst:616
msgid "目前\\ ``query``\\支持的语法包括："
msgstr "The query method currently supports the following syntaxes."

#: ../../source/df-basic.rst:619
msgid "语法"
msgstr "Syntax"

#: ../../source/df-basic.rst:619 ../../source/df-basic.rst:721
msgid "说明"
msgstr "Description"

#: ../../source/df-basic.rst:621
msgid "name"
msgstr ""

#: ../../source/df-basic.rst:621
msgid "没有 \\ ``@``\\ 前缀的都当做列名处理，有前缀的会获取本地变量"
msgstr ""
"Names without the at sign (@) are processed as column names. Otherwise, "
"they are processed as local variables. "

#: ../../source/df-basic.rst:622
msgid "operator"
msgstr ""

#: ../../source/df-basic.rst:622
msgid ""
"支持部分运算符：\\ ``+``\\，\\ ``-``\\，\\ ``*``\\，\\ ``/``\\，\\ ``//``"
"\\，\\ ``%``\\，\\ ``**``\\, \\ ``==``\\，\\ ``!=``\\，\\ ``<``\\，\\ ``<"
"=``\\，\\ ``>``\\，\\ ``>=``\\，\\ ``in``\\，\\ ``not in``\\"
msgstr ""
"The following operators are supported: \\ ``+``\\, \\ ``-``\\, \\ "
"``*``\\, \\ ``/``\\, \\ ``//``\\, \\ ``%``\\, \\ ``**``\\, \\ ``==``\\, "
"\\ ``!=``\\, \\ ``<``\\, \\ ``<=``\\, \\ ``>``\\, \\ ``>=``\\, \\ "
"``in``\\, and \\ ``not in``\\"

#: ../../source/df-basic.rst:624
msgid "bool"
msgstr ""

#: ../../source/df-basic.rst:624
msgid ""
"与或非操作，其中 \\ ``&``\\ 和 \\ ``and``\\ 表示与，\\ ``|``\\ 和 \\ ``or"
"``\\ 表示或"
msgstr ""
"Ampersands (\\ ``&``\\ ) and \\ ``and``\\ represent the AND operator. "
"Vertical bars (\\ ``|``\\) and \\ ``or``\\ represent the OR operator."

#: ../../source/df-basic.rst:625
msgid "attribute"
msgstr ""

#: ../../source/df-basic.rst:625
msgid "取对象属性"
msgstr "the attribute of the object"

#: ../../source/df-basic.rst:626
msgid "index, slice, Subscript"
msgstr "index, slice, Subscript"

#: ../../source/df-basic.rst:626
msgid "切片操作"
msgstr "slice operations"

#: ../../source/df-basic.rst:632
msgid "并列多行输出"
msgstr "Lateral view"

#: ../../source/df-basic.rst:633
msgid ""
"对于 list 及 map 类型的列，explode 方法会将该列转换为多行输出。使用 apply"
" 方法也可以输出多行。 为了进行聚合等操作，常常需要将这些输出和原表中的列"
"合并。此时可以使用 DataFrame 提供的并列多行输出功能， 写法为将多行输出"
"函数生成的集合与原集合中的列名一起映射。"
msgstr ""
"For list and map columns, the ``explode`` method can convert the columns "
"into multiple rows for output. Functions passed into the ``apply`` method"
" often generate multiple lines, too. It is often need to merge these "
"outputs with columns in original tables for purposes such as aggregation."
" In databases, this kind of operation is often called lateral view. In "
"PyODPS, you can achieve this as follows:"

#: ../../source/df-basic.rst:637
msgid "并列多行输出的例子如下："
msgstr "To generate the lateral view:"

#: ../../source/df-basic.rst:639
msgid ""
">>> df\n"
"   id         a             b\n"
"0   1  [a1, b1]  [a2, b2, c2]\n"
"1   2      [c1]      [d2, e2]\n"
">>> df[df.id, df.a.explode(), df.b]\n"
"   id   a             b\n"
"0   1  a1  [a2, b2, c2]\n"
"1   1  b1  [a2, b2, c2]\n"
"2   2  c1      [d2, e2]\n"
">>> df[df.id, df.a.explode(), df.b.explode()]\n"
"   id   a   b\n"
"0   1  a1  a2\n"
"1   1  a1  b2\n"
"2   1  a1  c2\n"
"3   1  b1  a2\n"
"4   1  b1  b2\n"
"5   1  b1  c2\n"
"6   2  c1  d2\n"
"7   2  c1  e2"
msgstr ""

#: ../../source/df-basic.rst:661
msgid ""
"如果多行输出方法对某个输入不产生任何输出，默认输入行将不在最终结果中出现"
"。如果需要在结果中出现该行，可以设置 ``keep_nulls=True``。此时，与该行"
"并列的值将输出为空值："
msgstr ""
"When the method does not produce any output for some rows, these rows "
"will not appear in the output, which is often not expected. To preserve "
"these rows in the output, you can add ``keep_nulls=True`` to ``explode`` "
"or ``apply`` calls. In this scenario, columns produced by ``explode`` or "
"``apply`` will be left None."

#: ../../source/df-basic.rst:664
msgid ""
">>> df\n"
"   id         a\n"
"0   1  [a1, b1]\n"
"1   2        []\n"
">>> df[df.id, df.a.explode()]\n"
"   id   a\n"
"0   1  a1\n"
"1   1  b1\n"
">>> df[df.id, df.a.explode(keep_nulls=True)]\n"
"   id     a\n"
"0   1    a1\n"
"1   1    b1\n"
"2   2  None"
msgstr ""

#: ../../source/df-basic.rst:680
msgid ""
"关于 explode 使用并列输出的具体文档可参考 :ref:`dfcollections`，对于 "
"apply 方法使用并列输出的例子可参考 :ref:`dfudtfapp`。"
msgstr ""
"For details about the explode method, see :ref:`dfcollections`. For "
"examples of the apply method, see :ref:`dfudtfapp`."

#: ../../source/df-basic.rst:684
msgid "限制条数"
msgstr "Limit output records"

#: ../../source/df-basic.rst:686
msgid ""
">>> iris[:3]\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:694
msgid ""
"值得注意的是，目前切片对于ODPS SQL后端不支持start和step。我们也可以使用"
"limit方法"
msgstr ""
"Note that for MaxCompute SQL backend, start and step are not supported in"
" slice operations. You can also use the limit method."

#: ../../source/df-basic.rst:696
msgid ""
">>> iris.limit(3)\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          5.1         3.5          1.4         0.2  Iris-setosa\n"
"1          4.9         3.0          1.4         0.2  Iris-setosa\n"
"2          4.7         3.2          1.3         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:706
msgid "**另外，切片操作只能作用在collection上，不能作用于sequence。**"
msgstr ""
"**Additionally, you can perform slice operations on Collection objects "
"only, not Sequence objects.**"

#: ../../source/df-basic.rst:709
msgid "执行"
msgstr "Execution"

#: ../../source/df-basic.rst:714
msgid "延迟执行"
msgstr "Deferred execution"

#: ../../source/df-basic.rst:716
msgid ""
"DataFrame上的所有操作并不会立即执行，只有当用户显式调用\\ ``execute``\\ "
"方法，或者一些立即执行的方法时（内部调用的就是\\ ``execute``\\ ），才会"
"真正去执行。"
msgstr ""
"The operations in DataFrame are not automatically executed. They are only"
" executed when you explicitly call the \\ `ʻexecute`` \\ action or "
"actions that internally call \\ `ʻexecute`` \\."

#: ../../source/df-basic.rst:718
msgid "这些立即执行的方法包括："
msgstr "These actions include:"

#: ../../source/df-basic.rst:721
msgid "方法"
msgstr "Action"

#: ../../source/df-basic.rst:721
msgid "返回值"
msgstr "Return value"

#: ../../source/df-basic.rst:723
msgid "persist"
msgstr ""

#: ../../source/df-basic.rst:723
msgid "将执行结果保存到ODPS表"
msgstr "Saves the execution result to MaxCompute tables."

#: ../../source/df-basic.rst:723
msgid "PyODPS DataFrame"
msgstr ""

#: ../../source/df-basic.rst:724
msgid "execute"
msgstr ""

#: ../../source/df-basic.rst:724
msgid "执行并返回全部结果"
msgstr "Executes the operations and returns all the results."

#: ../../source/df-basic.rst:724 ../../source/df-basic.rst:725
#: ../../source/df-basic.rst:726
msgid "ResultFrame"
msgstr ""

#: ../../source/df-basic.rst:725
msgid "head"
msgstr ""

#: ../../source/df-basic.rst:725
msgid "查看开头N行数据，这个方法会执行所有结果，并取开头N行数据"
msgstr "Executes the operations and returns the first N rows of result data."

#: ../../source/df-basic.rst:726
msgid "tail"
msgstr ""

#: ../../source/df-basic.rst:726
msgid "查看结尾N行数据，这个方法会执行所有结果，并取结尾N行数据"
msgstr "Executes the operations and returns the last N rows of result data."

#: ../../source/df-basic.rst:727
msgid "to_pandas"
msgstr ""

#: ../../source/df-basic.rst:727
msgid ""
"转化为 Pandas DataFrame 或者 Series，wrap 参数为 True 的时候，返回 PyODPS"
" DataFrame 对象"
msgstr ""
"Converts a collection object to a pandas DataFrame object or a Sequence "
"object to a Series object. When the wrap parameter is set to Ture, a "
"PyODPS DataFrame object is returned."

#: ../../source/df-basic.rst:727
msgid "wrap为True返回PyODPS DataFrame，False（默认）返回pandas DataFrame"
msgstr ""
"When wrap is set to True, a PyODPS DataFrame object is returned. If you "
"do not set wrap to True, a pandas DataFrame object is returned by "
"default."

#: ../../source/df-basic.rst:728
msgid "plot，hist，boxplot"
msgstr "plot, hist, boxplot"

#: ../../source/df-basic.rst:728
msgid "画图有关"
msgstr "Plotting-related methods."

#: ../../source/df-basic.rst:733
msgid ""
"**注意**\\ ：在交互式环境下，PyODPS DataFrame会在打印或者repr的时候，调用"
"\\ ``execute``\\ 方法，这样省去了用户手动去调用execute。"
msgstr ""
"**Note**\\: In an interactive environment, PyODPS DataFrame objects "
"automatically call the ``execute`` method when printing or repr is "
"called, so that you do not need to manually call the ``execution`` "
"method."

#: ../../source/df-basic.rst:736
msgid ""
">>> iris[iris.sepallength < 5][:5]\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          4.9         3.0          1.4         0.2  Iris-setosa\n"
"1          4.7         3.2          1.3         0.2  Iris-setosa\n"
"2          4.6         3.1          1.5         0.2  Iris-setosa\n"
"3          4.6         3.4          1.4         0.3  Iris-setosa\n"
"4          4.4         2.9          1.4         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:746
msgid "如果想关闭自动调用执行，则需要手动设置"
msgstr "You can manually disable automatic execution as follows:"

#: ../../source/df-basic.rst:748
msgid ""
">>> from odps import options\n"
">>> options.interactive = False\n"
">>>\n"
">>> iris[iris.sepallength < 5][:5]\n"
"Collection: ref_0\n"
"  odps.Table\n"
"    name: odps_test_sqltask_finance.`pyodps_iris`\n"
"    schema:\n"
"      sepallength           : double\n"
"      sepalwidth            : double\n"
"      petallength           : double\n"
"      petalwidth            : double\n"
"      name                  : string\n"
"\n"
"Collection: ref_1\n"
"  Filter[collection]\n"
"    collection: ref_0\n"
"    predicate:\n"
"      Less[sequence(boolean)]\n"
"        sepallength = Column[sequence(float64)] 'sepallength' from "
"collection ref_0\n"
"        Scalar[int8]\n"
"          5\n"
"\n"
"Slice[collection]\n"
"  collection: ref_1\n"
"  stop:\n"
"    Scalar[int8]\n"
"      5"
msgstr ""

#: ../../source/df-basic.rst:779
msgid ""
"此时打印或者 repr 对象，会显示整棵抽象语法树。如果需要执行，则必须手动"
"调用 ``execute`` 方法："
msgstr ""
"Now the entire abstract syntax tree is displayed when printing or repr is"
" called. When you want to actually execute this DataFrame, you have to "
"call ``execute`` method manually:"

#: ../../source/df-basic.rst:781
msgid ""
">>> iris[iris.sepallength < 5][:5].execute()\n"
"   sepallength  sepalwidth  petallength  petalwidth         name\n"
"0          4.9         3.0          1.4         0.2  Iris-setosa\n"
"1          4.7         3.2          1.3         0.2  Iris-setosa\n"
"2          4.6         3.1          1.5         0.2  Iris-setosa\n"
"3          4.6         3.4          1.4         0.3  Iris-setosa\n"
"4          4.4         2.9          1.4         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:792
msgid "读取执行结果"
msgstr "Obtaining execution results"

#: ../../source/df-basic.rst:794
msgid ""
"``execute`` 或 ``head`` 函数输出的结果为 ``ResultFrame`` 类型，可从中读取"
"结果。"
msgstr ""
"Outputs of ``execute`` or ``head`` methods are ``ResultFrame`` instances "
"where you can obtain execution results."

#: ../../source/df-basic.rst:798
msgid "ResultFrame 是结果集合，不能参与后续计算。"
msgstr ""
"The ResultFrame is a result set and cannot be used in subsequent "
"calculations."

#: ../../source/df-basic.rst:801
msgid "ResultFrame 可以迭代取出每条记录："
msgstr "You can use an iterative method to retrieve all records from ResultFrame."

#: ../../source/df-basic.rst:804
msgid ""
">>> result = iris.head(3)\n"
">>> for r in result:\n"
">>>     print(list(r))\n"
"[5.0999999999999996, 3.5, 1.3999999999999999, 0.20000000000000001, u"
"'Iris-setosa']\n"
"[4.9000000000000004, 3.0, 1.3999999999999999, 0.20000000000000001, u"
"'Iris-setosa']\n"
"[4.7000000000000002, 3.2000000000000002, 1.3, 0.20000000000000001, u"
"'Iris-setosa']"
msgstr ""

#: ../../source/df-basic.rst:814
msgid ""
"ResultFrame 也支持在安装有 pandas 的前提下转换为 pandas DataFrame 或使用 "
"pandas 后端的 PyODPS DataFrame："
msgstr ""
"When pandas is installed, a ResultFrame can be converted into a pandas "
"DataFrame or a PyODPS DataFrame with pandas backend."

#: ../../source/df-basic.rst:817
msgid ""
">>> pd_df = iris.head(3).to_pandas()  # 返回 pandas DataFrame\n"
">>> wrapped_df = iris.head(3).to_pandas(wrap=True)  # 返回使用 Pandas 后"
"端的 PyODPS DataFrame"
msgstr ""
">>> pd_df = iris.head(3).to_pandas()  # returns a pandas DataFrame\n"
">>> wrapped_df = iris.head(3).to_pandas(wrap=True)  # returns a PyODPS "
"DataFrame with pandas backend"

#: ../../source/df-basic.rst:823
msgid ""
"关于如何使用 pandas，请参考 `pandas 文档 <https://pandas.pydata.org/docs/"
">`_ 。pandas 为开源库，ODPS 不对其结果负责。"
msgstr ""

#: ../../source/df-basic.rst:827
msgid "保存执行结果为 ODPS 表"
msgstr "Save results to MaxCompute tables"

#: ../../source/df-basic.rst:829
msgid ""
"对 Collection，我们可以调用\\ ``persist``\\ 方法，参数为表名。返回一个新"
"的DataFrame对象"
msgstr ""
"For collection objects, you can use the \\ ``persist``\\ method, which "
"takes a table name as the parameter and returns a new DataFrame object."

#: ../../source/df-basic.rst:831
msgid ""
">>> iris2 = iris[iris.sepalwidth < 2.5].persist('pyodps_iris2')\n"
">>> iris2.head(5)\n"
"   sepallength  sepalwidth  petallength  petalwidth             name\n"
"0          4.5         2.3          1.3         0.3      Iris-setosa\n"
"1          5.5         2.3          4.0         1.3  Iris-versicolor\n"
"2          4.9         2.4          3.3         1.0  Iris-versicolor\n"
"3          5.0         2.0          3.5         1.0  Iris-versicolor\n"
"4          6.0         2.2          4.0         1.0  Iris-versicolor"
msgstr ""

#: ../../source/df-basic.rst:842
msgid ""
"``persist``\\ 可以传入 partitions 参数。加入该参数后，会创建一个分区表，"
"它的分区字段为 partitions 列出的字段， DataFrame 中相应字段的值决定该行将"
"被写入的分区。例如，当 partitions 为 ['name'] 且某行 name 的值为 test， "
"那么该行将被写入分区 ``name=test``。这适用于当分区需要通过计算获取的情形"
"。"
msgstr ""
"You can pass in a partitions parameter to ``persist``\\. A table is "
"created with partition fields specified by the parameter. Data in the "
"input DataFrame will be stored into the partition specified by the value "
"of the corresponding fields. For example, when the value of partitions is"
" ['name'] and the value of field ``name`` in a record is ``test``, the "
"row will be written into partition ``name=test``. This parameter can be "
"used when target partitions of data need to be calculated."

#: ../../source/df-basic.rst:846
msgid ""
">>> iris3 = iris[iris.sepalwidth < 2.5].persist('pyodps_iris3', "
"partitions=['name'])\n"
">>> iris3.data\n"
"odps.Table\n"
"  name: odps_test_sqltask_finance.`pyodps_iris3`\n"
"  schema:\n"
"    sepallength           : double\n"
"    sepalwidth            : double\n"
"    petallength           : double\n"
"    petalwidth            : double\n"
"  partitions:\n"
"    name                  : string"
msgstr ""

#: ../../source/df-basic.rst:861
msgid ""
"如果想写入已经存在的表的某个分区，``persist``\\ 可以传入 partition 参数，"
"指明写入表的哪个分区（如ds=******）。 这时要注意，该DataFrame的每个字段都"
"必须在该表存在，且类型相同。drop_partition和create_partition参数只有在"
"此时有效, 分别表示是否要删除（如果分区存在）或创建（如果分区不存在）该"
"分区。"
msgstr ""
"To write to a partition of an existing table, you can pass in a partition"
" parameter to ``persist``\\ to indicate which partition is the target "
"(for example, ds=******). Note that the table must contain all fields of "
"the DataFrame object and the fields must be the same types. The "
"drop_partition and create_partition parameters are only valid here, "
"indicating respectively whether to delete (if the partition exists) or "
"create (if the partition does not exist) the partition."

#: ../../source/df-basic.rst:865
msgid ""
">>> iris[iris.sepalwidth < 2.5].persist('pyodps_iris4', "
"partition='ds=test', drop_partition=True, create_partition=True)"
msgstr ""

#: ../../source/df-basic.rst:869
msgid ""
"persist 时，默认会覆盖原有数据。例如，当 persist 到一张分区表，对应分区的"
"数据将会被重写。如果写入一张非分区表，整张表的数据都将被重写。如果你想要"
"追加数据，可以使用参数 ``overwrite=False`` 。"
msgstr ""
"Persisting a DataFrame will overwrite existing data by default. For "
"instance, when persisting into a partitioned table, data in corresponding"
" partitions will be overwritten, while persisting into an unpartitioned "
"table will overwrite all data in it. If you want to append data into "
"existing tables or partitions, you may add ``overwrite=False`` ."

#: ../../source/df-basic.rst:871
msgid "写入表时，还可以指定表的生命周期，如下列语句将表的生命周期指定为10天："
msgstr ""
"You can also specify the lifecycle of a table when writing to it. The "
"following example sets the lifecycle of a table to 10 days."

#: ../../source/df-basic.rst:873
msgid ">>> iris[iris.sepalwidth < 2.5].persist('pyodps_iris5', lifecycle=10)"
msgstr ""

#: ../../source/df-basic.rst:877
msgid ""
"如果数据源中没有 ODPS 对象，例如数据源仅为 Pandas，在 persist 时需要手动"
"指定 ODPS 入口对象， 或者将需要的入口对象标明为全局对象，如："
msgstr ""
"If the data source contains no ODPS objects, for example, only pandas "
"data, you need to manually specify the ODPS object or mark the object as "
"global when calling persist. For example:"

#: ../../source/df-basic.rst:880
msgid ""
">>> # 假设入口对象为 o\n"
">>> # 指定入口对象\n"
">>> df.persist('table_name', odps=o)\n"
">>> # 或者可将入口对象标记为全局\n"
">>> o.to_global()\n"
">>> df.persist('table_name')"
msgstr ""
">>> # we assume the entrance object of PyODPS is o\n"
">>> # specify entrance object with odps argument\n"
">>> df.persist('table_name', odps=o)\n"
">>> # or mark the entrance object as global\n"
">>> o.to_global()\n"
">>> df.persist('table_name')"

#: ../../source/df-basic.rst:890
msgid "保存执行结果为 Pandas DataFrame"
msgstr "Save results to pandas DataFrame"

#: ../../source/df-basic.rst:892
msgid ""
"我们可以使用 ``to_pandas``\\ 方法，如果wrap参数为True，将返回PyODPS "
"DataFrame对象。"
msgstr ""
"You can use the ``to_pandas``\\ method. If wrap is set to True, a PyODPS "
"DataFrame object is returned."

#: ../../source/df-basic.rst:894
msgid ""
">>> type(iris[iris.sepalwidth < 2.5].to_pandas())\n"
"pandas.core.frame.DataFrame\n"
">>> type(iris[iris.sepalwidth < 2.5].to_pandas(wrap=True))\n"
"odps.df.core.DataFrame"
msgstr ""

#: ../../source/df-basic.rst:903
msgid ""
"``to_pandas`` 返回的 pandas DataFrame 与直接通过 pandas 创建的 DataFrame "
"没有任何区别， 数据的存储和计算均在本地。如果 ``wrap=True``，生成的即便是"
" PyODPS DataFrame，数据依然在本地。 如果你的数据很大，或者运行环境的内存"
"限制较为严格，请谨慎使用 ``to_pandas``。"
msgstr ""
"There are no differences between pandas DataFrames returned by "
"``to_pandas()`` calls and pandas DataFrames originally created by pandas."
" Therefore all data store and computations are done locally. Even if you "
"added ``wrap=True`` and the function returns a PyODPS DataFrame, the data"
" are still stored and computed locally. If you are handling a huge amount"
" of data, or your running enviromnent is quite restrictive, please be "
"cautious when using ``to_pandas``."

#: ../../source/df-basic.rst:908
msgid "立即运行设置运行参数"
msgstr "Set runtime parameters"

#: ../../source/df-basic.rst:910
msgid ""
"对于立即执行的方法，比如 ``execute``、``persist``、``to_pandas`` 等，可以"
"设置运行时参数（仅对ODPS SQL后端有效 ）。"
msgstr ""
"For actions such as `execute``, ``persist``, and ``to_pandas``, you can "
"set runtime parameters. This is only valid for MaxCompute SQL."

#: ../../source/df-basic.rst:912
msgid "一种方法是设置全局参数。详细参考 :ref:`SQL设置运行参数 <sql_hints>` 。"
msgstr ""
"You can also set global parameters. For details, see :ref:`SQL - runtime "
"parameters <sql_hints>`."

#: ../../source/df-basic.rst:914
msgid ""
"也可以在这些立即执行的方法上，使用 ``hints`` 参数。这样，这些参数只会作用"
"于当前的计算过程。"
msgstr ""
"Additionally, you can use the `hints`` parameter. These parameters are "
"only valid for the current calculation."

#: ../../source/df-basic.rst:917
msgid ""
">>> iris[iris.sepallength < "
"5].to_pandas(hints={'odps.sql.mapper.split.size': 16})"
msgstr ""

#: ../../source/df-basic.rst:923
msgid "运行时显示详细信息"
msgstr "Display details at runtime"

#: ../../source/df-basic.rst:925
msgid "有时，用户需要查看运行时instance的logview时，需要修改全局配置："
msgstr ""
"You sometimes need to modify the global configuration to view the logview"
" of an instance."

#: ../../source/df-basic.rst:927
msgid ""
">>> from odps import options\n"
">>> options.verbose = True\n"
">>>\n"
">>> iris[iris.sepallength < 5].exclude('sepallength')[:5].execute()\n"
"Sql compiled:\n"
"SELECT t1.`sepalwidth`, t1.`petallength`, t1.`petalwidth`, t1.`name`\n"
"FROM odps_test_sqltask_finance.`pyodps_iris` t1\n"
"WHERE t1.`sepallength` < 5\n"
"LIMIT 5\n"
"logview:\n"
"http://logview\n"
"\n"
"   sepalwidth  petallength  petalwidth         name\n"
"0         3.0          1.4         0.2  Iris-setosa\n"
"1         3.2          1.3         0.2  Iris-setosa\n"
"2         3.1          1.5         0.2  Iris-setosa\n"
"3         3.4          1.4         0.3  Iris-setosa\n"
"4         2.9          1.4         0.2  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:948
msgid "用户可以指定自己的日志记录函数，比如像这样："
msgstr "You can specify a logging function as follows:"

#: ../../source/df-basic.rst:950
msgid ""
">>> my_logs = []\n"
">>> def my_logger(x):\n"
">>>     my_logs.append(x)\n"
">>>\n"
">>> options.verbose_log = my_logger\n"
">>>\n"
">>> iris[iris.sepallength < 5].exclude('sepallength')[:5].execute()\n"
"   sepalwidth  petallength  petalwidth         name\n"
"0         3.0          1.4         0.2  Iris-setosa\n"
"1         3.2          1.3         0.2  Iris-setosa\n"
"2         3.1          1.5         0.2  Iris-setosa\n"
"3         3.4          1.4         0.3  Iris-setosa\n"
"4         2.9          1.4         0.2  Iris-setosa\n"
"\n"
">>> print(my_logs)\n"
"['Sql compiled:', 'SELECT t1.`sepalwidth`, t1.`petallength`, "
"t1.`petalwidth`, t1.`name` \\nFROM "
"odps_test_sqltask_finance.`pyodps_iris` t1 \\nWHERE t1.`sepallength` < 5 "
"\\nLIMIT 5', 'logview:', u'http://logview']"
msgstr ""

#: ../../source/df-basic.rst:970
msgid "缓存中间Collection计算结果"
msgstr "Cache intermediate results"

#: ../../source/df-basic.rst:972
msgid ""
"DataFrame的计算过程中，一些Collection被多处使用，或者用户需要查看中间过程"
"的执行结果， 这时用户可以使用 ``cache``\\ 标记某个collection需要被优先"
"计算。"
msgstr ""
"During the calculation of DataFrame, some Collection objects are used "
"multiple times, or you need to check the execution results of an "
"intermediate process, you can use the cache method to mark a collection "
"object so that it is calculated first."

#: ../../source/df-basic.rst:977
msgid "值得注意的是，``cache``\\ 延迟执行，调用cache不会触发立即计算。"
msgstr ""
"Note that ``cache``\\ delays execution. Calling this method does not "
"trigger automatic calculation."

#: ../../source/df-basic.rst:979
msgid ""
">>> cached = iris[iris.sepalwidth < 3.5]['sepallength', 'name'].cache()\n"
">>> df = cached.head(3)\n"
">>> df\n"
"   sepallength         name\n"
"0          4.9  Iris-setosa\n"
"1          4.7  Iris-setosa\n"
"2          4.6  Iris-setosa\n"
">>> cached.head(3)  # 由于cached已经被计算，所以能立刻取到计算结果\n"
"   sepallength         name\n"
"0          4.9  Iris-setosa\n"
"1          4.7  Iris-setosa\n"
"2          4.6  Iris-setosa"
msgstr ""
">>> cached = iris[iris.sepalwidth < 3.5]['sepallength', 'name'].cache()\n"
">>> df = cached.head(3)\n"
">>> df\n"
"   sepallength         name\n"
"0          4.9  Iris-setosa\n"
"1          4.7  Iris-setosa\n"
"2          4.6  Iris-setosa\n"
">>> cached.head(3)  # results can be fetched immediately as cached has "
"already been computed\n"
"   sepallength         name\n"
"0          4.9  Iris-setosa\n"
"1          4.7  Iris-setosa\n"
"2          4.6  Iris-setosa"

#: ../../source/df-basic.rst:998
msgid "异步和并行执行"
msgstr "Asynchronous and parallel executions"

#: ../../source/df-basic.rst:1000
msgid ""
"DataFrame 支持异步操作，对于立即执行的方法，包括 ``execute``、``persist``"
"、``head``、``tail``、``to_pandas`` （其他方法不支持）， 传入 ``async_`` "
"参数，即可以将一个操作异步执行，``timeout`` 参数指定超时时间， 异步返回的"
"是 `Future <https://docs.python.org/3/library/concurrent.futures.html#"
"future-objects>`_ 对象。"
msgstr ""
"DataFrame supports asynchronous operations. For actions such as "
"``execute``, ``persist``, ``head``, ``tail``, and ``to_pandas``, you can "
"pass in the ``async_`` parameter to enable asynchronous executions. The "
"``timeout`` parameter specifies the timeout period, and asynchronous "
"executions return `Future "
"<https://docs.python.org/3/library/concurrent.futures.html#future-"
"objects>`_ objects."

#: ../../source/df-basic.rst:1004
msgid ""
">>> future = iris[iris.sepal_width < 10].head(10, async_=True)\n"
">>> future.result()\n"
"   sepal_length  sepal_width  petal_length  petal_width     category\n"
"0           5.1          3.5           1.4          0.2  Iris-setosa\n"
"1           4.9          3.0           1.4          0.2  Iris-setosa\n"
"2           4.7          3.2           1.3          0.2  Iris-setosa\n"
"3           4.6          3.1           1.5          0.2  Iris-setosa\n"
"4           5.0          3.6           1.4          0.2  Iris-setosa\n"
"5           5.4          3.9           1.7          0.4  Iris-setosa\n"
"6           4.6          3.4           1.4          0.3  Iris-setosa\n"
"7           5.0          3.4           1.5          0.2  Iris-setosa\n"
"8           4.4          2.9           1.4          0.2  Iris-setosa\n"
"9           4.9          3.1           1.5          0.1  Iris-setosa"
msgstr ""

#: ../../source/df-basic.rst:1021
msgid ""
"DataFrame 的并行执行可以使用多线程来并行，单个 expr 的执行可以通过 ``n_"
"parallel`` 参数来指定并发度。 比如，当一个 DataFrame 的执行依赖的多个 "
"cache 的 DataFrame 能够并行执行时，该参数就会生效。"
msgstr ""
"The parallel execution of DataFrame can be achieved by using multiple "
"threads. You can use the ``n_parallel`` parameter to specify the degree "
"of parallelism for each expr execution. The parameter becomes valid when "
"the multiple cached DataFrame objects that a single DataFrame execution "
"depends on can be executed in parallel."

#: ../../source/df-basic.rst:1024
msgid ""
">>> expr1 = "
"iris.groupby('category').agg(value=iris.sepal_width.sum()).cache()\n"
">>> expr2 = "
"iris.groupby('category').agg(value=iris.sepal_length.mean()).cache()\n"
">>> expr3 = "
"iris.groupby('category').agg(value=iris.petal_length.min()).cache()\n"
">>> expr = expr1.union(expr2).union(expr3)\n"
">>> future = expr.execute(n_parallel=3, async_=True, timeout=2)  # 并行和"
"异步执行，2秒超时，返回Future对象\n"
">>> future.result()\n"
"          category    value\n"
"0      Iris-setosa    5.006\n"
"1  Iris-versicolor    5.936\n"
"2   Iris-virginica    6.588\n"
"3      Iris-setosa  170.900\n"
"4  Iris-versicolor  138.500\n"
"5   Iris-virginica  148.700\n"
"6      Iris-setosa    1.000\n"
"7  Iris-versicolor    3.000\n"
"8   Iris-virginica    4.500"
msgstr ""
">>> expr1 = "
"iris.groupby('category').agg(value=iris.sepal_width.sum()).cache()\n"
">>> expr2 = "
"iris.groupby('category').agg(value=iris.sepal_length.mean()).cache()\n"
">>> expr3 = "
"iris.groupby('category').agg(value=iris.petal_length.min()).cache()\n"
">>> expr = expr1.union(expr2).union(expr3)\n"
">>> future = expr.execute(n_parallel=3, async_=True, timeout=2)  # "
"Concurrent and asynchronous execution with 2 seconds timeout, return "
"Future object\n"
">>> future.result()\n"
"          category    value\n"
"0      Iris-setosa    5.006\n"
"1  Iris-versicolor    5.936\n"
"2   Iris-virginica    6.588\n"
"3      Iris-setosa  170.900\n"
"4  Iris-versicolor  138.500\n"
"5   Iris-virginica  148.700\n"
"6      Iris-setosa    1.000\n"
"7  Iris-versicolor    3.000\n"
"8   Iris-virginica    4.500"

#: ../../source/df-basic.rst:1044
msgid ""
"当同时执行多个 expr 时，我们可以用多线程执行，但会面临一个问题， 比如两个"
" DataFrame 有共同的依赖，这个依赖将会被执行两遍。"
msgstr ""
"You can use multiple threads to execute multiple expr objects in "
"parallel, but you may encounter a problem when two DataFrame objects "
"share the same dependency, and this dependency will be executed twice."

#: ../../source/df-basic.rst:1047
msgid ""
"现在我们提供了新的 ``Delay API``， 来将立即执行的操作（包括 ``execute``、"
"``persist``、``head``、``tail``、``to_pandas``，其他方法不支持）变成延迟"
"操作， 并返回 `Future <https://docs.python.org/3/library/concurrent."
"futures.html#future-objects>`_ 对象。 当用户触发delay执行的时候，会去寻找"
"共同依赖，按用户给定的并发度执行，并支持异步执行。"
msgstr ""
"A new ``Delay API`` is provided, which can delay the execution of actions"
" (including ``execute``, ``persist``, ``head``, ``tail``, and "
"``to_pandas``) and return `Future "
"<https://docs.python.org/3/library/concurrent.futures.html#future-"
"objects>`_ objects. When the delay method is triggered, the shared "
"dependency is executed based on the degree of parallelism you have "
"specified. Asynchronous execution is supported."

#: ../../source/df-basic.rst:1052
msgid ""
">>> from odps.df import Delay\n"
">>> delay = Delay()  # 创建Delay对象\n"
">>>\n"
">>> df = iris[iris.sepal_width < 5].cache()  # 有一个共同的依赖\n"
">>> future1 = df.sepal_width.sum().execute(delay=delay)  # 立即返回future"
"对象，此时并没有执行\n"
">>> future2 = df.sepal_width.mean().execute(delay=delay)\n"
">>> future3 = df.sepal_length.max().execute(delay=delay)\n"
">>> delay.execute(n_parallel=3)  # 并发度是3，此时才真正执行。\n"
"|==========================================|   1 /  1  (100.00%)        "
"21s\n"
">>> future1.result()\n"
"458.10000000000014\n"
">>> future2.result()\n"
"3.0540000000000007"
msgstr ""
">>> from odps.df import Delay\n"
">>> delay = Delay()  # create Delay object\n"
">>>\n"
">>> df = iris[iris.sepal_width < 5].cache()  # common dependency of "
"subsequent expressions\n"
">>> future1 = df.sepal_width.sum().execute(delay=delay)  # return Future "
"object, execution not started yet\n"
">>> future2 = df.sepal_width.mean().execute(delay=delay)\n"
">>> future3 = df.sepal_length.max().execute(delay=delay)\n"
">>> delay.execute(n_parallel=3)  # execution starts here with 3 "
"concurrent threads\n"
"|==========================================|   1 /  1  (100.00%)        "
"21s\n"
">>> future1.result()\n"
"458.10000000000014\n"
">>> future2.result()\n"
"3.0540000000000007"

#: ../../source/df-basic.rst:1069
msgid ""
"可以看到上面的例子里，共同依赖的对象会先执行，然后再以并发度为3分别执行"
"future1到future3。 当 ``n_parallel`` 为1时，执行时间会达到37s。"
msgstr ""
"As you can see in the above example, the shared dependency is executed "
"first. Objects future1 to future3 are then executed with the degree of "
"parallelism set to 3. When ``n_parallel`` is set to 1, the execution time"
" reaches 37s."

#: ../../source/df-basic.rst:1072
msgid ""
"``delay.execute`` 也接受 ``async_`` 操作来指定是否异步执行，当异步的时候"
"，也可以指定 ``timeout`` 参数来指定超时时间。"
msgstr ""
"You can also pass in the ``async_`` parameter to ``delay.execute`` to "
"specify whether asynchronous execution is enabled. When enabled, you can "
"use the ``timeout`` parameter to specify the timeout period."

