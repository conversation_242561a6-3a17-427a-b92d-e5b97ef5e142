#!/bin/sh

STAGE_FILES=$(git diff --cached --name-only --diff-filter=ACM)

echo 'check sensitive information ...'
FAIL=0
for FILE in $STAGE_FILES
do
  grep --color -Hni -E "(ssh-rsa|authorized_keys|id_dsa|ssh-keygen)" $FILE && FAIL=1
  grep --color -Hni -E "(private key|secret|signature|accessid|access_id|access_key|accesskey|access_|password)(.*?)(\=|\:)(\s*)(\'|\")[^\$^%][^)]+(\'|\")[^)]*$" $FILE && FAIL=1
  grep --color -Hni -E "jdbc\:odps\:.*?accessId\=[^\.]+)" $FILE && FAIL=1
done

if [ ${FAIL} == 0 ]; then
  echo 'check sensitive information ... passed'
  exit 0
else
  echo 'check sensitive information ... failed'
  exit 1
fi
