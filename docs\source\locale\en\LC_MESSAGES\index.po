# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-18 16:19+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/index.rst:7
msgid "PyODPS: ODPS Python SDK and data analysis framework"
msgstr ""

#: ../../source/index.rst:9
msgid ""
"`PyODPS <https://github.com/aliyun/aliyun-odps-python-sdk>`_ 是 "
"MaxCompute (ODPS) 的 Python 版本 SDK。它提供了对 MaxCompute 对象的"
"基本操作，并提供了 DataFrame 框架，能轻松在 MaxCompute (ODPS) 上进行"
"数据分析。"
msgstr ""
"`PyODPS <https://github.com/aliyun/aliyun-odps-python-sdk>`_ is the "
"Python SDK of MaxCompute. It supports basic actions on MaxCompute objects"
" and the DataFrame framework for ease of data analysis on MaxCompute."

#: ../../source/index.rst:-1
msgid "安装"
msgstr "Installation"

#: ../../source/index.rst:18
msgid ""
"PyODPS 支持 Python 2.7 以上的 Python 版本，包括 Python 3。系统安装了 pip "
"后，只需运行："
msgstr ""
"PyODPS supports Python 2.7 and later versions (including Python 3). After"
" installing PIP in the system, you only need to run"

#: ../../source/index.rst:20
msgid "pip install pyodps"
msgstr ""

#: ../../source/index.rst:24
msgid "PyODPS 的相关依赖会自动安装。"
msgstr "The related dependencies of PyODPS are automatically installed."

#: ../../source/index.rst:-1
msgid "快速开始"
msgstr "Quick start"

#: ../../source/index.rst:31
msgid ""
"你可以使用阿里云 Access ID / Key 来初始化一个 MaxCompute 的入口（参数值请"
"自行替换，不包含星号）。"
msgstr ""
"You can use access id and key of an Alibaba Cloud account to initialize a"
" MaxCompute (ODPS) entrance object, as shown in the following code. "
"Parameters of ```ODPS``` function should be replaced with your account "
"and project information. Asterisks should be removed."

#: ../../source/index.rst:33
msgid ""
"import os\n"
"from odps import ODPS\n"
"# 确保 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为用户 Access Key ID，\n"
"# ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为用户 Access Key Secret，\n"
"# 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
")"
msgstr ""
"import os\n"
"from odps import ODPS\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to Access Key ID of user\n"
"# while environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to "
"Access Key Secret of user.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
")"

#: ../../source/index.rst:47
msgid "这样就已经初始化，就可以对表、资源、函数等进行操作了。"
msgstr ""
"After completing initialization, you can operate tables, resources, and "
"functions."

#: ../../source/index.rst:49
msgid ""
"如果你使用 `STS Token <https://help.aliyun.com/document_detail/112449."
"html>`_ 访问 MaxCompute，可以使用下面的语句初始化 MaxCompute 入口对象："
msgstr ""
"If you need to use `STS Token <https://www.alibabacloud.com/help/en/iot-"
"platform/latest/ram-and-sts>`_ to access MaxCompute, you may use code "
"below to create a MaxCompute (ODPS) entrance object."

#: ../../source/index.rst:52
msgid ""
"import os\n"
"from odps import ODPS\n"
"from odps.accounts import StsAccount\n"
"# 确保 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为 Access Key ID，\n"
"# ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为 Access Key Secret，\n"
"# ALIBABA_CLOUD_STS_TOKEN 环境变量设置为 STS Token，\n"
"# 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
"account = StsAccount(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    os.getenv('ALIBABA_CLOUD_STS_TOKEN'),\n"
")\n"
"o = ODPS(\n"
"    account=account,\n"
"    project='**your-default-project**',\n"
"    endpoint='**your-end-point**',\n"
")"
msgstr ""
"import os\n"
"from odps import ODPS\n"
"from odps.accounts import StsAccount\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to acquired Access Key ID,\n"
"# environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to acquired "
"Access Key Secret\n"
"# while environment variable ALIBABA_CLOUD_STS_TOKEN set to acquired STS "
"token.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
"account = StsAccount(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    os.getenv('ALIBABA_CLOUD_STS_TOKEN'),\n"
")\n"
"o = ODPS(\n"
"    account=account,\n"
"    project='**your-default-project**',\n"
"    endpoint='**your-end-point**',\n"
")"

#: ../../source/index.rst:72
msgid ""
"在主入口，我们对于主要的 MaxCompute 对象都提供了最基本的几个操作，包括 ``"
"list``、``get``、\\ ``exist``、``create``、``delete``。"
msgstr ""
"We provide elementary functions for major MaxCompute objects, including "
"``list``, ``get``, ``exist``, ``create`` and ``delete``."

#: ../../source/index.rst:75
msgid ""
"我们会对这几部分来分别展开说明。后文中的 o 对象如无说明均指的是 "
"MaxCompute 入口对象。"
msgstr ""
"We will elaborate every object in the next chapters. If not mentioned, "
"the variable ``o`` represents the MaxCompute (ODPS) entrance object."

