# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-17 11:44+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-functions.rst:4
msgid "函数"
msgstr "Functions"

#: ../../source/base-functions.rst:6
msgid ""
"ODPS用户可以编写自定义 `函数 <https://help.aliyun.com/document_detail/"
"27823.html>`_ 用在ODPS SQL中。"
msgstr ""
"You can write user-defined `functions "
"<https://www.alibabacloud.com/help/en/doc-detail/27823.htm>`_ (UDFs) to "
"MaxCompute SQL."

#: ../../source/base-functions.rst:9
msgid "基本操作"
msgstr "Basic operations"

#: ../../source/base-functions.rst:11
msgid ""
"可以调用 ODPS 入口对象的 :meth:`~odps.ODPS.list_functions` 来获取项目空间"
"下的所有函数， :meth:`~odps.ODPS.exist_function` 能判断是否存在函数，\\ :"
"meth:`~odps.ODPS.get_function` 获取函数对象。"
msgstr ""
"Use :meth:`~odps.ODPS.list_functions` as the ODPS object to obtain all "
"functions in the project. Use :meth:`~odps.ODPS.exist_function` to check "
"whether the specified function exists. Use "
":meth:`~odps.ODPS.get_function` to obtain the object of a function."

#: ../../source/base-functions.rst:16
msgid "创建函数"
msgstr "Create functions"

#: ../../source/base-functions.rst:18
msgid ""
">>> # 引用当前 project 中的资源\n"
">>> resource = o.get_resource('my_udf.py')\n"
">>> function = o.create_function('test_function', "
"class_type='my_udf.Test', resources=[resource])\n"
">>> # 引用其他 project 中的资源\n"
">>> resource2 = o.get_resource('my_udf.py', project='another_project')\n"
">>> function2 = o.create_function('test_function2', "
"class_type='my_udf.Test', resources=[resource2])"
msgstr ""
">>> # reference resources in the current project\n"
">>> resource = o.get_resource('my_udf.py')\n"
">>> function = o.create_function('test_function', "
"class_type='my_udf.Test', resources=[resource])\n"
">>> # reference resources in other projects\n"
">>> resource2 = o.get_resource('my_udf.py', project='another_project')\n"
">>> function2 = o.create_function('test_function2', "
"class_type='my_udf.Test', resources=[resource2])"

#: ../../source/base-functions.rst:28
msgid "删除函数"
msgstr "Delete functions"

#: ../../source/base-functions.rst:30
msgid ""
">>> o.delete_function('test_function')\n"
">>> function.drop()  # Function对象存在时直接调用drop"
msgstr ""
">>> o.delete_function('test_function')\n"
">>> function.drop()  # call drop method of a Function instance to delete"

#: ../../source/base-functions.rst:36
msgid "更新函数"
msgstr "Update functions"

#: ../../source/base-functions.rst:38
msgid "只需对函数调用 ``update`` 方法即可。"
msgstr "To update functions, use the ``update`` method."

#: ../../source/base-functions.rst:40
msgid ""
">>> function = o.get_function('test_function')\n"
">>> new_resource = o.get_resource('my_udf2.py')\n"
">>> function.class_type = 'my_udf2.Test'\n"
">>> function.resources = [new_resource, ]\n"
">>> function.update()  # 更新函数"
msgstr ""
">>> function = o.get_function('test_function')\n"
">>> new_resource = o.get_resource('my_udf2.py')\n"
">>> function.class_type = 'my_udf2.Test'\n"
">>> function.resources = [new_resource, ]\n"
">>> function.update()  # update metadata on the function object"

