# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-19 22:09+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-tables.rst:4
msgid "表"
msgstr "Tables"

#: ../../source/base-tables.rst:6
msgid ""
"`表 <https://help.aliyun.com/document_detail/27819.html>`_ 是ODPS的数据"
"存储单元。"
msgstr ""
"`Tables <https://www.alibabacloud.com/help/en/doc-detail/27819.htm>`_ are"
" the data storage unit in MaxCompute."

#: ../../source/base-tables.rst:9 ../../source/base-tables.rst:507
msgid "基本操作"
msgstr "Basic operations"

#: ../../source/base-tables.rst:13
msgid ""
"本文档中的代码对 PyODPS 0.11.3 及后续版本有效。对早于 0.11.3 版本的 "
"PyODPS，请使用 ``odps.models.Schema`` 代替 ``odps.models.TableSchema``，"
"使用 ``schema`` 属性代替 ``table_schema`` 属性。"
msgstr ""
"Code in this document is only guaranteed to work under PyODPS 0.11.3 and "
"later versions. For PyODPS earlier than 0.11.3, please replace class "
"``odps.models.Schema`` with ``odps.models.TableSchema`` and ``schema`` "
"property with ``table_schema``."

#: ../../source/base-tables.rst:16
msgid ""
"我们可以用 ODPS 入口对象的 :meth:`~odps.ODPS.list_tables` 来列出项目空间"
"下的所有表。"
msgstr ""
"Use the :meth:`~odps.ODPS.list_tables` method as the ODPS object to list "
"all tables in a project."

#: ../../source/base-tables.rst:18
msgid ""
"for table in o.list_tables():\n"
"    print(table.name)"
msgstr ""

#: ../../source/base-tables.rst:23
msgid "可以通过 ``prefix`` 参数只列举给定前缀的表："
msgstr "You can list all tables with given prefix with ``prefix`` argument."

#: ../../source/base-tables.rst:25
msgid ""
"for table in o.list_tables(prefix=\"table_prefix\"):\n"
"    print(table.name)"
msgstr ""

#: ../../source/base-tables.rst:30
msgid ""
"通过该方法获取的 Table 对象不会自动加载表名以外的属性，此时获取这些属性（"
"例如 ``table_schema`` 或者 ``creation_time``）可能导致额外的请求并造成"
"额外的时间开销。如果需要在列举表的同时读取这些属性，在 PyODPS 0.11.5 及"
"后续版本中，可以为 :meth:`~odps.ODPS.list_tables` 添加 ``extended=True`` "
"参数："
msgstr ""
"Table objects obtained with code above do not load properties other than "
"names. If you get properties like ``table_schema`` or ``creation_time``, "
"an extra remote call will be performed which may cause extra delay. If "
"you need to get these properties at the same time when listing tables, "
"you can add ``extended=True`` argument for :meth:`~odps.ODPS.list_tables`"
" in PyODPS 0.11.5 or later."

#: ../../source/base-tables.rst:34
msgid ""
"for table in o.list_tables(extended=True):\n"
"    print(table.name, table.creation_time)"
msgstr ""

#: ../../source/base-tables.rst:39
msgid "如果你需要按类型列举表，可以指定 ``type`` 参数。不同类型的表列举方法如下："
msgstr ""
"You can list tables of given type by specifying ``type`` argument. "
"Examples of listing different types of tables are shown below."

#: ../../source/base-tables.rst:41
msgid ""
"managed_tables = list(o.list_tables(type=\"managed_table\"))  # 列举内置"
"表\n"
"external_tables = list(o.list_tables(type=\"external_table\"))  # 列举"
"外表\n"
"virtual_views = list(o.list_tables(type=\"virtual_view\"))  # 列举视图\n"
"materialized_views = list(o.list_tables(type=\"materialized_view\"))  # "
"列举物化视图"
msgstr ""
"managed_tables = list(o.list_tables(type=\"managed_table\"))  # iterate "
"over MaxCompute-managed tables\n"
"external_tables = list(o.list_tables(type=\"external_table\"))  # iterate"
" over external tables\n"
"virtual_views = list(o.list_tables(type=\"virtual_view\"))  # iterate "
"over non-materialized views\n"
"materialized_views = list(o.list_tables(type=\"materialized_view\"))  # "
"iterate over materialized views"

#: ../../source/base-tables.rst:48
msgid "通过调用 :meth:`~odps.ODPS.exist_table` 来判断表是否存在。"
msgstr ""
"Use :meth:`~odps.ODPS.exist_table` to check whether the specified table "
"exists."

#: ../../source/base-tables.rst:50
msgid "o.exist_table('dual')"
msgstr ""

#: ../../source/base-tables.rst:54
msgid "通过调用 :meth:`~odps.ODPS.get_table` 来获取表。"
msgstr "Use :meth:`~odps.ODPS.get_table` to obtain the specified table."

#: ../../source/base-tables.rst:56
msgid ""
">>> t = o.get_table('dual')\n"
">>> t.table_schema\n"
"odps.Schema {\n"
"  c_int_a                 bigint\n"
"  c_int_b                 bigint\n"
"  c_double_a              double\n"
"  c_double_b              double\n"
"  c_string_a              string\n"
"  c_string_b              string\n"
"  c_bool_a                boolean\n"
"  c_bool_b                boolean\n"
"  c_datetime_a            datetime\n"
"  c_datetime_b            datetime\n"
"}\n"
">>> t.lifecycle\n"
"-1\n"
">>> print(t.creation_time)\n"
"2014-05-15 14:58:43\n"
">>> t.is_virtual_view\n"
"False\n"
">>> t.size\n"
"1408\n"
">>> t.comment\n"
"'Dual Table Comment'\n"
">>> t.table_schema.columns\n"
"[<column c_int_a, type bigint>,\n"
" <column c_int_b, type bigint>,\n"
" <column c_double_a, type double>,\n"
" <column c_double_b, type double>,\n"
" <column c_string_a, type string>,\n"
" <column c_string_b, type string>,\n"
" <column c_bool_a, type boolean>,\n"
" <column c_bool_b, type boolean>,\n"
" <column c_datetime_a, type datetime>,\n"
" <column c_datetime_b, type datetime>]\n"
">>> t.table_schema['c_int_a']\n"
"<column c_int_a, type bigint>\n"
">>> t.table_schema['c_int_a'].comment\n"
"'Comment of column c_int_a'"
msgstr ""

#: ../../source/base-tables.rst:99
msgid "通过提供 ``project`` 参数，来跨project获取表。"
msgstr ""
"You can also provide the ``project`` parameter to obtain the specified "
"table from another project."

#: ../../source/base-tables.rst:101
msgid ">>> t = o.get_table('dual', project='other_project')"
msgstr ""

#: ../../source/base-tables.rst:106
msgid "创建表"
msgstr "Create tables"

#: ../../source/base-tables.rst:108
msgid ""
"你可以使用\\ :ref:`表 schema <table_schema>` 通过 :meth:`~odps.ODPS."
"create_table` 方法来创建表，方法如下："
msgstr ""
"You can use :meth:`~odps.ODPS.create_table` with :ref:`the table schema "
"<table_schema>` to create a table in the following way:"

#: ../../source/base-tables.rst:110
msgid ""
">>> from odps.models import TableSchema, Column, Partition\n"
">>>\n"
">>> schema = TableSchema.from_lists(\n"
">>>    ['num', 'num2', 'arr'], ['bigint', 'double', 'array<int>'], "
"['pt'], ['string']\n"
">>> )\n"
">>> table = o.create_table('my_new_table', schema)\n"
">>> table = o.create_table('my_new_table', schema, if_not_exists=True)  #"
" 只有不存在表时才创建\n"
">>> table = o.create_table('my_new_table', schema, lifecycle=7)  # 设置"
"生命周期"
msgstr ""
">>> from odps.models import TableSchema, Column, Partition\n"
">>>\n"
">>> schema = TableSchema.from_lists(\n"
">>>    ['num', 'num2', 'arr'], ['bigint', 'double', 'array<int>'], "
"['pt'], ['string']\n"
">>> )\n"
">>> table = o.create_table('my_new_table', schema)\n"
">>> table = o.create_table('my_new_table', schema, if_not_exists=True)  #"
" create table only when the table does not exist\n"
">>> table = o.create_table('my_new_table', schema, lifecycle=7)  # "
"configure lifecycle of the table (in days)"

#: ../../source/base-tables.rst:122
msgid "更简单的方式是采用“字段名 字段类型”字符串来创建表，方法如下："
msgstr ""
"An easier way is to use a string in the structure of “field name field "
"type” to create the table, as shown in the following code:"

#: ../../source/base-tables.rst:124
msgid ""
">>> table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
">>> # 创建分区表可传入 (表字段列表, 分区字段列表)\n"
">>> table = o.create_table('my_new_table', ('num bigint, num2 double', "
"'pt string'), if_not_exists=True)"
msgstr ""
">>> table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
">>> # a tuple like (column list, partition list) can be passed to create "
"a partitioned table\n"
">>> table = o.create_table('my_new_table', ('num bigint, num2 double', "
"'pt string'), if_not_exists=True)"

#: ../../source/base-tables.rst:131
msgid ""
"在未经设置的情况下，创建表时，只允许使用 bigint、double、decimal、string"
"、datetime、boolean、map 和 array 类型。\\ 如果你使用的是位于公共云上的"
"服务，或者支持 tinyint、struct 等新类型，可以设置 ``options.sql.use_odps2"
"_extension = True`` 打开这些类型的支持，示例如下："
msgstr ""
"By default, you can only use the bigint, double, decimal, string, "
"datetime, boolean, map and array types to create a table. If you use "
"public cloud services, you can set ``options.sql.use_odps2_extension = "
"True`` to enable more types such as tinyint and struct, as shown in the "
"following code:"

#: ../../source/base-tables.rst:135
msgid ""
">>> from odps import options\n"
">>> options.sql.use_odps2_extension = True\n"
">>> table = o.create_table('my_new_table', 'cat smallint, content "
"struct<title:varchar(100), body string>')"
msgstr ""

#: ../../source/base-tables.rst:141
msgid ""
":meth:`~odps.ODPS.create_table` 方法也提供了其他参数，可用于设置表属性及"
"事务性等参数。例如，下面的调用创建了一张 ACID 2.0 表并指定 ``key`` 为主键"
"（``key``必须指定为非空）。"
msgstr ""
"Other parameters of :meth:`~odps.ODPS.create_table` can be used to set "
"table properties or transaction properties. For instance, code below "
"creates an ACID 2.0 table and specify ``key`` as the primary key. The "
"column ``key`` should be specified as ``NOT NULL``."

#: ../../source/base-tables.rst:144
msgid ""
">>> table = o.create_table('my_trans_table', 'key string not null, value "
"string',\n"
">>>                        primary_key=['key'], transactional=True)"
msgstr ""

#: ../../source/base-tables.rst:151
msgid "同步表更新"
msgstr "Synchronize table updates"

#: ../../source/base-tables.rst:153
msgid ""
"有时候，一个表可能被别的程序做了更新，比如schema有了变化。此时可以调用 :"
"meth:`~odps.models.Table.reload` 方法来更新。"
msgstr ""
"If a table has been updated by another program and has changes in the "
"schema, you can use :meth:`~odps.models.Table.reload` to synchronize the "
"update."

#: ../../source/base-tables.rst:155
msgid ">>> table.reload()"
msgstr ""

#: ../../source/base-tables.rst:161
msgid "读写数据"
msgstr "Read and write data"

#: ../../source/base-tables.rst:165
msgid "获取表数据"
msgstr "Obtain table data"

#: ../../source/base-tables.rst:167
msgid ""
"有若干种方法能够获取表数据。首先，如果只是查看每个表的开始的小于1万条数据"
"，则可以使用 :meth:`~odps.models.Table.head` 方法。"
msgstr ""
"You can obtain table data in different ways. First, you can use "
":meth:`~odps.models.Table.head` to retrieve the first 10,000 or fewer "
"data items in each table."

#: ../../source/base-tables.rst:169
msgid ""
">>> t = o.get_table('dual')\n"
">>> for record in t.head(3):\n"
">>>     # 处理每个Record对象"
msgstr ""
">>> t = o.get_table('dual')\n"
">>> for record in t.head(3):\n"
">>>     # process every Record object"

#: ../../source/base-tables.rst:178
msgid ""
"其次，在 table 实例上可以执行 :meth:`~odps.models.Table.open_reader` 操作"
"来打一个 reader 来读取数据。如果表为分区表，需要引入 ``partition`` 参数"
"指定需要读取的分区。"
msgstr ""
"Then, use :meth:`~odps.models.Table.open_reader` as the table object to "
"open a reader and read the data. If you need to read data from a "
"partitioned table, you need to add a ``partition`` argument to specify "
"the partition to read."

#: ../../source/base-tables.rst:181
msgid "使用 with 表达式的写法，with 表达式会保证离开时关闭 reader："
msgstr ""
"Open the reader using a WITH clause, as shown in the following code. It "
"is ensured by with expression that the reader is closed once the with "
"block is exited."

#: ../../source/base-tables.rst:183
msgid ""
">>> with t.open_reader(partition='pt=test,pt2=test2') as reader:\n"
">>>     count = reader.count\n"
">>>     for record in reader[5:10]:  # 可以执行多次，直到将count数量的"
"record读完，这里可以改造成并行操作\n"
">>>         # 处理一条记录"
msgstr ""
">>> with t.open_reader(partition='pt=test,pt2=test2') as reader:\n"
">>>     count = reader.count\n"
">>>     for record in reader[5:10]:  # This line can be executed many "
"times until all records are visited. Parallelism can also be introduced."
"\n"
">>>         # process one record"

#: ../../source/base-tables.rst:190
msgid "不使用 with 表达式的写法："
msgstr ""
"Open the reader without using a WITH clause, as shown in the following "
"code:"

#: ../../source/base-tables.rst:192
msgid ""
">>> reader = t.open_reader(partition='pt=test,pt2=test2')\n"
">>> count = reader.count\n"
">>> for record in reader[5:10]:  # 可以执行多次，直到将count数量的record"
"读完，这里可以改造成并行操作\n"
">>>     # 处理一条记录\n"
">>> reader.close()"
msgstr ""
">>> reader = t.open_reader(partition='pt=test,pt2=test2')\n"
">>> count = reader.count\n"
">>> for record in reader[5:10]:  # This line can be executed many times "
"until all records are visited. Parallelism can also be introduced.\n"
">>>     # process one record\n"
">>> reader.close()"

#: ../../source/base-tables.rst:200
msgid ""
"更简单的调用方法是使用 ODPS 对象的 :meth:`~odps.ODPS.read_table` 方法，"
"例如"
msgstr ""
"An easier way is to use :meth:`~odps.ODPS.read_table` as the ODPS object,"
" as shown in the following code:"

#: ../../source/base-tables.rst:202
msgid ""
">>> for record in o.read_table('test_table', "
"partition='pt=test,pt2=test2'):\n"
">>>     # 处理一条记录"
msgstr ""
">>> for record in o.read_table('test_table', "
"partition='pt=test,pt2=test2'):\n"
">>>     # process one record"

#: ../../source/base-tables.rst:207
msgid ""
"从 0.11.2 开始，PyODPS 支持使用 `Arrow <https://arrow.apache.org/>`_ 格式"
"读写数据，该格式可以以更高\\ 效率与 pandas 等格式互相转换。安装 pyarrow "
"后，在调用 ``open_reader`` 时增加 ``arrow=True`` 参数，即可按 `Arrow "
"RecordBatch <https://arrow.apache.org/docs/python/data.html#record-"
"batches>`_ 格式读取表内容。"
msgstr ""
"Since 0.11.2, PyODPS supports reading and writing table data with `Arrow "
"<https://arrow.apache.org/>`_ format, which can be converted from and to "
"pandas or other formats with high efficiency. After installing pyarrow, "
"you can read data from tables with `Arrow RecordBatch "
"<https://arrow.apache.org/docs/python/data.html#record-batches>`_ format "
"by adding ``arrow=True`` argument when calling ``open_reader`` method."

#: ../../source/base-tables.rst:212
msgid ""
">>> with t.open_reader(partition='pt=test,pt2=test2', arrow=True) as "
"reader:\n"
">>>     count = reader.count\n"
">>>     for batch in reader:  # 可以执行多次，直到将所有 RecordBatch 读完"
"\n"
">>>         # 处理一个 RecordBatch，例如转换为 Pandas\n"
">>>         print(batch.to_pandas())"
msgstr ""
">>> with t.open_reader(partition='pt=test,pt2=test2', arrow=True) as "
"reader:\n"
">>>     count = reader.count\n"
">>>     for batch in reader:  # This line can be executed many times "
"until all record batches are visited.\n"
">>>         # process one RecordBatch, for instance, convert to Pandas\n"
">>>         print(batch.to_pandas())"

#: ../../source/base-tables.rst:220
msgid ""
"你也可以直接调用 reader 上的 ``to_pandas`` 方法直接从 reader 获取 pandas "
"DataFrame。 读取时，可以指定起始行号（从0开始）和行数。如果不指定，则默认"
"读取所有数据。"
msgstr ""
"You can also call ``to_pandas`` method on readers to read pandas "
"DataFrame. Start row index (starts from 0) and row count can be specified"
" on reading. If row indexes are not specified, all data will be read by "
"default."

#: ../../source/base-tables.rst:223
msgid ""
">>> with t.open_reader(partition='pt=test,pt2=test2', arrow=True) as "
"reader:\n"
">>>     # 指定起始行号和行数\n"
">>>     pd_df = reader.to_pandas(start=10, count=20)\n"
">>>     # 如不指定，则读取所有数据\n"
">>>     pd_df = reader.to_pandas()"
msgstr ""
">>> with t.open_reader(partition='pt=test,pt2=test2', arrow=True) as "
"reader:\n"
">>>     # specify start row index and row count\n"
">>>     pd_df = reader.to_pandas(start=10, count=20)\n"
">>>     # if not specified, all data will be read\n"
">>>     pd_df = reader.to_pandas()"

#: ../../source/base-tables.rst:233
msgid "你可以利用多进程加速读取 Pandas DataFrame："
msgstr "You can read data directly into Pandas DataFrames with multiple processes."

#: ../../source/base-tables.rst:235
msgid ""
">>> import multiprocessing\n"
">>> n_process = multiprocessing.cpu_count()\n"
">>> with t.open_reader(partition='pt=test,pt2=test2', arrow=True) as "
"reader:\n"
">>>     pd_df = reader.to_pandas(n_process=n_process)"
msgstr ""

#: ../../source/base-tables.rst:242
msgid ""
"为方便读取数据为 pandas，从 PyODPS 0.12.0 开始，Table 和 Partition 对象"
"支持直接调用 ``to_pandas`` 方法。"
msgstr ""
"To facilitate reading data as pandas, since PyODPS 0.12.0, ``to_pandas`` "
"method is added to table and partition objects."

#: ../../source/base-tables.rst:245
msgid ""
">>> # 将表读取为 pandas DataFrame\n"
">>> pd_df = table.to_pandas(start=10, count=20)\n"
">>> # 通过2个进程读取所有数据\n"
">>> pd_df = table.to_pandas(n_process=2)\n"
">>> # 将分区读取为 pandas\n"
">>> pd_df = partitioned_table.to_pandas(partition=\"pt=test\", start=10, "
"count=20)"
msgstr ""
">>> # read table as pandas dataframe\n"
">>> pd_df = table.to_pandas(start=10, count=20)\n"
">>> # read all data with 2 processes\n"
">>> pd_df = table.to_pandas(n_process=2)\n"
">>> # read partition as pandas\n"
">>> pd_df = partitioned_table.to_pandas(partition=\"pt=test\", start=10, "
"count=20)"

#: ../../source/base-tables.rst:254
msgid ""
"与此同时，从 PyODPS 0.12.0 开始，你也可以使用 ``iter_pandas`` 方法从一张"
"表或分区按多个批次读取 pandas DataFrame，并通过 ``batch_size`` 参数指定"
"每次读取的 DataFrame 批次大小，该大小默认值为 ``options.tunnel.read_row_"
"batch_size`` 指定，默认为 1024。"
msgstr ""
"At the same time, since PyODPS 0.12.0, you can also use ``iter_pandas`` "
"method to read multiple batches of pandas DataFrames from a table or "
"partition. The size of Dataframes can be specified with ``batch_size`` "
"argument, whose default value is specified with "
"``options.tunnel.read_row_batch_size`` and the default value is 1024."

#: ../../source/base-tables.rst:258
msgid ""
">>> # 以默认 batch_size 读取所有数据\n"
">>> for batch in table.iter_pandas():\n"
">>>     print(batch)\n"
">>> # 以 batch_size==100 读取前 1000 行数据\n"
">>> for batch in table.iter_pandas(batch_size=100, start=0, count=1000):\n"
">>>     print(batch)"
msgstr ""
">>> # iterate all data with default batch_size\n"
">>> for batch in table.iter_pandas():\n"
">>>     print(batch)\n"
">>> # iterate first 1000 rows with batch_size==100\n"
">>> for batch in table.iter_pandas(batch_size=100, start=0, count=1000):\n"
">>>     print(batch)"

#: ../../source/base-tables.rst:269
msgid ""
"``open_reader``、``read_table`` 以及 ``to_pandas`` 方法仅支持读取单个分区"
"。如果需要读取多个分区\\ 的值，例如读取所有符合 ``dt>20230119`` 这样条件"
"的分区，需要使用 ``iterate_partitions`` 方法，详见 :ref:`遍历表分区 <"
"iterate_partitions>` 章节。"
msgstr ""
"``open_reader``, ``read_table`` and ``to_pandas`` only supports reading "
"from one single partition. If you need to read from multiple partitions, "
"for instance, partitions specified by the inequality ``dt>20230119``, you"
" need to use method ``iterate_partitions``. For more details please take "
"a look at :ref:`iterating over table partitions <iterate_partitions>` "
"section."

#: ../../source/base-tables.rst:273
msgid ""
"导出数据是否包含分区列的值由输出格式决定。Record 格式数据默认包含分区列的"
"值，而 Arrow 格式默认不包含。\\ 从 PyODPS 0.12.0 开始，你可以通过指定 ``"
"append_partitions=True`` 显示引入分区列的值，通过 ``append_partitions="
"False`` 将分区列排除在结果之外。"
msgstr ""
"Data read from tables can include partition columns or not, depending on "
"the format. Record format contains partition columns by default, while "
"arrow format does not. Since PyODPS 0.12.0, you can explicitly include "
"partition columns by specifying ``append_partitions=True``, and exclude "
"them by specifying ``append_partitions=False``."

#: ../../source/base-tables.rst:280
msgid "向表写数据"
msgstr "Write data to tables"

#: ../../source/base-tables.rst:282
msgid ""
"类似于 :meth:`~odps.models.Table.open_reader`，table对象同样能执行 :meth:"
"`~odps.models.Table.open_writer` 来打开writer，并写数据。如果表为分区表，"
"需要引入 ``partition`` 参数指定需要写入的分区。"
msgstr ""
"Similar to :meth:`~odps.models.Table.open_reader`, you can use "
":meth:`~odps.models.Table.open_writer` as the table object to open a "
"writer and write data to the table. If the table to write is partitioned,"
" you need to add a ``partition`` argument to specify the partition to "
"write into."

#: ../../source/base-tables.rst:285
msgid ""
"使用 with 表达式的写法，with 表达式会保证离开时关闭 writer 并提交所有数据"
"："
msgstr ""
"Open the reader using a WITH clause, as shown in the following code. It "
"is ensured by with expression that the writer is closed once the with "
"block is exited and all written data are committed."

#: ../../source/base-tables.rst:287
msgid ""
">>> with t.open_writer(partition='pt=test') as writer:\n"
">>>     records = [[111, 'aaa', True],                 # 这里可以是list\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     writer.write(records)  # 这里records可以是可迭代对象\n"
">>>\n"
">>>     records = [t.new_record([111, 'aaa', True]),   # 也可以是Record"
"对象\n"
">>>                t.new_record([222, 'bbb', False]),\n"
">>>                t.new_record([333, 'ccc', True]),\n"
">>>                t.new_record([444, '中文', False])]\n"
">>>     writer.write(records)\n"
">>>"
msgstr ""
">>> with t.open_writer(partition='pt=test') as writer:\n"
">>>     records = [[111, 'aaa', True],                 # a list can be "
"used here\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     writer.write(records)  # records can also be iterable objects\n"
">>>\n"
">>>     records = [t.new_record([111, 'aaa', True]),   # a list with "
"records can also be used\n"
">>>                t.new_record([222, 'bbb', False]),\n"
">>>                t.new_record([333, 'ccc', True]),\n"
">>>                t.new_record([444, '中文', False])]\n"
">>>     writer.write(records)\n"
">>>"

#: ../../source/base-tables.rst:304
msgid "如果分区不存在，可以使用 ``create_partition`` 参数指定创建分区，如"
msgstr ""
"If the specified partition does not exist, use the ``create_partition`` "
"parameter to create a partition, as shown in the following code:"

#: ../../source/base-tables.rst:306
msgid ""
">>> with t.open_writer(partition='pt=test', create_partition=True) as "
"writer:\n"
">>>     records = [[111, 'aaa', True],                 # 这里可以是list\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     writer.write(records)  # 这里records可以是可迭代对象"
msgstr ""
">>> with t.open_writer(partition='pt=test', create_partition=True) as "
"writer:\n"
">>>     records = [[111, 'aaa', True],                 # a list can be "
"used here\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     writer.write(records)  # records can also be iterable objects"

#: ../../source/base-tables.rst:315
msgid ""
"更简单的写数据方法是使用 ODPS 对象的 :meth:`~odps.ODPS.write_table` 方法"
"，例如"
msgstr ""
"An easier way is to use :meth:`~odps.ODPS.write_table` as the ODPS object"
" to write data, as shown in the following code:"

#: ../../source/base-tables.rst:317
msgid ""
">>> records = [[111, 'aaa', True],                 # 这里可以是list\n"
">>>            [222, 'bbb', False],\n"
">>>            [333, 'ccc', True],\n"
">>>            [444, '中文', False]]\n"
">>> o.write_table('test_table', records, partition='pt=test', "
"create_partition=True)"
msgstr ""
">>> records = [[111, 'aaa', True],                 # a list can be used "
"here\n"
">>>            [222, 'bbb', False],\n"
">>>            [333, 'ccc', True],\n"
">>>            [444, '中文', False]]\n"
">>> o.write_table('test_table', records, partition='pt=test', "
"create_partition=True)"

#: ../../source/base-tables.rst:327
msgid ""
"**注意**\\ ：每次调用 :meth:`~odps.ODPS.write_table`，MaxCompute 都会在"
"服务端生成一个文件。\\ 这一操作需要较大的时间开销，同时过多的文件会降低"
"后续的查询效率。因此，我们建议在使用 :meth:`~odps.ODPS.write_table` 方法"
"时，一次性写入多组数据，或者传入一个 generator 对象。"
msgstr ""
"**Note**\\ ：Every time when :meth:`~odps.ODPS.write_table` is invoked，"
"MaxCompute generates a new file on the server side, which is an expensive"
" operation that reduces the throughput drastically. What's more, too many"
" files may increase query time on that table. Hence we propose writing "
"multiple records or passing a Python generator object when calling :meth:"
"`~odps.ODPS.write_table`."

#: ../../source/base-tables.rst:331
msgid ""
":meth:`~odps.ODPS.write_table` 写表时会追加到原有数据。如果需要覆盖数据，"
"可以为 :meth:`~odps.ODPS.write_table` 增加一个参数 ``overwrite=True``（仅"
"在 0.11.1 以后支持），或者调用 :meth:`Table.truncate() <odps.models.Table"
".truncate>` / 删除分区后再建立分区。"
msgstr ""
"When calling :meth:`~odps.ODPS.write_table`, new data will be appended to"
" existing data. If you need to overwrite existing data, you can add an "
"argument ``overwrite=True`` to :meth:`~odps.ODPS.write_table` call when "
"you are using PyODPS later than 0.11.1, or call :meth:`Table.truncate() "
"<odps.models.Table.truncate>` on tables or partitions."

#: ../../source/base-tables.rst:335
msgid ""
"你可以使用多线程写入数据。从 PyODPS 0.11.6 开始，直接将 open_writer 创建"
"的 Writer 对象分发到\\ 各个线程中即可完成多线程写入，写入时请注意不要关闭"
" writer，待所有数据写入完成后再关闭 writer。"
msgstr ""
"You can write data with multiple threads. Since PyODPS 0.11.6, simply "
"spawning ``writer`` objects created with ``open_writer`` method into "
"different threads and then data can be written in those threads. Note "
"that you shall not close writers until all data are written."

#: ../../source/base-tables.rst:338
msgid ""
"import random\n"
"# Python 2.7 请从三方库 futures 中 import ThreadPoolExecutor\n"
"from concurrent.futures import ThreadPoolExecutor\n"
"\n"
"def write_records(writer):\n"
"    for i in range(5):\n"
"        # 生成数据并写入\n"
"        record = table.new_record([random.randint(1, 100), "
"random.random()])\n"
"        writer.write(record)\n"
"\n"
"N_THREADS = 3\n"
"\n"
"# 此处省略入口对象 o 的创建过程\n"
"table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
"\n"
"with table.open_writer() as writer:\n"
"    pool = ThreadPoolExecutor(N_THREADS)\n"
"    futures = []\n"
"    for i in range(N_THREADS):\n"
"        futures.append(pool.submit(write_records, writer))\n"
"    # 等待线程中的写入完成\n"
"    [f.result() for f in futures]"
msgstr ""
"import random\n"
"# for Python 2.7 please import ThreadPoolExecutor from\n"
"# third-party library `futures`\n"
"from concurrent.futures import ThreadPoolExecutor\n"
"\n"
"def write_records(writer):\n"
"    for i in range(5):\n"
"        # generate data and write to passed writers\n"
"        record = table.new_record([random.randint(1, 100), "
"random.random()])\n"
"        writer.write(record)\n"
"\n"
"N_THREADS = 3\n"
"\n"
"# creation of MaxCompute entry object o is omitted here\n"
"table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
"\n"
"with table.open_writer() as writer:\n"
"    pool = ThreadPoolExecutor(N_THREADS)\n"
"    futures = []\n"
"    for i in range(N_THREADS):\n"
"        futures.append(pool.submit(write_records, writer))\n"
"    # wait for threaded calls to finish\n"
"    [f.result() for f in futures]"

#: ../../source/base-tables.rst:363
msgid ""
"你也可以使用多进程写入数据，以避免 Python GIL 带来的性能损失。从 PyODPS "
"0.11.6 开始，只需要将 open_writer 创建的 Writer 对象通过 multiprocessing "
"标准库传递到需要写入的子进程中即可写入。\\ 需要注意的是，与多线程的情形"
"不同，你应当在每个子进程完成写入后关闭 writer，并在所有写入子进程退出后\\"
" 再关闭主进程 writer（或离开 with 语句块），以保证所有数据被提交。"
msgstr ""
"You can also write data with ``multiprocessing`` module in Python to "
"avoid performance loss from GIL. Since PyODPS 0.11.6, you can simply pass"
" ``writer`` object created with ``open_writer`` method into subprocess "
"functions with ``multiprocessing`` APIs. Note that different from the "
"multi threading case, you need to close writers in every subprocess once "
"writing is finished and close writer in the main process once writing in "
"all subprocesses is done to make sure all written data are committed."

#: ../../source/base-tables.rst:368
msgid ""
"import random\n"
"from multiprocessing import Pool\n"
"\n"
"def write_records(writer):\n"
"    for i in range(5):\n"
"        # 生成数据并写入\n"
"        record = table.new_record([random.randint(1, 100), "
"random.random()])\n"
"        writer.write(record)\n"
"    # 需要手动在每个子进程中关闭连接\n"
"    writer.close()\n"
"\n"
"# 如果在独立的 Python 代码文件中，需要判断是否代码按主模块执行\n"
"# 以防止下面的代码被 multiprocessing 反复执行\n"
"if __name__ == '__main__':\n"
"    N_WORKERS = 3\n"
"\n"
"    # 此处省略入口对象 o 的创建过程\n"
"    table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
"\n"
"    with table.open_writer() as writer:\n"
"        pool = Pool(processes=N_WORKERS)\n"
"        futures = []\n"
"        for i in range(N_WORKERS):\n"
"            futures.append(pool.apply_async(write_records, (writer,)))\n"
"        # 等待子进程中的执行完成\n"
"        [f.get() for f in futures]"
msgstr ""
"import random\n"
"from multiprocessing import Pool\n"
"\n"
"def write_records(writer):\n"
"    for i in range(5):\n"
"        # generate data and write to passed writers\n"
"        record = table.new_record([random.randint(1, 100), "
"random.random()])\n"
"        writer.write(record)\n"
"    # need to close writers in every subprocess once writing is done\n"
"    writer.close()\n"
"\n"
"# need to judge if current code is executed as main module to make sure\n"
"# the code is not executed by multiprocessing repeatedly\n"
"if __name__ == '__main__':\n"
"    N_WORKERS = 3\n"
"\n"
"    # creation of MaxCompute entry object o is omitted here\n"
"    table = o.create_table('my_new_table', 'num bigint, num2 double', "
"if_not_exists=True)\n"
"\n"
"    with table.open_writer() as writer:\n"
"        pool = Pool(processes=N_WORKERS)\n"
"        futures = []\n"
"        for i in range(N_WORKERS):\n"
"            futures.append(pool.apply_async(write_records, (writer,)))\n"
"        # wait for subprocesses to finish\n"
"        [f.get() for f in futures]"

#: ../../source/base-tables.rst:397
msgid ""
"从 0.11.2 开始，PyODPS 支持使用 `Arrow <https://arrow.apache.org/>`_ 格式"
"读写数据，该格式可以以更高效率与 pandas 等格式互相转换。安装 pyarrow 后，"
"在调用 ``open_writer`` 时增加 ``arrow=True`` 参数，即可按 `Arrow "
"RecordBatch <https://arrow.apache.org/docs/python/data.html#record-"
"batches>`_ 格式写入表内容。PyODPS 也支持直接写入 pandas DataFrame，支持"
"自动转换为 Arrow RecordBatch。"
msgstr ""
"Since 0.11.2, PyODPS supports reading and writing table data with `Arrow "
"<https://arrow.apache.org/>`_ format, which can be converted from and to "
"pandas or other formats with high efficiency. After installing pyarrow, "
"you can write data into tables with `Arrow RecordBatch "
"<https://arrow.apache.org/docs/python/data.html#record-batches>`_ format "
"by adding ``arrow=True`` argument when calling ``open_writer`` method. "
"PyODPS also supports writing tables with pandas DataFrames, which will be"
" converted into Arrow RecordBatch directly."

#: ../../source/base-tables.rst:402
msgid ""
">>> import pandas as pd\n"
">>> import pyarrow as pa\n"
">>>\n"
">>> with t.open_writer(partition='pt=test', create_partition=True, "
"arrow=True) as writer:\n"
">>>     records = [[111, 'aaa', True],\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     df = pd.DataFrame(records, columns=[\"int_val\", \"str_val\", "
"\"bool_val\"])\n"
">>>     # 写入 RecordBatch\n"
">>>     batch = pa.RecordBatch.from_pandas(df)\n"
">>>     writer.write(batch)\n"
">>>     # 也可以直接写入 Pandas DataFrame\n"
">>>     writer.write(df)"
msgstr ""
">>> import pandas as pd\n"
">>> import pyarrow as pa\n"
">>>\n"
">>> with t.open_writer(partition='pt=test', create_partition=True) as "
"writer:\n"
">>>     records = [[111, 'aaa', True],\n"
">>>                [222, 'bbb', False],\n"
">>>                [333, 'ccc', True],\n"
">>>                [444, '中文', False]]\n"
">>>     df = pd.DataFrame(records, columns=[\"int_val\", \"str_val\", "
"\"bool_val\"])\n"
">>>     # write a RecordBatch\n"
">>>     batch = pa.RecordBatch.from_pandas(df)\n"
">>>     writer.write(batch)\n"
">>>     # Pandas DataFrame can also be used directly\n"
">>>     writer.write(df)"

#: ../../source/base-tables.rst:419
msgid ""
"为方便写入 pandas DataFrame，从 0.12.0 开始，PyODPS 支持直接通过 ``write_"
"table`` 方法写入 pandas DataFrame。\\ 如果写入数据前对应表不存在，可以"
"增加 ``create_table=True`` 参数以自动创建表。"
msgstr ""
"To facilitate writing pandas DataFrame, since PyODPS 0.12.0, pandas "
"DataFrames can be written with ``write_table`` method. If target table "
"does not exist, you can add ``create_table=True`` to let the method "
"create it for you."

#: ../../source/base-tables.rst:422
msgid ""
">>> import pandas as pd\n"
">>> df = pd.DataFrame([\n"
">>>     [111, 'aaa', True],\n"
">>>     [222, 'bbb', False],\n"
">>>     [333, 'ccc', True],\n"
">>>     [444, '中文', False]\n"
">>> ], columns=['num_col', 'str_col', 'bool_col'])\n"
">>> # 如果表 test_table 不存在，将会自动创建\n"
">>> o.write_table('test_table', df, partition='pt=test', "
"create_table=True, create_partition=True)"
msgstr ""
">>> import pandas as pd\n"
">>> df = pd.DataFrame([\n"
">>>     [111, 'aaa', True],\n"
">>>     [222, 'bbb', False],\n"
">>>     [333, 'ccc', True],\n"
">>>     [444, '中文', False]\n"
">>> ], columns=['num_col', 'str_col', 'bool_col'])\n"
">>> # if table test_table does not exist, it will be created "
"automatically\n"
">>> o.write_table('test_table', df, partition='pt=test', "
"create_table=True, create_partition=True)"

#: ../../source/base-tables.rst:434
msgid ""
"从 PyODPS 0.12.0 开始，``write_table`` 方法也支持动态分区，可通过 ``"
"partition_cols`` 参数传入需要作为分区的列名，\\ 并指定 ``create_partition"
"=True``，相应的分区将会自动创建。"
msgstr ""
"Since PyODPS 0.12.0, ``write_table`` supports dynamic partitioning. You "
"can use ``partition_cols`` argument to specify columns as partitions, and"
" when ``create_partition=True`` is specified, these partitions will be "
"created by the method."

#: ../../source/base-tables.rst:437
msgid ""
">>> import pandas as pd\n"
">>> df = pd.DataFrame([\n"
">>>     [111, 'aaa', True, 'p1'],\n"
">>>     [222, 'bbb', False, 'p1'],\n"
">>>     [333, 'ccc', True, 'p2'],\n"
">>>     [444, '中文', False, 'p2']\n"
">>> ], columns=['num_col', 'str_col', 'bool_col', 'pt'])\n"
">>> # 如果分区 pt=p1 或 pt=p2 不存在，将会自动创建。\n"
">>> o.write_table('test_part_table', df, partition_cols=['pt'], "
"create_partition=True)"
msgstr ""
">>> import pandas as pd\n"
">>> df = pd.DataFrame([\n"
">>>     [111, 'aaa', True, 'p1'],\n"
">>>     [222, 'bbb', False, 'p1'],\n"
">>>     [333, 'ccc', True, 'p2'],\n"
">>>     [444, '中文', False, 'p2']\n"
">>> ], columns=['num_col', 'str_col', 'bool_col', 'pt'])\n"
">>> # if partition pt=p1 or pt=p2 does not exist, they will be created "
"automatically\n"
">>> o.write_table('test_part_table', df, partition_cols=['pt'], "
"create_partition=True)"

#: ../../source/base-tables.rst:451
msgid ""
"``partition_cols`` 参数从 PyODPS 0.12.3 开始支持。在此之前的版本请使用 ``"
"partitions`` 参数。"
msgstr ""
"``partition_cols`` is supported since PyODPS 0.12.3. Please use "
"``partitions`` argument instead when using PyODPS 0.12.2 or earlier."

#: ../../source/base-tables.rst:454 ../../source/base-tables.rst:897
msgid "压缩选项"
msgstr "Compression options"

#: ../../source/base-tables.rst:455 ../../source/base-tables.rst:898
msgid ""
"为加快数据上传 / 下载速度，你可以在上传 / 下载数据时设置压缩选项。具体地"
"，可以创建一个 ``CompressOption`` 实例，在其中指定需要的压缩算法及压缩"
"等级。目前可用的压缩算法包括 zlib 和 ZSTD，其中 ZSTD 需要额外安装 ``"
"zstandard`` 包。"
msgstr ""
"You can specify compression options to accelerate data upload or "
"download. To achieve this, you may create a ``CompressOption`` instance "
"and specify compression algorithm and level in it. Currently zlib and "
"ZSTD is supported, and you need to install ``zstandard`` package to "
"enable ZSTD support."

#: ../../source/base-tables.rst:459 ../../source/base-tables.rst:902
msgid ""
"from odps.tunnel import CompressOption\n"
"\n"
"compress_option = CompressOption(\n"
"    compress_algo=\"zlib\",  # 算法名称\n"
"    level=0,               # 压缩等级，可选\n"
"    strategy=0,            # 压缩策略，可选，目前仅适用于 zlib\n"
")"
msgstr ""
"from odps.tunnel import CompressOption\n"
"\n"
"compress_option = CompressOption(\n"
"    compress_algo=\"zlib\",  # algorithm name\n"
"    level=0,               # (optional) compression level\n"
"    strategy=0,            # (optional) compression strategy, only for "
"zlib\n"
")"

#: ../../source/base-tables.rst:469
msgid "此后可在 ``open_reader`` / ``open_writer`` 中设置压缩选项，例如："
msgstr ""
"Then you may specify compression option when calling ``open_reader`` or "
"``open_writer``. For instance,"

#: ../../source/base-tables.rst:471
msgid ""
"with table.open_writer(compress_option=compress_option) as writer:\n"
"    # 写入数据，此处从略"
msgstr ""
"with table.open_writer(compress_option=compress_option) as writer:\n"
"    # replace this comment with actual data writing code"

#: ../../source/base-tables.rst:476
msgid ""
"如果仅需指定算法名，也可以直接在 ``open_reader`` / ``open_writer`` 中指定"
" ``compress_algo`` 参数，例如"
msgstr ""
"If you only need to specify name of the compression algorithm, you can "
"specify it with ``compress_algo`` argument directly."

#: ../../source/base-tables.rst:478
msgid ""
"with table.open_writer(compress_algo=\"zlib\") as writer:\n"
"    # 写入数据，此处从略"
msgstr ""
"with table.open_writer(compress_algo=\"zlib\") as writer:\n"
"    # replace this comment with actual data writing code"

#: ../../source/base-tables.rst:484
msgid "删除表"
msgstr "Delete tables"

#: ../../source/base-tables.rst:486
msgid ""
">>> o.delete_table('my_table_name', if_exists=True)  #  只有表存在时删除\n"
">>> t.drop()  # Table对象存在的时候可以直接执行drop函数"
msgstr ""
">>> o.delete_table('my_table_name', if_exists=True)  #  delete only when "
"the table exists\n"
">>> t.drop()  # call drop method of the Table object to delete directly"

#: ../../source/base-tables.rst:493
msgid "创建DataFrame"
msgstr "Create a DataFrame"

#: ../../source/base-tables.rst:495
msgid ""
"PyODPS提供了 :ref:`DataFrame框架 <df>` ，支持更方便地方式来查询和操作ODPS"
"数据。 使用 ``to_df`` 方法，即可转化为 DataFrame 对象。"
msgstr ""
"PyODPS provides a :ref:`DataFrame framework <df>` to easily search and "
"operate MaxCompute data. You can use ``to_df`` to convert a table to a "
"DataFrame object."

#: ../../source/base-tables.rst:498
msgid ""
">>> table = o.get_table('my_table_name')\n"
">>> df = table.to_df()"
msgstr ""

#: ../../source/base-tables.rst:504
msgid "表分区"
msgstr "Table partitions"

#: ../../source/base-tables.rst:509
msgid "判断表是否为分区表："
msgstr "Check if a table is partitioned:"

#: ../../source/base-tables.rst:511
#, python-format
msgid ""
">>> if table.table_schema.partitions:\n"
">>>     print('Table %s is partitioned.' % table.name)"
msgstr ""

#: ../../source/base-tables.rst:516
msgid ""
"使用 :meth:`~odps.models.Table.exist_partition` 方法判断分区是否存在（该"
"方法需要填写所有分区字段值）："
msgstr ""
"Check whether the specified partition exists with  "
":meth:`~odps.models.Table.exist_partition` method, all field values "
"should be provided:"

#: ../../source/base-tables.rst:518
msgid ">>> table.exist_partition('pt=test,sub=2015')"
msgstr ""

#: ../../source/base-tables.rst:522
msgid "判断给定前缀的分区是否存在："
msgstr "Check whether partitions satisfying provided prefix exist:"

#: ../../source/base-tables.rst:524
msgid ""
">>> # 表 table 的分区字段依次为 pt, sub\n"
">>> table.exist_partitions('pt=test')"
msgstr ""
">>> # the order of partitions fields of table is pt, sub\n"
">>> table.exist_partitions('pt=test')"

#: ../../source/base-tables.rst:529
msgid "使用 :meth:`~odps.models.Table.get_partition` 方法获取一个分区的相关信息："
msgstr ""
"Obtain information about one specified partition with "
":meth:`~odps.models.Table.get_partition` method:"

#: ../../source/base-tables.rst:531
msgid ""
">>> partition = table.get_partition('pt=test')\n"
">>> print(partition.creation_time)\n"
"2015-11-18 22:22:27\n"
">>> partition.size\n"
"0"
msgstr ""

#: ../../source/base-tables.rst:541
msgid ""
"这里的\"分区\"指的不是分区字段而是所有分区字段均确定的分区定义对应的子表"
"。如果某个分区字段对应多个值， 则相应地有多个子表，即多个分区。而 :meth:`"
"~odps.models.Table.get_partition` 只能获取一个分区的信息。因而，"
msgstr ""
"The word `partition` here refers to a partition specification that "
"specifies values of all partition columns which uniquely specifies a sub-"
"table, not partition columns. If one partition column is specified with "
"multiple values, it may refer to multiple sub-tables, or multiple "
"partitions. Meanwhile the method :meth:`~odps.models.Table.get_partition`"
" can only obtain information of only one sub-table. Thus,"

#: ../../source/base-tables.rst:544
msgid ""
"如果某些分区未指定，那么这个分区定义可能对应多个子表，``get_partition`` "
"时则不被 PyODPS 支持。\\ 此时，需要使用 :meth:`~odps.models.Table.iterate"
"_partitions` 分别处理每个分区。"
msgstr ""
"When some values of partition columns are absent, the specification could"
" represent multiple tables, and then calling ``get_partitions`` with this"
" specification is not supported in PyODPS. You need to use "
":meth:`~odps.models.Table.iterate_partitions` to handle every partition "
"respectively."

#: ../../source/base-tables.rst:546
msgid ""
"如果某个分区字段被定义多次，或者使用类似 ``pt>20210302`` 这样的非确定逻辑"
"表达式，则无法使用 ``get_partition`` 获取分区。在此情况下，可以尝试使用 `"
"`iterate_partitions`` 枚举每个分区。"
msgstr ""
"When some partition column is specified multiple times, or non-"
"deterministic logic expressions like ``pt>20210302`` is used, "
"``get_partition`` cannot be used to obtain partition information. In this"
" case, ``iterate_partitions`` might be used to iterate over all "
"partitions."

#: ../../source/base-tables.rst:550
msgid "创建分区"
msgstr "Create partitions"

#: ../../source/base-tables.rst:552
msgid ""
"下面的操作使用 :meth:`~odps.models.Table.create_partition` 方法创建一个"
"分区，如果分区存在将报错："
msgstr ""
"Code below will create a partition with "
":meth:`~odps.models.Table.create_partition` An error will be raised if "
"the partition already exists."

#: ../../source/base-tables.rst:554
msgid ">>> t.create_partition('pt=test')"
msgstr ""

#: ../../source/base-tables.rst:558
msgid "下面的操作将创建一个分区，如果分区存在则跳过："
msgstr ""
"Code below will create a partition or do nothing if the partition already"
" exists."

#: ../../source/base-tables.rst:560
msgid ">>> t.create_partition('pt=test', if_not_exists=True)"
msgstr ""

#: ../../source/base-tables.rst:567
msgid "遍历表分区"
msgstr "Iterate through partitions"

#: ../../source/base-tables.rst:568
msgid "下面的操作将遍历表全部分区："
msgstr "Code below iterates through all the partitions in a table."

#: ../../source/base-tables.rst:570
msgid ""
">>> for partition in table.partitions:\n"
">>>     print(partition.name)"
msgstr ""

#: ../../source/base-tables.rst:575
msgid ""
"如果要遍历部分分区值确定的分区，可以使用 :meth:`~odps.models.Table."
"iterate_partitions` 方法。"
msgstr ""
"If you need to iterate through partitions with certain values of "
"partition fields fixed, you can use "
":meth:`~odps.models.Table.iterate_partitions` method."

#: ../../source/base-tables.rst:577
msgid ""
">>> for partition in table.iterate_partitions(spec='pt=test'):\n"
">>>     print(partition.name)"
msgstr ""

#: ../../source/base-tables.rst:582
msgid ""
"自 PyODPS 0.11.3 开始，支持为 ``iterate_partitions`` 指定简单的逻辑表达式"
"及通过逗号连接，\\ 每个子表达式均须满足的复合逻辑表达式。或运算符暂不支持"
"。"
msgstr ""
"Since PyODPS 0.11.3, PyODPS supports using simple logic expressions or "
"logic expressions connected with commas which means combined conditions "
"when iterating through partitions. OR operator is not supported "
"currently."

#: ../../source/base-tables.rst:585
msgid ""
">>> for partition in table.iterate_partitions(spec='dt>20230119'):\n"
">>>     print(partition.name)"
msgstr ""

#: ../../source/base-tables.rst:592
msgid ""
"在 0.11.3 之前的版本中，``iterate_partitions`` 仅支持枚举前若干个分区等于"
"相应值的情形。例如， 当表的分区字段按顺序分别为 pt1、pt2 和 pt3，那么 ``"
"iterate_partitions`` 中的  ``spec`` 参数只能指定 ``pt1=xxx`` 或者 ``pt1="
"xxx,pt2=yyy`` 这样的形式。自 0.11.3 开始， ``iterate_partitions`` 支持更"
"多枚举方式，但仍建议尽可能限定上一级分区以提高枚举的效率。"
msgstr ""
"Before 0.11.3, ``iterate_partitions`` only supports specifying partition "
"values for the first partition fields. For instance, when a table has 3 "
"partition fields, pt1, pt2 and pt3, ``spec`` argument of "
"``iterate_partitions`` can only accept values like ``pt1=xxx`` or "
"``pt1=xxx,pt2=yyy``. Since 0.11.3, ``iterate_partitions`` supports more "
"flexible forms of ``spec`` arguments. However, it is still recommended to"
" fix values of first partition fields to improve speed of iteration."

#: ../../source/base-tables.rst:598
msgid "删除分区"
msgstr "Delete partitions"

#: ../../source/base-tables.rst:600
msgid ""
"下面的操作使用 :meth:`~odps.models.Table.delete_partition` 方法删除一个"
"分区："
msgstr ""
"Code below will delete a partition with "
":meth:`~odps.models.Table.delete_partition` method."

#: ../../source/base-tables.rst:602
msgid ""
">>> t.delete_partition('pt=test', if_exists=True)  # 存在的时候才删除\n"
">>> partition.drop()  # Partition对象存在的时候直接drop"
msgstr ""
">>> t.delete_partition('pt=test', if_exists=True)  # delete only when the"
" partition exists\n"
">>> partition.drop()  # delete directly via the drop method of the "
"partition object"

#: ../../source/base-tables.rst:608
msgid "获取值最大分区"
msgstr "Obtain the partition with maximal value:"

#: ../../source/base-tables.rst:609
msgid ""
"很多时候你可能希望获取值最大的分区。例如，当以日期为分区值时，你可能希望"
"获得日期最近的有数据的分区。PyODPS 自 0.11.3 开始支持此功能。"
msgstr ""
"Sometimes you want to get the partition with maximal value, for instance,"
" when dates are used as partition values, you may want to get the "
"partition with data and latest date. PyODPS starts supporting this "
"function since 0.11.3."

#: ../../source/base-tables.rst:612
msgid "创建分区表并写入一些数据："
msgstr "Create a partitioned table and write some data."

#: ../../source/base-tables.rst:614
#, python-format
msgid ""
"t = o.create_table(\"test_multi_pt_table\", (\"col string\", \"pt1 "
"string, pt2 string\"))\n"
"for pt1, pt2 in ((\"a\", \"a\"), (\"a\", \"b\"), (\"b\", \"c\"), (\"b\", "
"\"d\")):\n"
"    o.write_table(\"test_multi_pt_table\", [[\"value\"]], "
"partition=\"pt1=%s,pt2=%s\" % (pt1, pt2))"
msgstr ""

#: ../../source/base-tables.rst:620
msgid "如果想要获得值最大的分区，可以使用下面的代码："
msgstr ""
"If you want to get the partition with maximal value, you can use code "
"below:"

#: ../../source/base-tables.rst:622
msgid ""
">>> part = t.get_max_partition()\n"
">>> part\n"
"<Partition cupid_test_release.`test_multi_pt_table`(pt1='b',pt2='d')>\n"
">>> part.partition_spec[\"pt1\"]  # 获取某个分区字段的值\n"
"b"
msgstr ""
">>> part = t.get_max_partition()\n"
">>> part\n"
"<Partition cupid_test_release.`test_multi_pt_table`(pt1='b',pt2='d')>\n"
">>> part.partition_spec[\"pt1\"]  # get value of certain partition field\n"
"b"

#: ../../source/base-tables.rst:630
msgid "如果只希望获得最新的分区而忽略分区内是否有数据，可以用"
msgstr ""
"If you want to get latest partition while ignore whether the partition "
"has data, you may use"

#: ../../source/base-tables.rst:632
msgid ""
">>> t.get_max_partition(skip_empty=False)\n"
"<Partition cupid_test_release.`test_multi_pt_table`(pt1='b',pt2='d')>"
msgstr ""

#: ../../source/base-tables.rst:637
msgid "对于多级分区表，可以通过限定上级分区值来获得值最大的子分区，例如"
msgstr ""
"For tables with multiple partitions, you may specify the parent partition"
" specification to get child partition with maximal value, for instance,"

#: ../../source/base-tables.rst:639
msgid ""
">>> t.get_max_partition(\"pt1=a\")\n"
"<Partition cupid_test_release.`test_multi_pt_table`(pt1='a',pt2='b')>"
msgstr ""

#: ../../source/base-tables.rst:647
msgid "数据上传下载通道"
msgstr "Data upload and download tunnels"

#: ../../source/base-tables.rst:650
msgid ""
"不推荐直接使用 Tunnel 接口，该接口较为低级，简单的表写入推荐直接使用 "
"Tunnel 接口上实现的表 :ref:`写 <table_write>` 和 :ref:`读 <table_read>` "
"接口，可靠性和易用性更高。 只有在分布式写表等复杂场景下有直接使用 Tunnel "
"接口的需要。"
msgstr ""
"If you just need to upload a small amount of data, we do not recommend "
"using table tunnel directly for simple table reading and writing, as "
"there are more convenient :ref:`read <table_read>` and :ref:`write "
"<table_write>` methods which wraps table tunnel invocations. You might "
"use tunnel interfaces directly when writing tables distributedly or under"
" complicated scenarios."

#: ../../source/base-tables.rst:654
msgid ""
"ODPS Tunnel 是 MaxCompute 的数据通道，用户可以通过 Tunnel 向 MaxCompute "
"中上传或者下载数据。\\ 关于 ODPS Tunnel 的详细解释可以参考\\ `https://"
"help.aliyun.com/zh/maxcompute/user-guide/overview-of-dts <这篇文档>`_。"
msgstr ""
"MaxCompute Tunnel is the data channel of MaxCompute. You can use this to "
"upload data to or download data from MaxCompute. Details of MaxCompute "
"tunnel can be found in `this document "
"<https://help.aliyun.com/zh/maxcompute/user-guide/overview-of-dts>`_."

#: ../../source/base-tables.rst:658
msgid "上传"
msgstr "Upload"

#: ../../source/base-tables.rst:660
msgid "分块上传接口"
msgstr "Block upload interface"

#: ../../source/base-tables.rst:661
msgid ""
"直接使用 Tunnel 分块接口上传时，需要首先通过 :meth:`~odps.tunnel."
"TableTunnel.create_upload_session` 方法使用表名和分区创建 Upload Session"
"，此后从 Upload Session 创建 Writer。每个 Upload Session 可多次调用 :meth"
":`~odps.tunnel.TableUploadSession.open_record_writer` 方法创建多个 Writer"
"，每个 Writer 拥有一个 ``block_id`` 对应一个数据块。写入的数据类型为 :ref"
":`Record <record-type>` 类型。完成所有写入后，需要调用 Upload Session 上"
"的 :meth:`~odps.tunnel.TableUploadSession.commit` 方法并指定需要提交的"
"数据块列表。\\ 如果有某个 ``block_id`` 有数据写入但未包括在 ``commit`` 的"
"参数中，则该数据块不会出现在最终的表中。"
msgstr ""
"When using block upload interface of MaxCompute tunnel, you need to "
"create an upload session with "
":meth:`~odps.tunnel.TableTunnel.create_upload_session` method and table "
"partition specifications, and then create writers on the session. You may"
" call :meth:`~odps.tunnel.TableUploadSession.open_record_writer` multiple"
" times to create multiple writers. Every writer need to be specified with"
" a unique ``block_id``. Data to write should be of :ref:`record type "
"<record-type>`. After all data are written, you need to call ``commit`` "
"method of the upload session to commit selected blocks as a list. If some"
" ``block_id`` is omitted from the list submitted with "
":meth:`~odps.tunnel.TableUploadSession.commit` method, the corresponding "
"data block will not appear in the final table."

#: ../../source/base-tables.rst:668
msgid ""
"对于需要写入数据的情形，\\ ``commit`` 调用有且只能有一次，完成 ``commit``"
" 后 Upload Session 即完成写入，此后无法再在该 Upload Session 上提交。"
msgstr ""
"For writing data, ``commit`` method can only be called once. After "
"``commit`` is called, the upload session is closed and no more data can "
"be written on the same session."

#: ../../source/base-tables.rst:671
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Upload Session\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# 创建 record writer 并指定需要写入的 block_id 为 0\n"
"with upload_session.open_record_writer(0) as writer:\n"
"    record = table.new_record()\n"
"    record[0] = 'test1'\n"
"    record[1] = 'id1'\n"
"    writer.write(record)\n"
"\n"
"    record = table.new_record(['test2', 'id2'])\n"
"    writer.write(record)\n"
"\n"
"# 提交刚才写入的 block 0。多个 block id 需要同时提交\n"
"# 需要在 with 代码块外 commit，否则数据未写入即 commit，会导致报错并丢失"
"已写入的数据\n"
"# 对每个 upload_session，commit 只能调用一次\n"
"upload_session.commit([0])"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create an upload session for my_table and partition pt=test\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# create a record writer with block_id as 0\n"
"with upload_session.open_record_writer(0) as writer:\n"
"    record = table.new_record()\n"
"    record[0] = 'test1'\n"
"    record[1] = 'id1'\n"
"    writer.write(record)\n"
"\n"
"    record = table.new_record(['test2', 'id2'])\n"
"    writer.write(record)\n"
"\n"
"# submit block 0 which is just written. multiple blocks need to be "
"specified\n"
"#  on commit.\n"
"# need to commit outside with-block, or data will not be written before\n"
"#  commit, and an error will be raised and all data are lost.\n"
"# for every upload_session, commit can be called only once.\n"
"upload_session.commit([0])"

#: ../../source/base-tables.rst:696
msgid ""
"如果你需要在多个进程乃至节点中使用相同的 Upload Session，可以先创建 "
"Upload Session，并获取其 ``id`` 属性。此后在其他进程中调用 ``create_"
"upload_session`` 方法时，将该值作为 ``upload_id`` 参数。\\ 完成每个进程的"
"上传后，需要收集各进程提交数据所用的 ``block_id``，并在某个进程中完成 ``"
"commit``。"
msgstr ""
"If you need to reuse your upload session in multiple processes or nodes, "
"you may create your upload session first, obtain its ``id`` property and "
"then call ``create_upload_session`` in other processes with the value as "
"``upload_id`` argument. After all data are uploaded in processes, you "
"need to collect ``block_id`` used in these processes and call ``commit`` "
"in some process."

#: ../../source/base-tables.rst:700
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"##############\n"
"# 主进程\n"
"##############\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Upload Session\n"
"upload_session_main = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"# 获取 Session ID\n"
"session_id = upload_session_main.id\n"
"\n"
"# 分发 Session ID，此处省略分发过程\n"
"\n"
"##############\n"
"# 子进程\n"
"##############\n"
"\n"
"# 使用分发的 upload_id 创建 upload session\n"
"upload_session_sub = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test', upload_id=session_id)\n"
"# 创建 reader 并写入数据，注意区分不同进程的 block_id\n"
"with upload_session_sub.open_record_writer(local_block_id) as writer:\n"
"    # ... 生成数据 ...\n"
"    writer.write(record)\n"
"\n"
"# 回传本进程中使用的所有 block_id，此处省略回传过程\n"
"\n"
"##############\n"
"# 主进程\n"
"##############\n"
"\n"
"# 收集所有子进程上的 block_id，此处省略收集过程\n"
"\n"
"# 提交收集到的 block_id\n"
"upload_session_main.commit(collected_block_ids)"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"##############\n"
"# main process\n"
"##############\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create upload session for table and partition pt=test\n"
"upload_session_main = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"# obtain session id\n"
"session_id = upload_session_main.id\n"
"\n"
"# distribute session id to processes, need to be implemented by yourself\n"
"\n"
"##############\n"
"# sub-process\n"
"##############\n"
"\n"
"# create (or reuse) upload session with distributed session id\n"
"upload_session_sub = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test', upload_id=session_id)\n"
"# create reader and write data. you need to make block_id different "
"between processes\n"
"with upload_session_sub.open_record_writer(local_block_id) as writer:\n"
"    # ... generate data ...\n"
"    writer.write(record)\n"
"\n"
"# send back block_id used in current process, need to be implemented by "
"yourself\n"
"\n"
"##############\n"
"# main process\n"
"##############\n"
"\n"
"# collect all block_id in subprocesses, need to be implemented by "
"yourself\n"
"\n"
"# submit all collected block_id\n"
"upload_session_main.commit(collected_block_ids)"

#: ../../source/base-tables.rst:740
msgid ""
"需要注意的是，指定 block id 后，所创建的 Writer 为长连接，如果长时间不"
"写入会导致连接关闭，并导致写入失败，\\ 该时间通常为 5 分钟。如果你写入"
"数据的间隔较大，建议生成一批数据后再通过 ``open_record_writer`` 接口创建 "
"Writer 并按需写入数据。如果你只希望在单个 Writer 上通过 Tunnel 写入数据，"
"可以考虑在调用 ``open_record_writer`` 时不指定 block id，此时创建的 "
"Writer 在写入数据时将首先将数据缓存在本地，当 Writer 关闭或者缓存数据大于"
"\\ 一定大小（默认为 20MB，可通过 ``options.tunnel.block_buffer_size`` "
"指定）时才会写入数据。写入数据后，\\ 需要先通过 Writer 上的 :meth:`~odps."
"tunnel.BufferedRecordWriter.get_blocks_written` 方法获得已经写入的 block "
"列表，再进行提交。"
msgstr ""
"Note that writers created with ``open_record_writer`` establish long "
"connections which will be closed if no data are written in a long period "
"and writing fails. The gap is often 5 minutes. If you write data with "
"long gaps, it is recommended to cache data locally and create writers "
"with ``open_record_writer`` and then write data immediately on demand. "
"For simplicity, if you only want to upload data with one single writer "
"with tunnel interface, you can call ``open_record_writer`` without block "
"id and a buffered writer will be created. The buffered writer will cache "
"data locally. Submission will be performed on writer close or buffer size"
" exceeded certain limits, which is 20MB by default and can be configured "
"with ``options.tunnel.block_buffer_size``. After writing all data you "
"need to obtain all written blocks with "
":meth:`~odps.tunnel.BufferedRecordWriter.get_blocks_written` method."

#: ../../source/base-tables.rst:748
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Upload Session\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# 不指定 block id 以创建带缓存的 record writer\n"
"with upload_session.open_record_writer() as writer:\n"
"    record = table.new_record()\n"
"    record[0] = 'test1'\n"
"    record[1] = 'id1'\n"
"    writer.write(record)\n"
"\n"
"    record = table.new_record(['test2', 'id2'])\n"
"    writer.write(record)\n"
"\n"
"# 需要在 with 代码块外 commit，否则数据未写入即 commit，会导致报错\n"
"# 从 writer 获得已经写入的 block id 并提交\n"
"upload_session.commit(writer.get_blocks_written())"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create an upload session for my_table and partition pt=test\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# create writer without block id to create a record writer with buffers\n"
"with upload_session.open_record_writer() as writer:\n"
"    record = table.new_record()\n"
"    record[0] = 'test1'\n"
"    record[1] = 'id1'\n"
"    writer.write(record)\n"
"\n"
"    record = table.new_record(['test2', 'id2'])\n"
"    writer.write(record)\n"
"\n"
"# need to commit outside with-block, or data will not be written before\n"
"#  commit, and an error will be raised.\n"
"# obtain block ids from the writer and then commit\n"
"upload_session.commit(writer.get_blocks_written())"

#: ../../source/base-tables.rst:774
msgid ""
"使用带缓存的 Writer 时，需要注意不能在同一 Upload Session 上开启多个带"
"缓存 Writer 进行写入，\\ 否则可能导致冲突而使数据丢失。"
msgstr ""
"When using buffered writers, you need to avoid opening multiple writers "
"on a single upload session, or there might be collisions and data might "
"be lost."

#: ../../source/base-tables.rst:777
msgid ""
"如果你需要使用 Arrow 格式而不是 Record 格式进行上传，可以将 :meth:`~odps."
"tunnel.TableUploadSession.open_record_writer` 替换为 :meth:`~odps.tunnel."
"TableUploadSession.open_arrow_writer`，并写入 Arrow RecordBatch / Arrow "
"Table 或者 pandas DataFrame。"
msgstr ""
"If you need to upload with arrow format instead of record format, you may"
" replace :meth:`~odps.tunnel.TableUploadSession.open_record_writer` with "
":meth:`~odps.tunnel.TableUploadSession.open_arrow_writer` and write arrow"
" RecordBatches, Tables or pandas DataFrames."

#: ../../source/base-tables.rst:781
msgid ""
"import pandas as pd\n"
"import pyarrow as pa\n"
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# 使用 open_arrow_writer 而不是 open_record_writer\n"
"with upload_session.open_arrow_writer(0) as writer:\n"
"    df = pd.DataFrame({\"name\": [\"test1\", \"test2\"], \"id\": "
"[\"id1\", \"id2\"]})\n"
"    batch = pa.RecordBatch.from_pandas(df)\n"
"    writer.write(batch)\n"
"\n"
"# 需要在 with 代码块外 commit，否则数据未写入即 commit，会导致报错\n"
"upload_session.commit([0])"
msgstr ""
"import pandas as pd\n"
"import pyarrow as pa\n"
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"upload_session = tunnel.create_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"# use open_arrow_writer instead of open_record_writer\n"
"with upload_session.open_arrow_writer(0) as writer:\n"
"    df = pd.DataFrame({\"name\": [\"test1\", \"test2\"], \"id\": "
"[\"id1\", \"id2\"]})\n"
"    batch = pa.RecordBatch.from_pandas(df)\n"
"    writer.write(batch)\n"
"\n"
"# need to commit outside with-block, or data will not be written before\n"
"# commit, and an error will be raised.\n"
"upload_session.commit([0])"

#: ../../source/base-tables.rst:801
msgid "本章节中所述所有 Writer 均非线程安全。你需要为每个线程单独创建 Writer。"
msgstr ""
"All writers described in this chapter are not thread safe. You need to "
"create separate writers for every thread."

#: ../../source/base-tables.rst:804
msgid "流式上传接口"
msgstr "Stream upload interface"

#: ../../source/base-tables.rst:805
msgid ""
"MaxCompute 提供了\\ `流式上传接口 <https://help.aliyun.com/zh/maxcompute/"
"user-guide/overview-of-streaming-data-channels>`_\\ 用于简化分布式服务"
"开发成本。可以使用 :meth:`~odps.tunnel.TableTunnel.create_stream_upload_"
"session` 方法创建专门的 Upload Session。此时，不需要为该 Session 的 ``"
"open_record_writer`` 提供 block id。"
msgstr ""
"MaxCompute provides `stream upload interface "
"<https://help.aliyun.com/zh/maxcompute/user-guide/overview-of-streaming-"
"data-channels>`_ to reduce development cost of distributed services. You "
"may use :meth:`~odps.tunnel.TableTunnel.create_stream_upload_session` to "
"create special upload sessions. Block ids are not needed for "
"``open_record_writer`` for this session type."

#: ../../source/base-tables.rst:809
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"upload_session = tunnel.create_stream_upload_session(table.name, "
"partition_spec='pt=test')\n"
"\n"
"with upload_session.open_record_writer() as writer:\n"
"    record = table.new_record()\n"
"    record[0] = 'test1'\n"
"    record[1] = 'id1'\n"
"    writer.write(record)\n"
"\n"
"    record = table.new_record(['test2', 'id2'])\n"
"    writer.write(record)"
msgstr ""

#: ../../source/base-tables.rst:828
msgid "下载"
msgstr "Download"

#: ../../source/base-tables.rst:830
msgid ""
"直接使用 Tunnel 接口下载数据时，需要首先使用表名和分区创建 Download "
"Session，此后从 Download Session 创建 Reader。每个 Download Session 可"
"多次调用 :meth:`~odps.tunnel.TableDownloadSession.open_record_reader` "
"方法创建多个 Reader，每个 Reader 需要指定起始行号以及需要的行数。起始行号"
"从 0 开始，行数可指定为 Session 的 ``count`` 属性，为表或分区的总行数。"
"读取的数据类型为 :ref:`Record <record-type>` 类型。"
msgstr ""
"When using download interface of MaxCompute tunnel, you need to create a "
"download session and then create readers on the session. You may call "
":meth:`~odps.tunnel.TableDownloadSession.open_record_reader` multiple "
"times to create multiple readers. Every reader need to be specified with "
"a initial row number and a row count number. The initial row number "
"begins at 0, and the row count can be ``count`` property of the session, "
"which provides total row number of the table or partition. Data read from"
" the reader are of :ref:`record type <record-type>`."

#: ../../source/base-tables.rst:835
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Download Session\n"
"download_session = tunnel.create_download_session('my_table', "
"partition_spec='pt=test')\n"
"\n"
"# 创建 record reader 并指定需要读取的行范围\n"
"with download_session.open_record_reader(0, download_session.count) as "
"reader:\n"
"    for record in reader:\n"
"        # 处理每条记录"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create a download session for my_table and partition pt=test\n"
"download_session = tunnel.create_download_session('my_table', "
"partition_spec='pt=test')\n"
"\n"
"# create a record reader and specify row range to read\n"
"with download_session.open_record_reader(0, download_session.count) as "
"reader:\n"
"    for record in reader:\n"
"        # process every record"

#: ../../source/base-tables.rst:848
msgid ""
"如果你需要在多个进程乃至节点中使用相同的 Download Session，可以先创建 "
"Download Session，并获取其 ``id`` 属性。此后在其他进程中调用 :meth:`~odps"
".tunnel.TableTunnel.create_download_session` 方法时，将该值作为 ``"
"download_id`` 参数。"
msgstr ""
"If you need to reuse your download session in multiple processes or "
"nodes, you may create your download session first, obtain its ``id`` "
"property and then call "
":meth:`~odps.tunnel.TableTunnel.create_download_session` in other "
"processes with the value as ``download_id`` argument."

#: ../../source/base-tables.rst:852
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"##############\n"
"# 主进程\n"
"##############\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Download Session\n"
"download_session_main = tunnel.create_download_session(table.name, "
"partition_spec='pt=test')\n"
"# 获取 Session ID\n"
"session_id = download_session_main.id\n"
"\n"
"# 分发 Session ID，此处省略分发过程\n"
"\n"
"##############\n"
"# 子进程\n"
"##############\n"
"\n"
"# 使用分发的 upload_id 创建 download session\n"
"download_session_sub = tunnel.create_download_session(table.name, "
"partition_spec='pt=test', download_id=session_id)\n"
"# 创建 reader 并读取数据，注意不同的进程可能需要指定不同的 start / count\n"
"with download_session_sub.open_record_reader(start, count) as reader:\n"
"    for record in reader:\n"
"        # 处理记录"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"##############\n"
"# main process\n"
"##############\n"
"\n"
"table = o.get_table('my_table')\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create download session for table and partition pt=test\n"
"download_session_main = tunnel.create_download_session(table.name, "
"partition_spec='pt=test')\n"
"# obtain session id\n"
"session_id = download_session_main.id\n"
"\n"
"# distribute session id to processes, need to be implemented by yourself\n"
"\n"
"##############\n"
"# sub-process\n"
"##############\n"
"\n"
"# create (or reuse) download session with distributed session id\n"
"download_session_sub = tunnel.create_download_session(table.name, "
"partition_spec='pt=test', download_id=session_id)\n"
"# create reader and read data. note that you may specify different start "
"/ count for different process\n"
"with download_session_sub.open_record_reader(start, count) as reader:\n"
"    for record in reader:\n"
"        # handle data records"

#: ../../source/base-tables.rst:881
msgid ""
"你也可以通过使用 :meth:`~odps.tunnel.TableDownloadSession.open_arrow_"
"reader` 而不是 :meth:`~odps.tunnel.TableDownloadSession.open_record_"
"reader` 使读取的数据为 Arrow 格式而不是 Record 格式。"
msgstr ""
"You can download data with arrow format instead of record format by "
"calling :meth:`~odps.tunnel.TableDownloadSession.open_arrow_reader` "
"instead of :meth:`~odps.tunnel.TableDownloadSession.open_record_reader`."

#: ../../source/base-tables.rst:885
msgid ""
"from odps.tunnel import TableTunnel\n"
"\n"
"tunnel = TableTunnel(o)\n"
"download_session = tunnel.create_download_session('my_table', "
"partition_spec='pt=test')\n"
"\n"
"with download_session.open_arrow_reader(0, download_session.count) as "
"reader:\n"
"    for batch in reader:\n"
"        # 处理每个 Arrow RecordBatch"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"tunnel = TableTunnel(o)\n"
"download_session = tunnel.create_download_session('my_table', "
"partition_spec='pt=test')\n"
"\n"
"with download_session.open_arrow_reader(0, download_session.count) as "
"reader:\n"
"    for batch in reader:\n"
"        # process every Arrow RecordBatch"

#: ../../source/base-tables.rst:912
msgid ""
"此后，在创建 Upload / Download Session 时，可以指定 ``compress_option`` "
"参数，并在 ``open_xxx_reader`` / ``open_xxx_writer`` 方法中设置 ``"
"compress=True`` 即可启用压缩："
msgstr ""
"After that, when creating upload or download sessions, you may specify "
"``compress_option`` argument and use ``compress=True`` in "
"``open_xxx_reader`` or ``open_xxx_writer`` methods to enable compression."

#: ../../source/base-tables.rst:915
msgid ""
"tunnel = TableTunnel(o)\n"
"# 为 table 和 pt=test 分区创建 Download Session\n"
"download_session = tunnel.create_download_session(\n"
"    'my_table', partition_spec='pt=test', compress_option=compress_option"
"\n"
")\n"
"\n"
"# 创建 record reader 并指定需要读取的行范围\n"
"with download_session.open_record_reader(0, download_session.count, "
"compress=True) as reader:\n"
"    for record in reader:\n"
"        # 处理每条记录"
msgstr ""
"from odps.tunnel import TableTunnel\n"
"\n"
"tunnel = TableTunnel(o)\n"
"# create a download session for my_table and partition pt=test\n"
"download_session = tunnel.create_download_session('my_table', "
"partition_spec='pt=test')\n"
"\n"
"# create a record reader and specify row range to read\n"
"with download_session.open_record_reader(0, download_session.count, "
"compress=True) as reader:\n"
"    for record in reader:\n"
"        # process every record"

#: ../../source/base-tables.rst:928
msgid ""
"自 PyODPS 0.12.3 起，你可以通过全局配置指定当前 Python 进程中使用的压缩"
"选项，示例如下："
msgstr ""
"Since PyODPS 0.12.3, you may specify compress options used in current "
"Python process by setting global options as follows."

#: ../../source/base-tables.rst:930
msgid ""
"from odps import options\n"
"\n"
"# 启用压缩（默认为 zlib / deflate 编码）\n"
"options.tunnel.compress.enabled = True\n"
"# 设置压缩算法\n"
"options.tunnel.compress.algo = \"zstd\""
msgstr ""
"from odps import options\n"
"\n"
"# enable compression (default to zlib / deflate)\n"
"options.tunnel.compress.enabled = True\n"
"# set compression algorithm\n"
"options.tunnel.compress.algo = \"zstd\""

#: ../../source/base-tables.rst:939
msgid ""
"此后在所有后续数据读写操作中，都会启用压缩。更多压缩选项可以参考\\ :ref:`"
"配置选项 <options_tunnel>`。"
msgstr ""
"After that, compression will be enabled for all subsequent data read or "
"write operations. For more details, please refer to :ref:`Options "
"<options_tunnel>`."

#: ../../source/base-tables.rst:942
msgid "提升上传和下载性能"
msgstr "Improve upload and download performance"

#: ../../source/base-tables.rst:944
msgid ""
"Tunnel 上传和下载性能受到各种因素影响较大。首先，考虑对本地代码的优化，"
"主要有下面的优化点："
msgstr ""
"Performance of tunnel upload and download can be affected by various "
"factors. First, you may consider optimizing your local code, which "
"includes:"

#: ../../source/base-tables.rst:946
msgid ""
"减少创建 Upload Session 或者 Download Session 的次数，尽量复用。Tunnel "
"Session 本身创建代价较大，\\ 因而除非必要，一次读取或写入只应当创建一个。"
msgstr ""
"Reduce the number of times of creating upload or download sessions. Cost "
"of tunnel session creation is high, so unless necessary, only one upload "
"session should be created for each read or write."

#: ../../source/base-tables.rst:948
msgid "增加每个 Reader / Writer 读取或者写入的数据量。"
msgstr "Increase the amount of data read or written by readers or writers."

#: ../../source/base-tables.rst:949
msgid "启用数据压缩以减小传输的数据量。"
msgstr "Compress data to reduce the amount of data to transfer."

#: ../../source/base-tables.rst:950
msgid ""
"如果数据源或者需要的数据目标为 pandas，由于 Record 类型本身需要较大的 "
"Python 解释器时间开销，因而建议尽量采用 Arrow 接口进行读写。"
msgstr ""
"If the data source or target is pandas, since Record type itself has time"
" overhead in Python interpreter, it is recommended to use Arrow interface"
" for pandas data if possible."

#: ../../source/base-tables.rst:952
msgid ""
"如有可能，使用多线程或者 multiprocessing 进行读写。需要注意的是，Python "
"使用了 GIL，因而如果你读写数据\\ 前的预处理步骤使用了较多纯 Python 代码，"
"那么多线程可能未必提升性能。"
msgstr ""
"Use multi-threading or multiprocessing for reading or writing if "
"possible. Note that Python uses GIL, so if you use a lot of pure Python "
"code to do data preprocessing before reading or writing, multi-threading "
"may not improve performance much."

#: ../../source/base-tables.rst:955
msgid ""
"此外，读写数据时的网络状况等因素也可能影响上传和下载速度，可能发生共享 "
"Tunnel 服务资源用满或者客户端到 Tunnel 服务网络链路不稳定等因素。针对这些"
"情形，可以考虑购买独享资源 Tunnel 或者使用阿里云内网，相关信息可以\\ 参考"
"\\ `Tunnel 文档 <https://help.aliyun.com/zh/maxcompute/user-guide/"
"overview-of-dts#094b91802f18e>`_。"
msgstr ""
"In addition, factors such as network conditions when reading or writing "
"data may also affect upload or download speed, and Tunnel service "
"resources may be exhausted or network instability may occur. For these "
"cases, you may consider purchasing exclusive Tunnel resources or "
"uploading data in Alibaba Cloud intranet. More information can be found "
"in `Tunnel Documentation <https://help.aliyun.com/zh/maxcompute/user-"
"guide/overview-of-dts#094b91802f18e>`_."

