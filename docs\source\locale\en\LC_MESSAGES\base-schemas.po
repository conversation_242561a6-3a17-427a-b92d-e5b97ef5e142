# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.11.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-17 11:44+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-schemas.rst:4
msgid "Schema"
msgstr ""

#: ../../source/base-schemas.rst:8
msgid ""
"Schema 属于 MaxCompute 的公测功能，需要通过 `新功能测试申请 <https://help"
".aliyun.com/document_detail/128366.htm>`_ 开通。使用 Schema 需要 PyODPS "
"0.11.3 以上版本。"
msgstr ""
"Schema is a beta function of MaxCompute. You need to `apply for a trial "
"of new features <https://www.alibabacloud.com/help/en/maxcompute/latest"
"/apply-for-a-trial-of-new-features>`_ before accessing it. PyODPS above "
"0.11.3 is also needed."

#: ../../source/base-schemas.rst:11
msgid ""
"`Schema <https://help.aliyun.com/document_detail/437084.html>`_ 是 "
"MaxCompute 介于项目和表 / 资源 / 函数之间的概念，对表 / 资源 / 函数进行"
"进一步归类。"
msgstr ""
"`Schema <https://help.aliyun.com/document_detail/437084.html>`_ is a "
"concept between projects and objects like tables, resources or functions."
" It maintains categories for these objects."

#: ../../source/base-schemas.rst:15
msgid "Schema 基本操作"
msgstr "Basic operations"

#: ../../source/base-schemas.rst:16
msgid "你可以使用 :meth:`~odps.ODPS.exist_schema` 判断 Schema 对象是否存在："
msgstr ""
"You may use :meth:`~odps.ODPS.exist_schema` to check if the schema with "
"specific name exists."

#: ../../source/base-schemas.rst:18
msgid "print(o.exist_schema(\"test_schema\"))"
msgstr ""

#: ../../source/base-schemas.rst:22
msgid "使用 :meth:`~odps.ODPS.create_schema` 创建一个 Schema 对象："
msgstr "Use :meth:`~odps.ODPS.create_schema` to create a schema object."

#: ../../source/base-schemas.rst:24
msgid ""
"schema = o.create_schema(\"test_schema\")\n"
"print(schema)"
msgstr ""

#: ../../source/base-schemas.rst:29
msgid "使用 :meth:`~odps.ODPS.delete_schema` 删除一个 Schema 对象："
msgstr "Use :meth:`~odps.ODPS.delete_schema` to delete a schema object."

#: ../../source/base-schemas.rst:31
msgid "schema = o.delete_schema(\"test_schema\")"
msgstr ""

#: ../../source/base-schemas.rst:35
msgid ""
"使用 :meth:`~odps.ODPS.get_schema` 获得一个 Schema 对象并打印 Schema "
"Owner："
msgstr ""
"Use :meth:`~odps.ODPS.get_schema` to obtain a schema object and print its"
" owner."

#: ../../source/base-schemas.rst:37
msgid ""
"schema = o.get_schema(\"test_schema\")\n"
"print(schema.owner)"
msgstr ""

#: ../../source/base-schemas.rst:42
msgid "使用 :meth:`~odps.ODPS.list_schema` 列举所有 Schema 对象并打印名称："
msgstr ""
"Use :meth:`~odps.ODPS.list_schema` to list all schemas in s project and "
"print their names."

#: ../../source/base-schemas.rst:44
msgid ""
"for schema in o.list_schema():\n"
"    print(schema.name)"
msgstr ""

#: ../../source/base-schemas.rst:50
msgid "操作 Schema 中的对象"
msgstr "Handling objects in Schema"

#: ../../source/base-schemas.rst:51
msgid ""
"在开启 Schema 后，MaxCompute 入口对象默认操作的 MaxCompute 对象都位于名为"
" ``DEFAULT`` 的 Schema 下。为操作其他 Schema 下的对象，需要在创建入口对象"
"时指定 Schema，例如："
msgstr ""
"After schemas are enabled, calls on your MaxCompute entrance only affects"
" objects in the schema named ``DEFAULT`` by default. To handle objects in"
" other schemas, you need to provide the name of the schema. For instance,"

#: ../../source/base-schemas.rst:54
msgid ""
"import os\n"
"from odps import ODPS\n"
"# 保证 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量设置为用户 Access Key ID，\n"
"# ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量设置为用户 Access Key Secret\n"
"# 不建议直接使用 Access Key ID / Access Key Secret 字符串\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
"    schema='**your-schema-name**',\n"
")"
msgstr ""
"import os\n"
"from odps import ODPS\n"
"# Make sure environment variable ALIBABA_CLOUD_ACCESS_KEY_ID already set "
"to Access Key ID of user\n"
"# while environment variable ALIBABA_CLOUD_ACCESS_KEY_SECRET set to "
"Access Key Secret of user.\n"
"# Not recommended to hardcode Access Key ID or Access Key Secret in your "
"code.\n"
"o = ODPS(\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),\n"
"    os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),\n"
"    project='**your-project**',\n"
"    endpoint='**your-endpoint**',\n"
"    schema='**your-schema-name**',\n"
")"

#: ../../source/base-schemas.rst:69
msgid ""
"也可以为不同对象的操作方法指定 ``schema`` 参数。例如，下面的方法列举了 ``"
"test_schema`` 下所有的表："
msgstr ""
"You can also specify names of schemas when handling MaxCompute objects. "
"For instance, the code below lists all tables under the schema "
"``test_schema``."

#: ../../source/base-schemas.rst:72
msgid ""
"for table in o.list_tables(schema='test_schema'):\n"
"    print(table)"
msgstr ""

#: ../../source/base-schemas.rst:77
msgid "下列方法给出了如何从 ``test_schema`` 获取表 ``dual`` 并输出表结构："
msgstr ""
"The code below gets a table named ``dual`` under schema named "
"``test_schema``and outputs its structure."

#: ../../source/base-schemas.rst:79
msgid ""
"table = o.get_table('dual', schema='test_schema')\n"
"print(table.table_schema)"
msgstr ""

#: ../../source/base-schemas.rst:84
msgid "在执行 SQL 时，可以指定默认 Schema："
msgstr ""
"You can also specify name of the default schema when executing SQL "
"statements."

#: ../../source/base-schemas.rst:86
msgid "o.execute_sql(\"SELECT * FROM dual\", default_schema=\"test_schema\")"
msgstr ""

#: ../../source/base-schemas.rst:90
msgid ""
"对于表而言，如果项目空间没有启用 Schema，``get_table`` 方法对于 ``x.y`` "
"形式的表名，默认按照 ``project.table`` 处理。如果当前租户开启了\\ `租户级"
"语法开关 <https://help.aliyun.com/zh/maxcompute/user-guide/tenant-"
"information>`_\\ ，\\ ``get_table`` 会将 ``x.y`` 作为 ``schema.table`` "
"处理，否则依然按照 ``project.table`` 处理。如果租户上没有配置该选项，可以"
"配置 ``options.enable_schema = True``，此后所有 ``x.y`` 都将被作为 ``"
"schema.table`` 处理："
msgstr ""
"For tables, if schema is not enabled in project, ``get_table`` will "
"handle ``x.y`` as ``project.table``. When `tenant-level information "
"schema syntax <https://www.alibabacloud.com/help/en/maxcompute/user-guide"
"/tenant-information>`_ is enabled for current tenant, ``get_table`` will "
"handle ``x.y`` as ``schema.table``, or it will be still handled as "
"``project.table``. If the option is not specified, you may configure "
"``options.enable_schema = True`` in your Python code and then all table "
"names like ``x.y`` will be handled as ``schema.table``."

#: ../../source/base-schemas.rst:96
msgid ""
"from odps import options\n"
"options.enable_schema = True\n"
"print(o.get_table(\"myschema.mytable\"))"
msgstr ""

#: ../../source/base-schemas.rst:104
msgid ""
"``options.enable_schema`` 自 PyODPS 0.12.0 开始支持，低版本 PyODPS 需要"
"使用 ``options.always_enable_schema``。"
msgstr ""
"``options.enable_schema`` is supported since PyODPS 0.12.0. "
"``options.always_enable_schema`` should be used in lower versions."

