# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-15 13:24+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/api-df.rst:4 odps.df.expr.collections.bfill:5
#: odps.df.expr.collections.dropna:7 odps.df.expr.collections.ffill:5
#: odps.df.expr.collections.fillna:7 of
msgid "DataFrame"
msgstr ""

#: odps.df.core.DataFrame:1 of
msgid "Main entrance of PyODPS DataFrame."
msgstr ""

#: odps.df.core.DataFrame:3 of
msgid "Users can initial a DataFrame by :class:`odps.models.Table`."
msgstr ""

#: ../../source/api-df.rst odps.df.core.DataFrame.batch_persist
#: odps.df.expr.collections.apply odps.df.expr.collections.applymap
#: odps.df.expr.collections.distinct odps.df.expr.collections.extract_kv
#: odps.df.expr.collections.map_reduce odps.df.expr.collections.melt
#: odps.df.expr.collections.pivot odps.df.expr.collections.pivot_table
#: odps.df.expr.collections.sample odps.df.expr.collections.sort_values
#: odps.df.expr.collections.to_kv odps.df.expr.element._map
#: odps.df.expr.element._switch odps.df.expr.expressions.Expr.persist
#: odps.df.expr.expressions.SequenceExpr.astype odps.df.expr.merge._drop
#: odps.df.expr.merge.concat odps.df.expr.merge.inner_join
#: odps.df.expr.merge.intersect odps.df.expr.merge.join
#: odps.df.expr.merge.left_join odps.df.expr.merge.outer_join
#: odps.df.expr.merge.right_join odps.df.expr.merge.setdiff
#: odps.df.expr.merge.union odps.df.tools.libtools.bloomfilter
#: odps.df.tools.libtools.hll_count of
msgid "Parameters"
msgstr ""

#: odps.df.core.DataFrame:5 of
msgid "ODPS table or pandas DataFrame"
msgstr ""

#: odps.df.core.DataFrame odps.df.expr.collections.apply
#: odps.df.expr.collections.applymap odps.df.expr.collections.distinct
#: odps.df.expr.collections.extract_kv odps.df.expr.collections.map_reduce
#: odps.df.expr.collections.melt odps.df.expr.collections.pivot
#: odps.df.expr.collections.pivot_table odps.df.expr.collections.sample
#: odps.df.expr.collections.sort_values odps.df.expr.collections.to_kv
#: odps.df.expr.element._map odps.df.expr.element._switch
#: odps.df.expr.expressions.CollectionExpr
#: odps.df.expr.expressions.Expr.persist odps.df.expr.expressions.RandomScalar
#: odps.df.expr.expressions.Scalar odps.df.expr.expressions.SequenceExpr.astype
#: odps.df.expr.merge._drop odps.df.expr.merge.concat
#: odps.df.expr.merge.inner_join odps.df.expr.merge.join
#: odps.df.expr.merge.left_join odps.df.expr.merge.outer_join
#: odps.df.expr.merge.right_join odps.df.expr.merge.union
#: odps.df.tools.libtools.bloomfilter odps.df.tools.libtools.hll_count
#: odps.ml.expr.mixin.MLCollectionMixin.continuous
#: odps.ml.expr.mixin.MLCollectionMixin.discrete
#: odps.ml.expr.mixin.MLCollectionMixin.erase_key_value
#: odps.ml.expr.mixin.MLCollectionMixin.key_value
#: odps.ml.expr.mixin.MLSequenceMixin.continuous
#: odps.ml.expr.mixin.MLSequenceMixin.discrete
#: odps.ml.expr.mixin.MLSequenceMixin.erase_key_value
#: odps.ml.expr.mixin.MLSequenceMixin.key_value of
msgid "Example"
msgstr ""

#: odps.df.core.DataFrame.batch_persist:1 of
msgid "Persist multiple DataFrames into ODPS."
msgstr ""

#: odps.df.core.DataFrame.batch_persist:3 of
msgid "DataFrames to persist."
msgstr ""

#: odps.df.core.DataFrame.batch_persist:4 of
msgid ""
"Table names to persist to. Use (table, partition) tuple to store to a "
"table partition."
msgstr ""

#: odps.df.core.DataFrame.batch_persist:5 of
msgid "args for Expr.persist"
msgstr ""

#: odps.df.core.DataFrame.batch_persist:6 of
msgid "kwargs for Expr.persist"
msgstr ""

#: odps.df.core.DataFrame.batch_persist odps.df.expr.merge.intersect
#: odps.df.expr.merge.setdiff of
msgid "Examples"
msgstr ""

#: odps.df.core.DataFrame.view:1 odps.df.expr.expressions.CollectionExpr.view:1
#: of
msgid "Clone a same collection. useful for self-join."
msgstr ""

#: ../../source/api-df.rst odps.df.expr.collections.apply
#: odps.df.expr.collections.applymap odps.df.expr.collections.distinct
#: odps.df.expr.collections.extract_kv odps.df.expr.collections.map_reduce
#: odps.df.expr.collections.melt odps.df.expr.collections.pivot
#: odps.df.expr.collections.pivot_table odps.df.expr.collections.sample
#: odps.df.expr.collections.sort_values odps.df.expr.collections.to_kv
#: odps.df.expr.element._map odps.df.expr.element._switch
#: odps.df.expr.expressions.Expr.persist
#: odps.df.expr.expressions.SequenceExpr.astype odps.df.expr.merge._drop
#: odps.df.expr.merge.concat odps.df.expr.merge.inner_join
#: odps.df.expr.merge.intersect odps.df.expr.merge.join
#: odps.df.expr.merge.left_join odps.df.expr.merge.outer_join
#: odps.df.expr.merge.right_join odps.df.expr.merge.setdiff
#: odps.df.expr.merge.union odps.df.tools.libtools.bloomfilter
#: odps.df.tools.libtools.hll_count of
msgid "Returns"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr:1 of
msgid "Collection represents for the two-dimensions data."
msgstr ""

#: odps.df.expr.reduction.all_:1 of
msgid "All is True."
msgstr ""

#: odps.df.expr.reduction.any_:1 of
msgid "Any is True."
msgstr ""

#: odps.df.expr.collections.append_id:1 of
msgid "Append an ID column to current column to form a new DataFrame."
msgstr ""

#: odps.df.expr.collections.append_id:3 of
msgid "name of appended ID field."
msgstr ""

#: odps.df.expr.collections.append_id:5 of
msgid "DataFrame with ID field"
msgstr ""

#: ../../source/api-df.rst odps.df.expr.collections.extract_kv
#: odps.df.expr.collections.to_kv
#: odps.ml.expr.mixin.MLCollectionMixin.continuous
#: odps.ml.expr.mixin.MLCollectionMixin.discrete
#: odps.ml.expr.mixin.MLCollectionMixin.erase_key_value
#: odps.ml.expr.mixin.MLCollectionMixin.key_value
#: odps.ml.expr.mixin.MLSequenceMixin.continuous
#: odps.ml.expr.mixin.MLSequenceMixin.discrete
#: odps.ml.expr.mixin.MLSequenceMixin.erase_key_value
#: odps.ml.expr.mixin.MLSequenceMixin.key_value of
msgid "Return type"
msgstr ""

#: odps.df.expr.collections.apply:1 of
msgid "Apply a function to a row when axis=1 or column when axis=0."
msgstr ""

#: odps.df.expr.collections.apply:4 of
msgid "function to apply"
msgstr ""

#: odps.df.expr.collections.apply:5 of
msgid "row when axis=1 else column"
msgstr ""

#: odps.df.expr.collections.apply:6 of
msgid "output names"
msgstr ""

#: odps.df.expr.collections.apply:7 of
msgid "output types"
msgstr ""

#: odps.df.expr.collections.apply:8 of
msgid "if True will return a sequence else return a collection"
msgstr ""

#: odps.df.expr.collections.apply:9 of
msgid "resources to read"
msgstr ""

#: odps.df.expr.collections.apply:10 of
msgid "if True, keep rows producing empty results, only work in lateral views"
msgstr ""

#: odps.df.expr.collections.apply:11 of
msgid "args for function"
msgstr ""

#: odps.df.expr.collections.apply:12 of
msgid "kwargs for function"
msgstr ""

#: odps.df.expr.collections.apply:17 of
msgid "Apply a function to a row:"
msgstr ""

#: odps.df.expr.collections.apply:29 of
msgid "Apply a function to a column:"
msgstr ""

#: odps.df.expr.collections.applymap:1 of
msgid "Call func on each element of this collection."
msgstr ""

#: odps.df.expr.collections.applymap:3 odps.df.expr.element._map:3 of
msgid ""
"lambda, function, :class:`odps.models.Function`, or str which is the name"
" of :class:`odps.models.Funtion`"
msgstr ""

#: odps.df.expr.collections.applymap:5 odps.df.expr.element._map:5 of
msgid "if not provided, will be the dtype of this sequence"
msgstr ""

#: odps.df.expr.collections.applymap:6 of
msgid "columns to apply this function on"
msgstr ""

#: odps.df.expr.collections.applymap:7 of
msgid "columns to skip when applying the function"
msgstr ""

#: odps.df.expr.collections.applymap:8 of
msgid "a new collection"
msgstr ""

#: odps.df.expr.expressions.Expr.ast:1 of
msgid "Return the AST string."
msgstr ""

#: odps.df.expr.expressions.Expr.ast:3 of
msgid "AST tree"
msgstr ""

#: odps.df.expr.collections.bfill:1 of
msgid ""
"Fill NA/NaN values with the backward method. Equivalent to "
"fillna(method='bfill')."
msgstr ""

#: odps.df.expr.collections.bfill:3 odps.df.expr.collections.ffill:3 of
msgid "input DataFrame."
msgstr ""

#: odps.df.expr.collections.bfill:4 odps.df.expr.collections.ffill:4
#: odps.df.expr.collections.fillna:6 of
msgid "Labels along other axis to consider."
msgstr ""

#: odps.df.tools.libtools.bloomfilter:1 of
msgid "Filter collection on the `on` sequence by BloomFilter built by `column`"
msgstr ""

#: odps.df.tools.libtools.bloomfilter:4 of
msgid "sequence or column name"
msgstr ""

#: odps.df.tools.libtools.bloomfilter:5 of
msgid "instance of Column"
msgstr ""

#: odps.df.tools.libtools.bloomfilter:6 of
msgid "numbers of capacity"
msgstr ""

#: odps.df.tools.libtools.bloomfilter:8 odps.df.tools.libtools.hll_count:4 of
msgid "error rate"
msgstr ""

#: odps.df.expr.collections.distinct:3 odps.df.expr.collections.melt:7
#: odps.df.expr.collections.melt:13 odps.df.expr.collections.pivot:4
#: odps.df.expr.collections.pivot:8 odps.df.expr.collections.pivot_table:3
#: odps.df.expr.collections.pivot_table:9 odps.df.expr.collections.reshuffle:7
#: odps.df.expr.collections.sample:3 odps.df.expr.collections.sample:13
#: odps.df.expr.collections.sort_values:3 odps.df.expr.groupby.groupby:3
#: odps.df.expr.merge._drop:7 odps.df.expr.merge.concat:7
#: odps.df.expr.merge.inner_join:12 odps.df.expr.merge.intersect:3
#: odps.df.expr.merge.intersect:6 odps.df.expr.merge.join:17
#: odps.df.expr.merge.left_join:18 odps.df.expr.merge.outer_join:18
#: odps.df.expr.merge.right_join:18 odps.df.expr.merge.setdiff:7
#: odps.df.expr.merge.union:6 odps.df.tools.libtools.bloomfilter:10 of
msgid "collection"
msgstr ""

#: odps.df.CollectionExpr.columns:1 of
msgid "columns :rtype: list which each element is a Column"
msgstr ""

#: odps.df.CollectionExpr.columns of
msgid "type"
msgstr ""

#: odps.df.CollectionExpr.columns:4 of
msgid "return"
msgstr ""

#: odps.df.expr.expressions.Expr.compile:1 of
msgid "Compile this expression into an ODPS SQL"
msgstr ""

#: odps.df.expr.expressions.Expr.compile:3 of
msgid "compiled DAG"
msgstr ""

#: odps.df.expr.merge.concat:1 of
msgid "Concat collections."
msgstr ""

#: odps.df.expr.merge.concat:3 odps.df.expr.merge.inner_join:8
#: odps.df.expr.merge.join:8 odps.df.expr.merge.left_join:8
#: odps.df.expr.merge.outer_join:8 odps.df.expr.merge.right_join:8
#: odps.df.expr.merge.union:3 of
msgid "left collection"
msgstr ""

#: odps.df.expr.merge.concat:4 of
msgid "right collections, can be a DataFrame object or a list of DataFrames"
msgstr ""

#: odps.df.expr.merge.concat:5 of
msgid "whether to remove duplicate entries. only available when axis == 0"
msgstr ""

#: odps.df.expr.merge.concat:6 of
msgid ""
"when axis == 0, the DataFrames are merged vertically, otherwise "
"horizontally."
msgstr ""

#: odps.df.expr.merge.concat:9 of
msgid "Note that axis==1 can only be used under Pandas DataFrames or XFlow."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.continuous:1 of
msgid "Set fields to be continuous."
msgstr ""

#: odps.df.expr.expressions._count:1 odps.df.expr.reduction.count:1 of
msgid "Value counts"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.discrete:1 of
msgid "Set fields to be discrete."
msgstr ""

#: odps.df.expr.collections.distinct:1 of
msgid ""
"Get collection with duplicate rows removed, optionally only considering "
"certain columns"
msgstr ""

#: odps.df.expr.collections.distinct:4 of
msgid "sequence or sequences"
msgstr ""

#: odps.df.expr.collections.distinct:5 of
msgid "dinstinct collection"
msgstr ""

#: odps.df.expr.merge._drop:1 of
msgid "Drop data from a DataFrame."
msgstr ""

#: odps.df.expr.merge._drop:3 odps.df.expr.merge.setdiff:4 of
msgid "collection to drop data from"
msgstr ""

#: odps.df.expr.merge._drop:4 of
msgid "data to be removed"
msgstr ""

#: odps.df.expr.merge._drop:5 of
msgid "0 for deleting rows, 1 for columns."
msgstr ""

#: odps.df.expr.merge._drop:6 of
msgid "columns of data to select, only useful when axis == 0"
msgstr ""

#: odps.df.expr.collections.dropna:1 of
msgid ""
"Return object with labels on given axis omitted where alternately any or "
"all of the data are missing"
msgstr ""

#: odps.df.expr.collections.dropna:3 odps.df.expr.collections.extract_kv:4
#: odps.df.expr.collections.fillna:3 odps.df.expr.collections.min_max_scale:3
#: odps.df.expr.collections.to_kv:3 of
msgid "input DataFrame"
msgstr ""

#: odps.df.expr.collections.dropna:4 of
msgid ""
"can be ‘any’ or ‘all’. If 'any' is specified any NA values are present, "
"drop that label. If 'all' is specified and all values are NA, drop that "
"label."
msgstr ""

#: odps.df.expr.collections.dropna:5 of
msgid "require that many non-NA values"
msgstr ""

#: odps.df.expr.collections.dropna:6 of
msgid ""
"Labels along other axis to consider, e.g. if you are dropping rows these "
"would be a list of columns to include"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.erase_key_value:1
#: odps.ml.expr.mixin.MLSequenceMixin.erase_key_value:1 of
msgid "Erase key-value represented fields."
msgstr ""

#: odps.df.expr.merge.setdiff:1 of
msgid ""
"Exclude data from a collection, like `except` clause in SQL. All "
"collections involved should have same schema."
msgstr ""

#: odps.df.expr.merge.intersect:4 odps.df.expr.merge.setdiff:5 of
msgid "collection or list of collections"
msgstr ""

#: odps.df.expr.merge.setdiff:6 of
msgid "whether to preserve duplicate entries"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.exclude:1 of
msgid "Projection columns which not included in the fields"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.exclude:3 of
msgid "field names"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.exclude:4
#: odps.df.expr.expressions.CollectionExpr.filter:4
#: odps.df.expr.expressions.CollectionExpr.filter_parts:6
#: odps.df.expr.expressions.CollectionExpr.query:4
#: odps.df.expr.expressions.CollectionExpr.select:5 of
msgid "new collection"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.exclude:5
#: odps.df.expr.expressions.CollectionExpr.select:6 of
msgid ":class:`odps.df.expr.expression.CollectionExpr`"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.exclude_fields:1 of
msgid "Exclude one or more fields from feature fields."
msgstr ""

#: odps.df.expr.expressions.Expr.execute:1
#: odps.df.expr.expressions.Expr.persist:15 of
msgid "settings for SQL, e.g. `odps.sql.mapper.split.size`"
msgstr ""

#: odps.df.expr.expressions.Expr.execute:3
#: odps.df.expr.expressions.Expr.persist:17 of
msgid "instance priority, 9 as default"
msgstr ""

#: odps.df.expr.expressions.Expr.execute:5
#: odps.df.expr.expressions.Expr.persist:19 of
msgid "cluster to run this instance"
msgstr ""

#: odps.df.expr.expressions.Expr.execute:6 of
msgid "execution result"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.head:5
#: odps.df.expr.expressions.CollectionExpr.tail:5
#: odps.df.expr.expressions.Expr.execute:7 of
msgid ":class:`odps.df.backends.frame.ResultFrame`"
msgstr ""

#: odps.df.expr.collections.extract_kv:1 of
msgid ""
"Extract values in key-value represented columns into standalone columns. "
"New column names will be the name of the key-value column followed by an "
"underscore and the key."
msgstr ""

#: odps.df.expr.collections.extract_kv:5 of
msgid "the key-value columns to be extracted."
msgstr ""

#: odps.df.expr.collections.extract_kv:6 odps.df.expr.collections.to_kv:5 of
msgid "delimiter between key and value."
msgstr ""

#: odps.df.expr.collections.extract_kv:7 odps.df.expr.collections.to_kv:6 of
msgid "delimiter between key-value pairs."
msgstr ""

#: odps.df.expr.collections.extract_kv:8 of
msgid "type of value columns to generate."
msgstr ""

#: odps.df.expr.collections.extract_kv:9 of
msgid "default value for missing key-value pairs."
msgstr ""

#: odps.df.expr.collections.extract_kv:11 of
msgid "extracted data frame"
msgstr ""

#: odps.df.expr.collections.ffill:1 of
msgid ""
"Fill NA/NaN values with the forward method. Equivalent to "
"fillna(method='ffill')."
msgstr ""

#: odps.df.expr.collections.fillna:1 of
msgid "Fill NA/NaN values using the specified method"
msgstr ""

#: odps.df.expr.collections.fillna:4 of
msgid "can be ‘backfill’, ‘bfill’, ‘pad’, ‘ffill’ or None"
msgstr ""

#: odps.df.expr.collections.fillna:5 odps.df.expr.element._fillna:4 of
msgid "value to fill into"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter:1 of
msgid "Filter the data by predicates"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter:3 of
msgid "the conditions to filter"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter:5
#: odps.df.expr.expressions.CollectionExpr.filter_parts:7
#: odps.df.expr.expressions.CollectionExpr.query:5
#: odps.df.expr.expressions.SequenceExpr.head:5
#: odps.df.expr.groupby.value_counts:11 of
msgid ":class:`odps.df.expr.expressions.CollectionExpr`"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter_parts:1 of
msgid ""
"Filter the data by partition string. A partition string looks like "
"`pt1=1,pt2=2/pt1=2,pt2=1`, where comma (,) denotes 'and', while (/) "
"denotes 'or'."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter_parts:4 of
msgid "predicate string of partition filter"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.filter_parts:5 of
msgid ""
"True if you want to exclude partition fields, otherwise False. True for "
"default."
msgstr ""

#: odps.df.expr.groupby.groupby:1 of
msgid "Group collection by a series of sequences."
msgstr ""

#: odps.df.expr.groupby.groupby:4 odps.df.expr.groupby.groupby:5 of
msgid "columns to group"
msgstr ""

#: odps.df.expr.groupby.groupby:6 of
msgid "GroupBy instance"
msgstr ""

#: odps.df.expr.groupby.groupby:7 of
msgid ":class:`odps.df.expr.groupby.GroupBy`"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.head:1 of
msgid "Return the first n rows. Execute at once."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.head:4
#: odps.df.expr.expressions.CollectionExpr.tail:4
#: odps.df.expr.expressions.SequenceExpr.head:4 of
msgid "result frame"
msgstr ""

#: odps.df.expr.merge.inner_join:1 of
msgid "Inner join two collections."
msgstr ""

#: odps.df.expr.merge.inner_join:3 odps.df.expr.merge.join:3
#: odps.df.expr.merge.left_join:3 odps.df.expr.merge.outer_join:3
#: odps.df.expr.merge.right_join:3 of
msgid ""
"If `on` is not specified, we will find the common fields of the left and "
"right collection. `suffixes` means that if column names conflict, the "
"suffixes will be added automatically. For example, both left and right "
"has a field named `col`, there will be col_x, and col_y in the joined "
"collection."
msgstr ""

#: odps.df.expr.merge.inner_join:9 odps.df.expr.merge.join:9
#: odps.df.expr.merge.left_join:9 odps.df.expr.merge.outer_join:9
#: odps.df.expr.merge.right_join:9 odps.df.expr.merge.union:4 of
msgid "right collection"
msgstr ""

#: odps.df.expr.merge.inner_join:10 odps.df.expr.merge.join:10
#: odps.df.expr.merge.left_join:10 odps.df.expr.merge.outer_join:10
#: odps.df.expr.merge.right_join:10 of
msgid "fields to join on"
msgstr ""

#: odps.df.expr.merge.inner_join:11 odps.df.expr.merge.left_join:11
#: odps.df.expr.merge.outer_join:11 odps.df.expr.merge.right_join:11 of
msgid "when name conflict, the suffixes will be added to both columns."
msgstr ""

#: odps.df.expr.merge.intersect:1 of
msgid "Calc intersection among datasets,"
msgstr ""

#: odps.df.expr.merge.intersect:5 of
msgid "whether to preserve duolicate entries"
msgstr ""

#: odps.df.expr.merge.join:1 of
msgid "Join two collections."
msgstr ""

#: odps.df.expr.merge.join:11 of
msgid "'inner', 'left', 'right', or 'outer'"
msgstr ""

#: odps.df.expr.merge.join:12 of
msgid "when name conflict, the suffix will be added to both columns."
msgstr ""

#: odps.df.expr.merge.join:13 odps.df.expr.merge.left_join:12
#: odps.df.expr.merge.outer_join:12 odps.df.expr.merge.right_join:12 of
msgid "set use mapjoin or not, default value False."
msgstr ""

#: odps.df.expr.merge.join:14 of
msgid ""
"set use of skewjoin or not, default value False. Can specify True if the "
"collection is skew, or a list specifying columns with skew values, or a "
"list of dicts specifying skew combinations."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.key_value:1
#: odps.ml.expr.mixin.MLSequenceMixin.key_value:1 of
msgid "Set fields to be key-value represented."
msgstr ""

#: odps.df.expr.reduction.kurtosis:1 of
msgid "Calculate kurtosis of the sequence"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.label_field:1 of
msgid "Select one field as the label field."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.label_field:3
#: odps.ml.expr.mixin.MLCollectionMixin.weight_field:3 of
msgid "Note that this field will be exclude from feature fields."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.label_field:5 of
msgid "Selected label field"
msgstr ""

#: odps.df.expr.merge.left_join:1 of
msgid "Left join two collections."
msgstr ""

#: odps.df.expr.merge.left_join:13 odps.df.expr.merge.outer_join:13
#: odps.df.expr.merge.right_join:13 of
msgid ""
"whether to merge columns with the same name into one column without "
"suffix. If the value is True, columns in the predicate with same names "
"will be merged, with non-null value. If the value is 'left' or 'right', "
"the values of predicates on the left / right collection will be taken. "
"You can also pass a dictionary to describe the behavior of each column, "
"such as { 'a': 'auto', 'b': 'left' }."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.limit:1 of
msgid "limit n records"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.limit:3 of
msgid "n records"
msgstr ""

#: odps.df.expr.collections.map_reduce:1 of
msgid "MapReduce API, mapper or reducer should be provided."
msgstr ""

#: odps.df.expr.collections.map_reduce:4 of
msgid "mapper function or class"
msgstr ""

#: odps.df.expr.collections.map_reduce:5 of
msgid "reducer function or class"
msgstr ""

#: odps.df.expr.collections.map_reduce:6 of
msgid "the keys to group after mapper"
msgstr ""

#: odps.df.expr.collections.map_reduce:7 of
msgid "the keys to sort after mapper"
msgstr ""

#: odps.df.expr.collections.map_reduce:8 odps.df.expr.collections.reshuffle:6
#: of
msgid "True if ascending else False"
msgstr ""

#: odps.df.expr.collections.map_reduce:9 of
msgid "combiner function or class, combiner's output should be equal to mapper"
msgstr ""

#: odps.df.expr.collections.map_reduce:10 of
msgid "combiner's buffer size, 1024 as default"
msgstr ""

#: odps.df.expr.collections.map_reduce:11 of
msgid "mapper's output names"
msgstr ""

#: odps.df.expr.collections.map_reduce:12 of
msgid "mapper's output types"
msgstr ""

#: odps.df.expr.collections.map_reduce:13 of
msgid "the resources for mapper"
msgstr ""

#: odps.df.expr.collections.map_reduce:14 of
msgid "reducer's output names"
msgstr ""

#: odps.df.expr.collections.map_reduce:15 of
msgid "reducer's output types"
msgstr ""

#: odps.df.expr.collections.map_reduce:16 of
msgid "the resources for reducer"
msgstr ""

#: odps.df.expr.reduction.max_:1 of
msgid "Max value"
msgstr ""

#: odps.df.expr.reduction.mean:1 of
msgid "Arithmetic mean."
msgstr ""

#: odps.df.expr.reduction.median:1 of
msgid "Median value."
msgstr ""

#: odps.df.expr.collections.melt:1 of
msgid ""
"“Unpivots” a DataFrame from wide format to long format, optionally "
"leaving identifier variables set."
msgstr ""

#: odps.df.expr.collections.melt:3 of
msgid ""
"This function is useful to massage a DataFrame into a format where one or"
" more columns are identifier variables (id_vars), while all other "
"columns, considered measured variables (value_vars), are “unpivoted” to "
"the row axis, leaving just two non-identifier columns, ‘variable’ and "
"‘value’."
msgstr ""

#: odps.df.expr.collections.melt:8 of
msgid "column(s) to use as identifier variables."
msgstr ""

#: odps.df.expr.collections.melt:9 of
msgid ""
"column(s) to unpivot. If not specified, uses all columns that are not set"
" as id_vars."
msgstr ""

#: odps.df.expr.collections.melt:10 of
msgid ""
"name to use for the ‘variable’ column. If None it uses frame.columns.name"
" or ‘variable’."
msgstr ""

#: odps.df.expr.collections.melt:11 of
msgid "name to use for the ‘value’ column."
msgstr ""

#: odps.df.expr.collections.melt:12 of
msgid "whether to ignore NaN values in data."
msgstr ""

#: odps.df.expr.reduction.min_:1 of
msgid "Min value"
msgstr ""

#: odps.df.expr.collections.min_max_scale:1 of
msgid ""
"Resize a data frame by max / min values, i.e., (X - min(X)) / (max(X) - "
"min(X))"
msgstr ""

#: odps.df.expr.collections.min_max_scale:4 of
msgid "the target range to resize the value into, i.e., v * (b - a) + a"
msgstr ""

#: odps.df.expr.collections.min_max_scale:5 of
msgid ""
"determine whether input data should be kept. If True, scaled input data "
"will be appended to the data frame with `suffix`"
msgstr ""

#: odps.df.expr.collections.min_max_scale:6 of
msgid ""
"columns names to resize. If set to None, float or int-typed columns will "
"be normalized if the column is not specified as a group column."
msgstr ""

#: odps.df.expr.collections.min_max_scale:7
#: odps.df.expr.collections.std_scale:8 of
msgid "determine scale groups. Scaling will be done in each group separately."
msgstr ""

#: odps.df.expr.collections.min_max_scale:8
#: odps.df.expr.collections.std_scale:9 of
msgid "column suffix to be appended to the scaled columns."
msgstr ""

#: odps.df.expr.collections.min_max_scale:10
#: odps.df.expr.collections.std_scale:11 of
msgid "resized data frame"
msgstr ""

#: odps.df.expr.reduction.moment:1 of
msgid "Calculate the n-th order moment of the sequence"
msgstr ""

#: odps.df.expr.reduction.moment:4 of
msgid "moment order, must be an integer"
msgstr ""

#: odps.df.expr.reduction.moment:5 of
msgid "if central moments are to be computed."
msgstr ""

#: odps.df.expr.reduction.nunique:1 of
msgid "The distinct count."
msgstr ""

#: odps.df.expr.merge.outer_join:1 of
msgid "Outer join two collections."
msgstr ""

#: odps.df.expr.expressions.Expr.persist:1 of
msgid ""
"Persist the execution into a new table. If `partitions` not specified, "
"will create a new table without partitions if the table does not exist, "
"and insert the SQL result into it. If `partitions` are specified, they "
"will be the partition fields of the new table. If `partition` is "
"specified, the data will be inserted into the exact partition of the "
"table."
msgstr ""

#: odps.df.expr.expressions.Expr.persist:7 of
msgid "table name"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:8 of
msgid "list of string, the partition fields"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:10 of
msgid "persist to a specified partition"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:12 of
msgid "table lifecycle. If absent, `options.lifecycle` will be used."
msgstr ""

#: odps.df.expr.expressions.Expr.persist:14 of
msgid "project name, if not provided, will be the default project"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:20 of
msgid "overwrite the table, True as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:22 of
msgid "drop table if exists, False as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:24 of
msgid "create table first if not exits, True as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:26 of
msgid "drop partition if exists, False as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:28 of
msgid "create partition if not exists, None as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:30 of
msgid "cast all columns' types as the existed table, False as default"
msgstr ""

#: odps.df.expr.expressions.Expr.persist:32 of
msgid ":class:`odps.df.DataFrame`"
msgstr ""

#: odps.df.expr.collections.pivot:1 of
msgid ""
"Produce ‘pivot’ table based on 3 columns of this DataFrame. Uses unique "
"values from rows / columns and fills with values."
msgstr ""

#: odps.df.expr.collections.pivot:5 of
msgid "use to make new collection's grouped rows"
msgstr ""

#: odps.df.expr.collections.pivot:6 of
msgid "use to make new collection's columns"
msgstr ""

#: odps.df.expr.collections.pivot:7 of
msgid "values to use for populating new collection's values"
msgstr ""

#: odps.df.expr.collections.pivot_table:1 of
msgid "Create a spreadsheet-style pivot table as a DataFrame."
msgstr ""

#: odps.df.expr.collections.pivot_table:4 of
msgid "column to aggregate"
msgstr ""

#: odps.df.expr.collections.pivot_table:5 of
msgid "rows to group"
msgstr ""

#: odps.df.expr.collections.pivot_table:6 of
msgid "keys to group by on the pivot table column"
msgstr ""

#: odps.df.expr.collections.pivot_table:7 of
msgid "aggregate function or functions"
msgstr ""

#: odps.df.expr.collections.pivot_table:8 of
msgid "value to replace missing value with, default None"
msgstr ""

#: odps.df.expr.reduction.quantile:1 of
msgid "Percentile value."
msgstr ""

#: odps.df.expr.reduction.quantile:4 of
msgid "probability or list of probabilities, in [0, 1]"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.query:1 of
msgid "Query the data with a boolean expression."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.query:3 of
msgid ""
"the query string, you can use '@' character refer to environment "
"variables."
msgstr ""

#: odps.df.expr.collections.reshuffle:1 of
msgid "Reshuffle data."
msgstr ""

#: odps.df.expr.collections.reshuffle:4 of
msgid "the sequence or scalar to shuffle by. RandomScalar as default"
msgstr ""

#: odps.df.expr.collections.reshuffle:5 of
msgid "the sequence or scalar to sort."
msgstr ""

#: odps.df.expr.merge.right_join:1 of
msgid "Right join two collections."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.roles:1 of
msgid "Set roles of fields"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.roles:3 of
msgid "Clear feature roles on fields"
msgstr ""

#: odps.df.expr.collections.sample:1 of
msgid "Sample collection."
msgstr ""

#: odps.df.expr.collections.sample:4 of
msgid "how many parts to hash"
msgstr ""

#: odps.df.expr.collections.sample:5 of
msgid "the columns to sample"
msgstr ""

#: odps.df.expr.collections.sample:6 of
msgid "the part to sample out, can be a list of parts, must be from 0 to parts-1"
msgstr ""

#: odps.df.expr.collections.sample:7 of
msgid ""
"how many rows to sample. If `strata` is specified, `n` should be a dict "
"with values in the strata column as dictionary keys and corresponding "
"sample size as values"
msgstr ""

#: odps.df.expr.collections.sample:8 of
msgid ""
"how many fraction to sample. If `strata` is specified, `n` should be a "
"dict with values in the strata column as dictionary keys and "
"corresponding sample weight as values"
msgstr ""

#: odps.df.expr.collections.sample:9 of
msgid "whether to perform replace sampling"
msgstr ""

#: odps.df.expr.collections.sample:10 of
msgid "the column name of weights"
msgstr ""

#: odps.df.expr.collections.sample:11 of
msgid "the name of strata column"
msgstr ""

#: odps.df.expr.collections.sample:12 of
msgid "the random seed when performing sampling"
msgstr ""

#: odps.df.expr.collections.sample:15 of
msgid ""
"Note that n, frac, replace, weights, strata and random_state can only be "
"used under Pandas DataFrames or XFlow."
msgstr ""

#: odps.df.expr.collections.sample:20 of
msgid "Sampling with parts:"
msgstr ""

#: odps.df.expr.collections.sample:26 of
msgid "Sampling with fraction or weights, replacement option can be specified:"
msgstr ""

#: odps.df.expr.collections.sample:32 of
msgid "Sampling with weight column:"
msgstr ""

#: odps.df.expr.collections.sample:37 of
msgid ""
"Stratified sampling. Note that currently we do not support stratified "
"sampling with replacement."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.select:1 of
msgid "Projection columns. Remember to avoid column names' conflict."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.select:3 of
msgid "columns to project"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.select:4 of
msgid "columns and their names to project"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.select_features:1 of
msgid "Select one or more fields as feature fields."
msgstr ""

#: odps.df.expr.reduction.skew:1 of
msgid "Calculate skewness of the sequence"
msgstr ""

#: odps.df.expr.collections.sort_values:1 of
msgid "Sort the collection by values. `sort` is an alias name for `sort_values`"
msgstr ""

#: odps.df.expr.collections.sort_values:4 of
msgid "the sequence or sequences to sort"
msgstr ""

#: odps.df.expr.collections.sort_values:5 of
msgid ""
"Sort ascending vs. descending. Sepecify list for multiple sort orders. If"
" this is a list of bools, must match the length of the by"
msgstr ""

#: odps.df.expr.collections.sort_values:7 of
msgid "Sorted collection"
msgstr ""

#: odps.df.expr.collections.split:1 of
msgid "Split the current column into two column objects with certain ratio."
msgstr ""

#: odps.df.expr.collections.split:3 of
msgid "Split ratio"
msgstr ""

#: odps.df.expr.collections.split:5 of
msgid "two split DataFrame objects"
msgstr ""

#: odps.df.expr.reduction.std:1 of
msgid "Standard deviation."
msgstr ""

#: odps.df.expr.collections.std_scale:1 of
msgid "Resize a data frame by mean and standard error."
msgstr ""

#: odps.df.expr.collections.std_scale:3 of
msgid "Input DataFrame"
msgstr ""

#: odps.df.expr.collections.std_scale:4 of
msgid "Determine whether the output will be subtracted by means"
msgstr ""

#: odps.df.expr.collections.std_scale:5 of
msgid "Determine whether the output will be divided by standard deviations"
msgstr ""

#: odps.df.expr.collections.std_scale:6 of
msgid ""
"Determine whether input data should be kept. If True, scaled input data "
"will be appended to the data frame with `suffix`"
msgstr ""

#: odps.df.expr.collections.std_scale:7 of
msgid ""
"Columns names to resize. If set to None, float or int-typed columns will "
"be normalized if the column is not specified as a group column."
msgstr ""

#: odps.df.expr.reduction.sum_:1 of
msgid "Sum value"
msgstr ""

#: odps.df.expr.element._switch:1 of
msgid "Similar to the case-when in SQL. Refer to the example below"
msgstr ""

#: odps.df.expr.element._between:4 odps.df.expr.element._cut:3
#: odps.df.expr.element._cut:11 odps.df.expr.element._fillna:3
#: odps.df.expr.element._fillna:5 odps.df.expr.element._int_to_datetime:3
#: odps.df.expr.element._int_to_datetime:4 odps.df.expr.element._isin:4
#: odps.df.expr.element._isna:3 odps.df.expr.element._isna:4
#: odps.df.expr.element._isnull:3 odps.df.expr.element._isnull:4
#: odps.df.expr.element._notin:4 odps.df.expr.element._notna:3
#: odps.df.expr.element._notna:4 odps.df.expr.element._notnull:3
#: odps.df.expr.element._notnull:4 odps.df.expr.element._switch:6
#: odps.df.expr.strings._capitalize:4 odps.df.expr.strings._contains:3
#: odps.df.expr.strings._contains:9 odps.df.expr.strings._endswith:6
#: odps.df.expr.strings._extract:7 odps.df.expr.strings._find:9
#: odps.df.expr.strings._get:5 odps.df.expr.strings._ljust:7
#: odps.df.expr.strings._lower:4 odps.df.expr.strings._pad:7
#: odps.df.expr.strings._repeat:5 odps.df.expr.strings._replace:10
#: odps.df.expr.strings._rfind:9 odps.df.expr.strings._rjust:7
#: odps.df.expr.strings._rstrip:6 odps.df.expr.strings._startswith:6
#: odps.df.expr.strings._strip:6 odps.df.expr.strings._upper:4
#: odps.df.tools.libtools.hll_count:7 of
msgid "sequence or scalar"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.tail:1
#: odps.df.expr.expressions.SequenceExpr.tail:1 of
msgid "Return the last n rows. Execute at once."
msgstr ""

#: odps.df.expr.collections.to_kv:1 of
msgid "Merge values in specified columns into a key-value represented column."
msgstr ""

#: odps.df.expr.collections.to_kv:4 of
msgid "the columns to be merged."
msgstr ""

#: odps.df.expr.collections.to_kv:7 of
msgid "name of the new key-value column"
msgstr ""

#: odps.df.expr.collections.to_kv:9 of
msgid "converted data frame"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.to_pandas:1 of
msgid "Convert to pandas DataFrame. Execute at once."
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.to_pandas:3
#: odps.df.expr.expressions.SequenceExpr.to_pandas:3 of
msgid "if True, wrap the pandas DataFrame into a PyODPS DataFrame"
msgstr ""

#: odps.df.expr.expressions.CollectionExpr.to_pandas:4 of
msgid "pandas DataFrame"
msgstr ""

#: odps.df.expr.reduction.tolist:1 of
msgid ""
"Pack all data in the sequence into a list :param expr: :param unique: "
"make every elements in the sequence to be unique :return:"
msgstr ""

#: odps.df.expr.merge.union:1 of
msgid "Union two collections."
msgstr ""

#: odps.df.expr.reduction.var:1 of
msgid "Variance"
msgstr ""

#: odps.df.expr.reduction.var:4 of
msgid "degree of freedom"
msgstr ""

#: odps.df.expr.expressions.Expr.verify:1 of
msgid "Verify if this expression can be compiled into ODPS SQL."
msgstr ""

#: odps.df.expr.expressions.Expr.verify:3 of
msgid "True if compilation succeed else False"
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.weight_field:1 of
msgid "Select one field as the weight field."
msgstr ""

#: odps.ml.expr.mixin.MLCollectionMixin.weight_field:5 of
msgid "Selected weight field"
msgstr ""

#: odps.df.expr.expressions.SequenceExpr:1 of
msgid "Sequence represents for 1-dimension data."
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.astype:1 of
msgid "Cast to a new data type."
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.astype:3 of
msgid "the new data type"
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.astype:4 of
msgid "casted sequence"
msgstr ""

#: odps.df.expr.element._between:1 of
msgid ""
"Return a boolean sequence or scalar show whether each element is between "
"`left` and `right`."
msgstr ""

#: odps.df.expr.element._between:5 of
msgid "left value"
msgstr ""

#: odps.df.expr.element._between:6 of
msgid "right value"
msgstr ""

#: odps.df.expr.element._between:7 of
msgid "if true, will be left <= expr <= right, else will be left < expr < right"
msgstr ""

#: odps.df.expr.element._between:8 odps.df.expr.element._isin:6
#: odps.df.expr.element._notin:6 odps.df.expr.strings._isalnum:5
#: odps.df.expr.strings._isalpha:5 odps.df.expr.strings._isdecimal:5
#: odps.df.expr.strings._isdigit:5 odps.df.expr.strings._islower:5
#: odps.df.expr.strings._isnumeric:6 odps.df.expr.strings._isspace:5
#: odps.df.expr.strings._istitle:5 odps.df.expr.strings._isupper:5 of
msgid "boolean sequence or scalar"
msgstr ""

#: odps.ml.expr.mixin.MLSequenceMixin.continuous:1 of
msgid "Set sequence to be continuous."
msgstr ""

#: odps.df.expr.element._cut:1 of
msgid "Return indices of half-open bins to which each value of `expr` belongs."
msgstr ""

#: odps.df.expr.element._cut:4 of
msgid "list of scalars"
msgstr ""

#: odps.df.expr.element._cut:5 of
msgid ""
"indicates whether the bins include the rightmost edge or not. If right =="
" True(the default), then the bins [1, 2, 3, 4] indicate (1, 2], (2, 3], "
"(3, 4]"
msgstr ""

#: odps.df.expr.element._cut:7 of
msgid ""
"Usesd as labes for the resulting bins. Must be of the same length as the "
"resulting bins."
msgstr ""

#: odps.df.expr.element._cut:8 of
msgid "Whether the first interval should be left-inclusive or not."
msgstr ""

#: odps.df.expr.element._cut:9 of
msgid "include the bin below the leftmost edge or not"
msgstr ""

#: odps.df.expr.element._cut:10 of
msgid "include the bin above the rightmost edge or not"
msgstr ""

#: odps.ml.expr.mixin.MLSequenceMixin.discrete:1 of
msgid "Set sequence to be discrete."
msgstr ""

#: odps.df.SequenceExpr.dtype:1 of
msgid ""
"Return the data type. Available types: int8, int16, int32, int64, "
"float32, float64, boolean, string, decimal, datetime"
msgstr ""

#: odps.df.SequenceExpr.dtype:4 of
msgid "the data type"
msgstr ""

#: odps.df.expr.element._fillna:1 of
msgid "Fill null with value."
msgstr ""

#: odps.df.expr.element._hash:1 of
msgid "Calculate the hash value."
msgstr ""

#: odps.df.expr.element._hash:4 of
msgid "hash function"
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.head:1 of
msgid "Return first n rows. Execute at once."
msgstr ""

#: odps.df.tools.libtools.hll_count:1 of
msgid "Calculate HyperLogLog count"
msgstr ""

#: odps.df.tools.libtools.hll_count:6 of
msgid "the splitter to split the column value"
msgstr ""

#: odps.df.expr.element._isin:1 of
msgid ""
"Return a boolean sequence or scalar showing whether each element is "
"exactly contained in the passed `values`."
msgstr ""

#: odps.df.expr.element._isin:5 odps.df.expr.element._notin:5 of
msgid "`list` object or sequence"
msgstr ""

#: odps.df.expr.element._isna:1 odps.df.expr.element._isnull:1 of
msgid ""
"Return a sequence or scalar according to the input indicating if the "
"values are null."
msgstr ""

#: odps.df.expr.element._map:1 of
msgid "Call func on each element of this sequence."
msgstr ""

#: odps.df.expr.element._map:6 of
msgid "a new sequence"
msgstr ""

#: odps.df.expr.element._notin:1 of
msgid ""
"Return a boolean sequence or scalar showing whether each element is not "
"contained in the passed `values`."
msgstr ""

#: odps.df.expr.element._notna:1 odps.df.expr.element._notnull:1 of
msgid ""
"Return a sequence or scalar according to the input indicating if the "
"values are not null."
msgstr ""

#: odps.ml.expr.mixin.MLSequenceMixin.role:1 of
msgid "Set role of current column"
msgstr ""

#: odps.ml.expr.mixin.MLSequenceMixin.role:3 of
msgid "name of the role to be selected."
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.to_pandas:1 of
msgid "Convert to pandas Series. Execute at once."
msgstr ""

#: odps.df.expr.expressions.SequenceExpr.to_pandas:4 of
msgid "pandas Series"
msgstr ""

#: odps.df.expr.groupby.value_counts:1 of
msgid "Return object containing counts of unique values."
msgstr ""

#: odps.df.expr.groupby.value_counts:3 of
msgid ""
"The resulting object will be in descending order so that the first "
"element is the most frequently-occuring element. Exclude NA values by "
"default"
msgstr ""

#: odps.df.expr.groupby.value_counts:6 of
msgid "sequence"
msgstr ""

#: odps.df.expr.groupby.value_counts:7 of
msgid "if sort"
msgstr ""

#: odps.df.expr.groupby.value_counts:9 of
msgid "Don’t include counts of None, default False"
msgstr ""

#: odps.df.expr.groupby.value_counts:10 of
msgid "collection with two columns"
msgstr ""

#: odps.df.expr.element._int_to_datetime:1 of
msgid ""
"Return a sequence or scalar that is the datetime value of the current "
"numeric sequence or scalar."
msgstr ""

#: odps.df.expr.strings._capitalize:1 of
msgid ""
"Convert strings in the Sequence or string scalar to be capitalized. "
"Equivalent to str.capitalize()."
msgstr ""

#: odps.df.expr.reduction.cat:1 of
msgid "Concatenate strings in sequence with given separator"
msgstr ""

#: odps.df.expr.reduction.cat:4 of
msgid "other sequences"
msgstr ""

#: odps.df.expr.reduction.cat:5 of
msgid "string or None, default None"
msgstr ""

#: odps.df.expr.reduction.cat:6 of
msgid "string or None default None, if None, NA in the sequence are ignored"
msgstr ""

#: odps.df.expr.strings._contains:1 of
msgid ""
"Return boolean sequence whether given pattern/regex is contained in each "
"string in the sequence"
msgstr ""

#: odps.df.expr.strings._contains:4 odps.df.expr.strings._replace:5 of
msgid "Character sequence or regular expression"
msgstr ""

#: odps.df.expr.strings._contains:5 of
msgid "If True, case sensitive"
msgstr ""

#: odps.df.expr.strings._contains:7 of
msgid "re module flags, e.g. re.IGNORECASE"
msgstr ""

#: odps.df.expr.strings._contains:8 of
msgid "If True use regex, otherwise use string finder"
msgstr ""

#: odps.df.expr.strings._endswith:1 of
msgid ""
"Return boolean sequence or scalar indicating whether each string in the "
"sequence or scalar ends with passed pattern. Equivalent to "
"str.endswith()."
msgstr ""

#: odps.df.expr.strings._endswith:5 odps.df.expr.strings._startswith:5 of
msgid "Character sequence"
msgstr ""

#: odps.df.expr.strings._extract:1 of
msgid "Find group in each string in the Series using passed regular expression."
msgstr ""

#: odps.df.expr.strings._extract:4 of
msgid "Pattern or regular expression"
msgstr ""

#: odps.df.expr.strings._extract:5 of
msgid "re module, e.g. re.IGNORECASE"
msgstr ""

#: odps.df.expr.strings._extract:6 of
msgid "if None as group 0"
msgstr ""

#: odps.df.expr.strings._find:1 of
msgid ""
"Return lowest indexes in each strings in the sequence or scalar where the"
" substring is fully contained between [start:end]. Return -1 on failure. "
"Equivalent to standard str.find()."
msgstr ""

#: odps.df.expr.strings._find:6 of
msgid "substring being searched"
msgstr ""

#: odps.df.expr.strings._find:7 of
msgid "left edge index"
msgstr ""

#: odps.df.expr.strings._find:8 of
msgid "right edge index"
msgstr ""

#: odps.df.expr.strings._get:1 of
msgid ""
"Extract element from lists, tuples, or strings in each element in the "
"sequence or scalar"
msgstr ""

#: odps.df.expr.strings._get:4 of
msgid "Integer index(location)"
msgstr ""

#: odps.df.expr.strings._isalnum:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" alphanumeric. Equivalent to str.isalnum()."
msgstr ""

#: odps.df.expr.strings._isalpha:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" alphabetic. Equivalent to str.isalpha()."
msgstr ""

#: odps.df.expr.strings._isdecimal:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" decimal. Equivalent to str.isdecimal()."
msgstr ""

#: odps.df.expr.strings._isdigit:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" digits. Equivalent to str.isdigit()."
msgstr ""

#: odps.df.expr.strings._islower:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" lowercase. Equivalent to str.islower()."
msgstr ""

#: odps.df.expr.strings._isnumeric:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" numeric. Equivalent to str.isnumeric()."
msgstr ""

#: odps.df.expr.strings._isspace:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" whitespace. Equivalent to str.isspace()."
msgstr ""

#: odps.df.expr.strings._istitle:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" titlecase. Equivalent to str.istitle()."
msgstr ""

#: odps.df.expr.strings._isupper:1 of
msgid ""
"Check whether all characters in each string in the sequence or scalar are"
" uppercase. Equivalent to str.isupper()."
msgstr ""

#: odps.df.expr.strings._len:1 of
msgid "Compute length of each string in the sequence or scalar"
msgstr ""

#: odps.df.expr.strings._len:4 of
msgid "lengths"
msgstr ""

#: odps.df.expr.strings._ljust:1 of
msgid ""
"Filling right side of strings in the sequence or scalar with an "
"additional character. Equivalent to str.ljust()."
msgstr ""

#: odps.df.expr.strings._ljust:5 odps.df.expr.strings._rjust:5 of
msgid ""
"Minimum width of resulting string; additional characters will be filled "
"with `fillchar`"
msgstr ""

#: odps.df.expr.strings._ljust:6 odps.df.expr.strings._rjust:6 of
msgid "Additional character for filling, default is whitespace."
msgstr ""

#: odps.df.expr.strings._lower:1 of
msgid ""
"Convert strings in the sequence or scalar lowercase. Equivalent to "
"str.lower()."
msgstr ""

#: odps.df.expr.strings._lstrip:1 of
msgid ""
"Strip whitespace (including newlines) from each string in the sequence or"
" scalar from left side. Equivalent to str.lstrip()."
msgstr ""

#: odps.df.expr.strings._lstrip:6 of
msgid "sequence or sclaar"
msgstr ""

#: odps.df.expr.strings._pad:1 of
msgid ""
"Pad strings in the sequence or scalar with an additional character to "
"specified side."
msgstr ""

#: odps.df.expr.strings._pad:4 of
msgid ""
"Minimum width of resulting string; additional characters will be filled "
"with spaces"
msgstr ""

#: odps.df.expr.strings._pad:5 of
msgid "{‘left’, ‘right’, ‘both’}, default ‘left’"
msgstr ""

#: odps.df.expr.strings._pad:6 of
msgid "Additional character for filling, default is whitespace"
msgstr ""

#: odps.df.expr.strings._repeat:1 of
msgid ""
"Duplicate each string in the sequence or scalar by indicated number of "
"times."
msgstr ""

#: odps.df.expr.strings._repeat:4 of
msgid "times"
msgstr ""

#: odps.df.expr.strings._replace:1 of
msgid ""
"Replace occurrence of pattern/regex in the sequence or scalar with some "
"other string. Equivalent to str.replace()"
msgstr ""

#: odps.df.expr.strings._replace:6 of
msgid "Replacement"
msgstr ""

#: odps.df.expr.strings._replace:7 of
msgid "Number of replacements to make from start"
msgstr ""

#: odps.df.expr.strings._replace:8 of
msgid "if True, case sensitive"
msgstr ""

#: odps.df.expr.strings._replace:9 of
msgid "re module flag, e.g. re.IGNORECASE"
msgstr ""

#: odps.df.expr.strings._rfind:1 of
msgid ""
"Return highest indexes in each strings in the sequence or scalar where "
"the substring is fully contained between [start:end]. Return -1 on "
"failure. Equivalent to standard str.rfind()."
msgstr ""

#: odps.df.expr.strings._rjust:1 of
msgid ""
"Filling left side of strings in the sequence or scalar with an additional"
" character. Equivalent to str.rjust()."
msgstr ""

#: odps.df.expr.strings._rstrip:1 of
msgid ""
"Strip whitespace (including newlines) from each string in the sequence or"
" scalar from right side. Equivalent to str.rstrip()."
msgstr ""

#: odps.df.expr.strings._slice:1 of
msgid "Slice substrings from each element in the sequence or scalar"
msgstr ""

#: odps.df.expr.strings._slice:4 odps.df.expr.strings._slice:5
#: odps.df.expr.strings._slice:6 of
msgid "int or None"
msgstr ""

#: odps.df.expr.strings._slice:7 of
msgid "sliced"
msgstr ""

#: odps.df.expr.strings._split:1 of
msgid ""
"Split each string (a la re.split) in the Series/Index by given pattern, "
"propagating NA values. Equivalent to str.split()."
msgstr ""

#: odps.df.expr.strings._split:5 of
msgid "Separator to split on. If None, splits on whitespace"
msgstr ""

#: odps.df.expr.strings._split:6 of
msgid "not supported right now"
msgstr ""

#: odps.df.expr.strings._split:7 of
msgid "list sequence or scalar"
msgstr ""

#: odps.df.expr.strings._startswith:1 of
msgid ""
"Return boolean sequence or scalar indicating whether each string in the "
"sequence or scalar starts with passed pattern. Equivalent to "
"str.startswith()."
msgstr ""

#: odps.df.expr.strings._strip:1 of
msgid ""
"Strip whitespace (including newlines) from each string in the sequence or"
" scalar from left and right sides. Equivalent to str.strip()."
msgstr ""

#: odps.df.expr.strings._strptime:1 of
msgid ""
"Return datetimes specified by date_format, which supports the same string"
" format as the python standard library. Details of the string format can "
"be found in python string format doc"
msgstr ""

#: odps.df.expr.strings._strptime:6 of
#, python-format
msgid "date format string (e.g. “%Y-%m-%d”)"
msgstr ""

#: odps.df.expr.strings._swapcase:1 of
msgid ""
"Convert strings in the sequence or scalar to be swapcased. Equivalent to "
"str.swapcase()."
msgstr ""

#: odps.df.expr.strings._swapcase:4 odps.df.expr.strings._title:5 of
msgid "converted"
msgstr ""

#: odps.df.expr.strings._title:1 of
msgid ""
"Convert strings in the sequence or scalar to titlecase. Equivalent to "
"str.title()."
msgstr ""

#: odps.df.expr.strings._todict:1 of
msgid ""
"Convert the string sequence / expr into a string dict given item and key-"
"value delimiters."
msgstr ""

#: odps.df.expr.strings._todict:4 of
msgid "delimiter between data items"
msgstr ""

#: odps.df.expr.strings._todict:5 of
msgid "delimiter between keys and values"
msgstr ""

#: odps.df.expr.strings._todict:6 of
msgid "dict sequence or scalar"
msgstr ""

#: odps.df.expr.strings._upper:1 of
msgid ""
"Convert strings in the sequence or scalar uppercase. Equivalent to "
"str.upper()."
msgstr ""

#: odps.df.expr.strings._zfill:1 of
msgid ""
"Filling left side of strings in the sequence or scalar with 0. Equivalent"
" to str.zfill()."
msgstr ""

#: odps.df.expr.strings._zfill:4 of
msgid ""
"Minimum width of resulting string; additional characters will be filled "
"with 0"
msgstr ""

#: odps.df.expr.strings._zfill:5 of
msgid "filled"
msgstr ""

#: odps.df.expr.expressions.Scalar:1 of
msgid "Represent for the scalar type."
msgstr ""

#: odps.df.expr.expressions.Scalar:3 of
msgid "value of the scalar"
msgstr ""

#: odps.df.expr.expressions.Scalar:4 of
msgid "value type of the scalar"
msgstr ""

#: odps.df.NullScalar:1 of
msgid "Creates a Scalar representing typed None values."
msgstr ""

#: odps.df.NullScalar:3 of
msgid "type of the scalar"
msgstr ""

#: odps.df.NullScalar:4 of
msgid "Scalar with None value"
msgstr ""

#: odps.df.expr.expressions.RandomScalar:1 of
msgid "Represent for the random scalar type."
msgstr ""

#: odps.df.expr.expressions.RandomScalar:3 of
msgid "random seed, None by default"
msgstr ""

#: odps.df.expr.window.cume_dist:1 of
msgid "Calculate cumulative ratio of a sequence expression."
msgstr ""

#: odps.df.expr.window.cumcount:3 odps.df.expr.window.cume_dist:3
#: odps.df.expr.window.cummax:3 odps.df.expr.window.cummean:3
#: odps.df.expr.window.cummedian:3 odps.df.expr.window.cummin:3
#: odps.df.expr.window.cumstd:3 odps.df.expr.window.cumsum:3
#: odps.df.expr.window.dense_rank:3 odps.df.expr.window.lag:5
#: odps.df.expr.window.lead:5 odps.df.expr.window.nth_value:3
#: odps.df.expr.window.percent_rank:3 odps.df.expr.window.qcut:5
#: odps.df.expr.window.rank:3 odps.df.expr.window.row_number:3 of
msgid "expression for calculation"
msgstr ""

#: odps.df.expr.window.cumcount:4 odps.df.expr.window.cume_dist:4
#: odps.df.expr.window.cummax:4 odps.df.expr.window.cummean:4
#: odps.df.expr.window.cummedian:4 odps.df.expr.window.cummin:4
#: odps.df.expr.window.cumstd:4 odps.df.expr.window.cumsum:4
#: odps.df.expr.window.dense_rank:4 odps.df.expr.window.lag:6
#: odps.df.expr.window.lead:6 odps.df.expr.window.nth_value:6
#: odps.df.expr.window.percent_rank:4 odps.df.expr.window.qcut:7
#: odps.df.expr.window.rank:4 odps.df.expr.window.row_number:4 of
msgid "name of the sort column"
msgstr ""

#: odps.df.expr.window.cumcount:5 odps.df.expr.window.cume_dist:5
#: odps.df.expr.window.cummax:5 odps.df.expr.window.cummean:5
#: odps.df.expr.window.cummedian:5 odps.df.expr.window.cummin:5
#: odps.df.expr.window.cumstd:5 odps.df.expr.window.cumsum:5
#: odps.df.expr.window.dense_rank:5 odps.df.expr.window.lag:7
#: odps.df.expr.window.lead:7 odps.df.expr.window.nth_value:7
#: odps.df.expr.window.percent_rank:5 odps.df.expr.window.qcut:8
#: odps.df.expr.window.rank:5 odps.df.expr.window.row_number:5 of
msgid "whether to sort in ascending order"
msgstr ""

#: odps.df.expr.window.cumcount:9 odps.df.expr.window.cume_dist:6
#: odps.df.expr.window.cummax:9 odps.df.expr.window.cummean:9
#: odps.df.expr.window.cummedian:9 odps.df.expr.window.cummin:9
#: odps.df.expr.window.cumstd:9 odps.df.expr.window.cumsum:9
#: odps.df.expr.window.dense_rank:6 odps.df.expr.window.lag:8
#: odps.df.expr.window.lead:8 odps.df.expr.window.nth_value:8
#: odps.df.expr.window.percent_rank:6 odps.df.expr.window.qcut:9
#: odps.df.expr.window.rank:6 odps.df.expr.window.row_number:6 of
msgid "calculated column"
msgstr ""

#: odps.df.expr.window.dense_rank:1 of
msgid "Calculate dense rank of a sequence expression."
msgstr ""

#: odps.df.expr.window.rank:1 of
msgid "Calculate rank of a sequence expression."
msgstr ""

#: odps.df.expr.window.nth_value:1 of
msgid "Get nth value of a grouped and sorted expression."
msgstr ""

#: odps.df.expr.window.nth_value:4 of
msgid "integer position"
msgstr ""

#: odps.df.expr.window.nth_value:5 of
msgid "whether to skip null values, False by default"
msgstr ""

#: odps.df.expr.window.percent_rank:1 of
msgid "Calculate percentage rank of a sequence expression."
msgstr ""

#: odps.df.expr.window.qcut:1 of
msgid ""
"Get quantile-based bin indices of every element of a grouped and sorted "
"expression. The indices of bins start from 0. If cuts are not of equal "
"sizes, extra items will be appended into the first group."
msgstr ""

#: odps.df.expr.window.qcut:6 of
msgid "number of bins"
msgstr ""

#: odps.df.expr.window.row_number:1 of
msgid "Calculate row number of a sequence expression."
msgstr ""

#: odps.df.expr.window.cumcount:1 of
msgid "Calculate cumulative count of a sequence expression."
msgstr ""

#: odps.df.expr.window.cumcount:6 odps.df.expr.window.cummax:6
#: odps.df.expr.window.cummean:6 odps.df.expr.window.cummedian:6
#: odps.df.expr.window.cummin:6 odps.df.expr.window.cumstd:6
#: odps.df.expr.window.cumsum:6 of
msgid "whether to eliminate duplicate entries"
msgstr ""

#: odps.df.expr.window.cumcount:7 odps.df.expr.window.cummax:7
#: odps.df.expr.window.cummean:7 odps.df.expr.window.cummedian:7
#: odps.df.expr.window.cummin:7 odps.df.expr.window.cumstd:7
#: odps.df.expr.window.cumsum:7 of
msgid "the start point of a window"
msgstr ""

#: odps.df.expr.window.cumcount:8 odps.df.expr.window.cummax:8
#: odps.df.expr.window.cummean:8 odps.df.expr.window.cummedian:8
#: odps.df.expr.window.cummin:8 odps.df.expr.window.cumstd:8
#: odps.df.expr.window.cumsum:8 of
msgid "the end point of a window"
msgstr ""

#: odps.df.expr.window.cummax:1 of
msgid "Calculate cumulative maximum of a sequence expression."
msgstr ""

#: odps.df.expr.window.cummin:1 of
msgid "Calculate cumulative minimum of a sequence expression."
msgstr ""

#: odps.df.expr.window.lag:1 of
msgid "Get value in the row ``offset`` rows prior to the current row."
msgstr ""

#: odps.df.expr.window.lag:3 odps.df.expr.window.lead:3 of
msgid "the offset value"
msgstr ""

#: odps.df.expr.window.lag:4 odps.df.expr.window.lead:4 of
msgid ""
"default value for the function, when there are no rows satisfying the "
"offset"
msgstr ""

#: odps.df.expr.window.lead:1 of
msgid "Get value in the row ``offset`` rows after to the current row."
msgstr ""

#: odps.df.expr.window.cummean:1 of
msgid "Calculate cumulative mean of a sequence expression."
msgstr ""

#: odps.df.expr.window.cummedian:1 of
msgid "Calculate cumulative median of a sequence expression."
msgstr ""

#: odps.df.expr.window.cumstd:1 of
msgid "Calculate cumulative standard deviation of a sequence expression."
msgstr ""

#: odps.df.expr.window.cumsum:1 of
msgid "Calculate cumulative summation of a sequence expression."
msgstr ""

#~ msgid "DataFrame Reference"
#~ msgstr ""

