#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Copyright 1999-2022 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from sqlalchemy.ext.compiler import compiles

from ..sqlalchemy.ext import SACreateTempTableAs


class CreateTempTableAs(SACreateTempTableAs):

    def __init__(self, name, query, expr):
        super(CreateTempTableAs, self).__init__(name, query)
        self.expr = expr


@compiles(CreateTempTableAs)
def create_temp_table_as(element, compiler, **kw):
    return 'CREATE TEMP TABLE %s WITH(appendonly=true, orientation=heap, bucketnum=1) ' \
           'AS %s DISTRIBUTED BY (%s)' % (
        element.name,
        compiler.process(element.query),
        ', '.join(element.expr.schema.names)  # we just distribute by all columns
    )
