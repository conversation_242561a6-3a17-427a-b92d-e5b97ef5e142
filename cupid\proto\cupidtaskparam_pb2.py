# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cupidtaskparam.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='cupidtaskparam.proto',
  package='apsara.odps.cupid.protocol',
  serialized_pb=_b('\n\x14\x63upidtaskparam.proto\x12\x1a\x61psara.odps.cupid.protocol\")\n\x0bJobConfItem\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\"G\n\x07JobConf\x12<\n\x0bjobconfitem\x18\x01 \x03(\x0b\x32\'.apsara.odps.cupid.protocol.JobConfItem\"P\n\x11\x43upidTaskOperator\x12\x11\n\tmoperator\x18\x01 \x02(\t\x12\x13\n\x0bmlookupName\x18\x02 \x01(\t\x12\x13\n\x0bmenginetype\x18\x03 \x01(\t\"\xd9\x01\n\x15MultiTablesInputInfos\x12\x11\n\tsplitSize\x18\x01 \x01(\x05\x12\x12\n\nsplitCount\x18\x02 \x01(\x05\x12V\n\x18multiTablesInputInfoItem\x18\x03 \x03(\x0b\x32\x34.apsara.odps.cupid.protocol.MultiTablesInputInfoItem\x12\x14\n\x0csplitTempDir\x18\x04 \x01(\t\x12\x1a\n\x12require_split_info\x18\x05 \x01(\x08\x12\x0f\n\x07inputId\x18\x06 \x01(\x05\"n\n\x18MultiTablesInputInfoItem\x12\x10\n\x08projName\x18\x01 \x02(\t\x12\x0f\n\x07tblName\x18\x02 \x02(\t\x12\x0c\n\x04\x63ols\x18\x03 \x01(\t\x12\x0e\n\x06schema\x18\x04 \x01(\t\x12\x11\n\tpartSpecs\x18\x05 \x01(\t\"\xb7\x01\n\x11PartitionSizeInfo\x12\x10\n\x08projName\x18\x01 \x02(\t\x12\x0f\n\x07tblName\x18\x02 \x02(\t\x12\x0c\n\x04\x63ols\x18\x03 \x03(\t\x12\x0e\n\x06schema\x18\x04 \x01(\t\x12\x11\n\tsplitSize\x18\x05 \x01(\x05\x12\x12\n\nsplitCount\x18\x06 \x01(\x05\x12\x11\n\tpartSpecs\x18\x07 \x03(\t\x12\x11\n\todpsRdtId\x18\x08 \x01(\x05\x12\x14\n\x0csplitTempDir\x18\t \x01(\t\"E\n\x13\x43upidSetInformation\x12\x12\n\ninstanceId\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"U\n\x16\x43upidProxyTokenRequest\x12\x12\n\ninstanceId\x18\x01 \x01(\t\x12\x0f\n\x07\x61ppName\x18\x02 \x01(\t\x12\x16\n\x0e\x65xpiredInHours\x18\x03 \x01(\x05\"C\n\x16GetApplicationMetaInfo\x12\x15\n\rapplicationId\x18\x01 \x01(\t\x12\x12\n\ninstanceId\x18\x02 \x01(\t\"\x8c\x01\n\x19UpdateApplicationMetaInfo\x12\x15\n\rapplicationId\x18\x01 \x01(\t\x12\x44\n\x0f\x61pplicationMeta\x18\x02 \x01(\x0b\x32+.apsara.odps.cupid.protocol.ApplicationMeta\x12\x12\n\ninstanceId\x18\x03 \x01(\t\"R\n\x17ListApplicationMetaInfo\x12\x18\n\x10\x61pplicationTypes\x18\x01 \x01(\t\x12\x1d\n\x15yarnApplicationStates\x18\x02 \x01(\t\"\xa6\x01\n\x19\x43reateApplicationMetaInfo\x12\x15\n\rapplicationId\x18\x01 \x01(\t\x12\x12\n\ninstanceId\x18\x02 \x01(\t\x12\x17\n\x0f\x61pplicationTags\x18\x03 \x01(\t\x12\x17\n\x0f\x61pplicationType\x18\x04 \x01(\t\x12\x13\n\x0brunningMode\x18\x05 \x01(\t\x12\x17\n\x0f\x61pplicationName\x18\x06 \x01(\t\"0\n\x16GenVolumePanguPathInfo\x12\x16\n\x0eodpsvolumepath\x18\x01 \x01(\t\"~\n\tSplitItem\x12\x13\n\x0bpartitionid\x18\x01 \x01(\r\x12\x16\n\x0esplitfilestart\x18\x02 \x01(\x04\x12\x14\n\x0csplitfileend\x18\x03 \x01(\x04\x12\x17\n\x0fschemafilestart\x18\x04 \x01(\x04\x12\x15\n\rschemafileend\x18\x05 \x01(\x04\"w\n\x16GetPartitionSizeResult\x12\x0c\n\x04size\x18\x01 \x01(\x03\x12\x14\n\x0csplitTempDir\x18\x02 \x01(\t\x12\x39\n\nsplit_item\x18\x03 \x03(\x0b\x32%.apsara.odps.cupid.protocol.SplitItem\"0\n\nCommitFile\x12\x10\n\x08\x66ileName\x18\x01 \x01(\t\x12\x10\n\x08\x62ucketId\x18\x02 \x01(\x05\"M\n\x0e\x43ommitFileList\x12;\n\x0b\x66ileEntries\x18\x01 \x03(\x0b\x32&.apsara.odps.cupid.protocol.CommitFile\"\xc9\x01\n\x15\x44\x44LMultiTableInfoItem\x12\x15\n\rsaveTableName\x18\x01 \x02(\t\x12\x18\n\x10saveTableProject\x18\x02 \x02(\t\x12\x1a\n\x12panguTempDirSuffix\x18\x03 \x01(\t\x12\x10\n\x08partSpec\x18\x04 \x01(\t\x12\x13\n\x0bisOverWrite\x18\x05 \x01(\x08\x12<\n\x08\x66ileList\x18\x06 \x01(\x0b\x32*.apsara.odps.cupid.protocol.CommitFileList\"g\n\x12\x44\x44LMultiTableInfos\x12Q\n\x16\x64\x64lMultiTableInfoItems\x18\x01 \x03(\x0b\x32\x31.apsara.odps.cupid.protocol.DDLMultiTableInfoItem\"\x90\x01\n\x07\x44\x44LInfo\x12\x15\n\rsaveTableName\x18\x01 \x02(\t\x12\x18\n\x10saveTableProject\x18\x02 \x02(\t\x12\x13\n\x0bisOverWrite\x18\x03 \x01(\x08\x12?\n\rddlInfoIterms\x18\x04 \x03(\x0b\x32(.apsara.odps.cupid.protocol.DDLInfoIterm\":\n\x0c\x44\x44LInfoIterm\x12\x10\n\x08partSpec\x18\x01 \x02(\t\x12\x18\n\x10panguTempDirPath\x18\x02 \x02(\t\"G\n\rSaveTableInfo\x12\x13\n\x0bprojectname\x18\x01 \x02(\t\x12\x0e\n\x06saveid\x18\x02 \x02(\t\x12\x11\n\ttablename\x18\x03 \x01(\t\":\n\x12SubApplicationMeta\x12\x0f\n\x07\x61ppName\x18\x01 \x01(\t\x12\x13\n\x0btrackingUrl\x18\x02 \x01(\t\"\xa2\x03\n\x0f\x41pplicationMeta\x12\x0f\n\x07project\x18\x01 \x01(\t\x12\x12\n\ninstanceId\x18\x02 \x01(\t\x12\x15\n\rapplicationId\x18\x03 \x01(\t\x12\x17\n\x0f\x61pplicationTags\x18\x04 \x01(\t\x12\x13\n\x0brunningMode\x18\x05 \x01(\t\x12\x17\n\x0f\x61pplicationType\x18\x06 \x01(\t\x12\x1c\n\x14yarnApplicationState\x18\x07 \x01(\x03\x12\x1e\n\x16\x66inalApplicationStatus\x18\x08 \x01(\x03\x12\x1b\n\x13originalTrackingUrl\x18\t \x01(\t\x12\x13\n\x0btrackingUrl\x18\n \x01(\t\x12\x13\n\x0b\x64iagnostics\x18\x0b \x01(\t\x12\x17\n\x0f\x61pplicationName\x18\x0c \x01(\t\x12\x13\n\x0bstartedTime\x18\r \x01(\x04\x12\x14\n\x0c\x66inishedTime\x18\x0e \x01(\x04\x12\x43\n\x0bsubAppMetas\x18\x0f \x03(\x0b\x32..apsara.odps.cupid.protocol.SubApplicationMeta\"@\n\x12TaskServiceRequest\x12\x12\n\nmethodName\x18\x01 \x01(\t\x12\x16\n\x0erequestInBytes\x18\x02 \x01(\x0c\"_\n\x13\x41pplicationMetaList\x12H\n\x13\x61pplicationMetaList\x18\x01 \x03(\x0b\x32+.apsara.odps.cupid.protocol.ApplicationMeta\"\xd7\t\n\x0e\x43upidTaskParam\x12I\n\x12mcupidtaskoperator\x18\x01 \x02(\x0b\x32-.apsara.odps.cupid.protocol.CupidTaskOperator\x12\x34\n\x07jobconf\x18\x02 \x01(\x0b\x32#.apsara.odps.cupid.protocol.JobConf\x12\x44\n\rlocalresource\x18\x03 \x01(\x0b\x32-.apsara.odps.cupid.protocol.OdpsLocalResource\x12H\n\x11partitionsizeinfo\x18\x04 \x01(\x0b\x32-.apsara.odps.cupid.protocol.PartitionSizeInfo\x12\x34\n\x07\x64\x64lInfo\x18\x05 \x01(\x0b\x32#.apsara.odps.cupid.protocol.DDLInfo\x12@\n\rsaveTableInfo\x18\x06 \x01(\x0b\x32).apsara.odps.cupid.protocol.SaveTableInfo\x12R\n\x16genVolumePanguPathInfo\x18\x07 \x01(\x0b\x32\x32.apsara.odps.cupid.protocol.GenVolumePanguPathInfo\x12J\n\x12\x64\x64lMultiTableInfos\x18\x08 \x01(\x0b\x32..apsara.odps.cupid.protocol.DDLMultiTableInfos\x12P\n\x15multiTablesInputInfos\x18\t \x01(\x0b\x32\x31.apsara.odps.cupid.protocol.MultiTablesInputInfos\x12R\n\x16getApplicationMetaInfo\x18\n \x01(\x0b\x32\x32.apsara.odps.cupid.protocol.GetApplicationMetaInfo\x12X\n\x19\x63reateApplicationMetaInfo\x18\x0b \x01(\x0b\x32\x35.apsara.odps.cupid.protocol.CreateApplicationMetaInfo\x12T\n\x17listApplicationMetaInfo\x18\x0c \x01(\x0b\x32\x33.apsara.odps.cupid.protocol.ListApplicationMetaInfo\x12X\n\x19updateApplicationMetaInfo\x18\r \x01(\x0b\x32\x35.apsara.odps.cupid.protocol.UpdateApplicationMetaInfo\x12L\n\x13\x63upidSetInformation\x18\x0e \x01(\x0b\x32/.apsara.odps.cupid.protocol.CupidSetInformation\x12R\n\x16\x63upidProxyTokenRequest\x18\x0f \x01(\x0b\x32\x32.apsara.odps.cupid.protocol.CupidProxyTokenRequest\x12J\n\x12taskServiceRequest\x18\x10 \x01(\x0b\x32..apsara.odps.cupid.protocol.TaskServiceRequest\"\x07\n\x05Ready\"\x1d\n\x07Running\x12\x12\n\nrunningMsg\x18\x01 \x02(\t\"\x1d\n\x07Success\x12\x12\n\nsuccessMsg\x18\x01 \x02(\t\"!\n\tBizFailed\x12\x14\n\x0c\x62izFailedMsg\x18\x01 \x02(\t\"-\n\x0f\x43upidTaskFailed\x12\x1a\n\x12\x63upidTaskFailedMsg\x18\x01 \x02(\t\"\x88\x01\n\x06\x46\x61iled\x12\x38\n\tbizFailed\x18\x01 \x01(\x0b\x32%.apsara.odps.cupid.protocol.BizFailed\x12\x44\n\x0f\x63upidTaskFailed\x18\x02 \x01(\x0b\x32+.apsara.odps.cupid.protocol.CupidTaskFailed\"\x0b\n\tCancelled\"\t\n\x07Waiting\"\x1f\n\x0cWaitForReRun\x12\x0f\n\x07waitMsg\x18\x01 \x01(\t\"\x9e\x03\n\x1a\x43upidTaskDetailResultParam\x12\x30\n\x05ready\x18\x01 \x01(\x0b\x32!.apsara.odps.cupid.protocol.Ready\x12\x34\n\x07waiting\x18\x02 \x01(\x0b\x32#.apsara.odps.cupid.protocol.Waiting\x12\x34\n\x07running\x18\x03 \x01(\x0b\x32#.apsara.odps.cupid.protocol.Running\x12\x34\n\x07success\x18\x04 \x01(\x0b\x32#.apsara.odps.cupid.protocol.Success\x12\x32\n\x06\x66\x61iled\x18\x05 \x01(\x0b\x32\".apsara.odps.cupid.protocol.Failed\x12\x38\n\tcancelled\x18\x06 \x01(\x0b\x32%.apsara.odps.cupid.protocol.Cancelled\x12>\n\x0cwaitForReRun\x18\x07 \x01(\x0b\x32(.apsara.odps.cupid.protocol.WaitForReRun\"\x83\x01\n\x15OdpsLocalResourceItem\x12\x13\n\x0bprojectname\x18\x01 \x01(\t\x12\x18\n\x10relativefilepath\x18\x02 \x02(\t\x12;\n\x04type\x18\x03 \x02(\x0e\x32-.apsara.odps.cupid.protocol.LocalResourceType\"a\n\x11OdpsLocalResource\x12L\n\x11localresourceitem\x18\x01 \x03(\x0b\x32\x31.apsara.odps.cupid.protocol.OdpsLocalResourceItem*?\n\x11LocalResourceType\x12\x10\n\x0cTempResource\x10\x01\x12\n\n\x06Volume\x10\x02\x12\x0c\n\x08Resource\x10\x03\x42\x16\x42\x14\x43upidTaskParamProtos')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

_LOCALRESOURCETYPE = _descriptor.EnumDescriptor(
  name='LocalResourceType',
  full_name='apsara.odps.cupid.protocol.LocalResourceType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TempResource', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Volume', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Resource', index=2, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5293,
  serialized_end=5356,
)
_sym_db.RegisterEnumDescriptor(_LOCALRESOURCETYPE)

LocalResourceType = enum_type_wrapper.EnumTypeWrapper(_LOCALRESOURCETYPE)
TempResource = 1
Volume = 2
Resource = 3



_JOBCONFITEM = _descriptor.Descriptor(
  name='JobConfItem',
  full_name='apsara.odps.cupid.protocol.JobConfItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.JobConfItem.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.JobConfItem.value', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=52,
  serialized_end=93,
)


_JOBCONF = _descriptor.Descriptor(
  name='JobConf',
  full_name='apsara.odps.cupid.protocol.JobConf',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobconfitem', full_name='apsara.odps.cupid.protocol.JobConf.jobconfitem', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=166,
)


_CUPIDTASKOPERATOR = _descriptor.Descriptor(
  name='CupidTaskOperator',
  full_name='apsara.odps.cupid.protocol.CupidTaskOperator',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='moperator', full_name='apsara.odps.cupid.protocol.CupidTaskOperator.moperator', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='mlookupName', full_name='apsara.odps.cupid.protocol.CupidTaskOperator.mlookupName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='menginetype', full_name='apsara.odps.cupid.protocol.CupidTaskOperator.menginetype', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=168,
  serialized_end=248,
)


_MULTITABLESINPUTINFOS = _descriptor.Descriptor(
  name='MultiTablesInputInfos',
  full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='splitSize', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.splitSize', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitCount', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.splitCount', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='multiTablesInputInfoItem', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.multiTablesInputInfoItem', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitTempDir', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.splitTempDir', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='require_split_info', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.require_split_info', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='inputId', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfos.inputId', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=251,
  serialized_end=468,
)


_MULTITABLESINPUTINFOITEM = _descriptor.Descriptor(
  name='MultiTablesInputInfoItem',
  full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projName', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem.projName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tblName', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem.tblName', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cols', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem.cols', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem.schema', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpecs', full_name='apsara.odps.cupid.protocol.MultiTablesInputInfoItem.partSpecs', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=470,
  serialized_end=580,
)


_PARTITIONSIZEINFO = _descriptor.Descriptor(
  name='PartitionSizeInfo',
  full_name='apsara.odps.cupid.protocol.PartitionSizeInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projName', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.projName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tblName', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.tblName', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cols', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.cols', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schema', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.schema', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitSize', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.splitSize', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitCount', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.splitCount', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpecs', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.partSpecs', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='odpsRdtId', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.odpsRdtId', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitTempDir', full_name='apsara.odps.cupid.protocol.PartitionSizeInfo.splitTempDir', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=583,
  serialized_end=766,
)


_CUPIDSETINFORMATION = _descriptor.Descriptor(
  name='CupidSetInformation',
  full_name='apsara.odps.cupid.protocol.CupidSetInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.CupidSetInformation.instanceId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key', full_name='apsara.odps.cupid.protocol.CupidSetInformation.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='apsara.odps.cupid.protocol.CupidSetInformation.value', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=768,
  serialized_end=837,
)


_CUPIDPROXYTOKENREQUEST = _descriptor.Descriptor(
  name='CupidProxyTokenRequest',
  full_name='apsara.odps.cupid.protocol.CupidProxyTokenRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.CupidProxyTokenRequest.instanceId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='appName', full_name='apsara.odps.cupid.protocol.CupidProxyTokenRequest.appName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='expiredInHours', full_name='apsara.odps.cupid.protocol.CupidProxyTokenRequest.expiredInHours', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=839,
  serialized_end=924,
)


_GETAPPLICATIONMETAINFO = _descriptor.Descriptor(
  name='GetApplicationMetaInfo',
  full_name='apsara.odps.cupid.protocol.GetApplicationMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applicationId', full_name='apsara.odps.cupid.protocol.GetApplicationMetaInfo.applicationId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.GetApplicationMetaInfo.instanceId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=926,
  serialized_end=993,
)


_UPDATEAPPLICATIONMETAINFO = _descriptor.Descriptor(
  name='UpdateApplicationMetaInfo',
  full_name='apsara.odps.cupid.protocol.UpdateApplicationMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applicationId', full_name='apsara.odps.cupid.protocol.UpdateApplicationMetaInfo.applicationId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationMeta', full_name='apsara.odps.cupid.protocol.UpdateApplicationMetaInfo.applicationMeta', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.UpdateApplicationMetaInfo.instanceId', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=996,
  serialized_end=1136,
)


_LISTAPPLICATIONMETAINFO = _descriptor.Descriptor(
  name='ListApplicationMetaInfo',
  full_name='apsara.odps.cupid.protocol.ListApplicationMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applicationTypes', full_name='apsara.odps.cupid.protocol.ListApplicationMetaInfo.applicationTypes', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='yarnApplicationStates', full_name='apsara.odps.cupid.protocol.ListApplicationMetaInfo.yarnApplicationStates', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1138,
  serialized_end=1220,
)


_CREATEAPPLICATIONMETAINFO = _descriptor.Descriptor(
  name='CreateApplicationMetaInfo',
  full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applicationId', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.applicationId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.instanceId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationTags', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.applicationTags', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationType', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.applicationType', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='runningMode', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.runningMode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationName', full_name='apsara.odps.cupid.protocol.CreateApplicationMetaInfo.applicationName', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1223,
  serialized_end=1389,
)


_GENVOLUMEPANGUPATHINFO = _descriptor.Descriptor(
  name='GenVolumePanguPathInfo',
  full_name='apsara.odps.cupid.protocol.GenVolumePanguPathInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='odpsvolumepath', full_name='apsara.odps.cupid.protocol.GenVolumePanguPathInfo.odpsvolumepath', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1391,
  serialized_end=1439,
)


_SPLITITEM = _descriptor.Descriptor(
  name='SplitItem',
  full_name='apsara.odps.cupid.protocol.SplitItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partitionid', full_name='apsara.odps.cupid.protocol.SplitItem.partitionid', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitfilestart', full_name='apsara.odps.cupid.protocol.SplitItem.splitfilestart', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitfileend', full_name='apsara.odps.cupid.protocol.SplitItem.splitfileend', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schemafilestart', full_name='apsara.odps.cupid.protocol.SplitItem.schemafilestart', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='schemafileend', full_name='apsara.odps.cupid.protocol.SplitItem.schemafileend', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1441,
  serialized_end=1567,
)


_GETPARTITIONSIZERESULT = _descriptor.Descriptor(
  name='GetPartitionSizeResult',
  full_name='apsara.odps.cupid.protocol.GetPartitionSizeResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='size', full_name='apsara.odps.cupid.protocol.GetPartitionSizeResult.size', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='splitTempDir', full_name='apsara.odps.cupid.protocol.GetPartitionSizeResult.splitTempDir', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='split_item', full_name='apsara.odps.cupid.protocol.GetPartitionSizeResult.split_item', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1569,
  serialized_end=1688,
)


_COMMITFILE = _descriptor.Descriptor(
  name='CommitFile',
  full_name='apsara.odps.cupid.protocol.CommitFile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fileName', full_name='apsara.odps.cupid.protocol.CommitFile.fileName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='bucketId', full_name='apsara.odps.cupid.protocol.CommitFile.bucketId', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1690,
  serialized_end=1738,
)


_COMMITFILELIST = _descriptor.Descriptor(
  name='CommitFileList',
  full_name='apsara.odps.cupid.protocol.CommitFileList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fileEntries', full_name='apsara.odps.cupid.protocol.CommitFileList.fileEntries', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1740,
  serialized_end=1817,
)


_DDLMULTITABLEINFOITEM = _descriptor.Descriptor(
  name='DDLMultiTableInfoItem',
  full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='saveTableName', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.saveTableName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='saveTableProject', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.saveTableProject', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='panguTempDirSuffix', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.panguTempDirSuffix', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partSpec', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.partSpec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isOverWrite', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.isOverWrite', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fileList', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfoItem.fileList', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1820,
  serialized_end=2021,
)


_DDLMULTITABLEINFOS = _descriptor.Descriptor(
  name='DDLMultiTableInfos',
  full_name='apsara.odps.cupid.protocol.DDLMultiTableInfos',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ddlMultiTableInfoItems', full_name='apsara.odps.cupid.protocol.DDLMultiTableInfos.ddlMultiTableInfoItems', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2023,
  serialized_end=2126,
)


_DDLINFO = _descriptor.Descriptor(
  name='DDLInfo',
  full_name='apsara.odps.cupid.protocol.DDLInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='saveTableName', full_name='apsara.odps.cupid.protocol.DDLInfo.saveTableName', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='saveTableProject', full_name='apsara.odps.cupid.protocol.DDLInfo.saveTableProject', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isOverWrite', full_name='apsara.odps.cupid.protocol.DDLInfo.isOverWrite', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ddlInfoIterms', full_name='apsara.odps.cupid.protocol.DDLInfo.ddlInfoIterms', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2129,
  serialized_end=2273,
)


_DDLINFOITERM = _descriptor.Descriptor(
  name='DDLInfoIterm',
  full_name='apsara.odps.cupid.protocol.DDLInfoIterm',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partSpec', full_name='apsara.odps.cupid.protocol.DDLInfoIterm.partSpec', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='panguTempDirPath', full_name='apsara.odps.cupid.protocol.DDLInfoIterm.panguTempDirPath', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2275,
  serialized_end=2333,
)


_SAVETABLEINFO = _descriptor.Descriptor(
  name='SaveTableInfo',
  full_name='apsara.odps.cupid.protocol.SaveTableInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectname', full_name='apsara.odps.cupid.protocol.SaveTableInfo.projectname', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='saveid', full_name='apsara.odps.cupid.protocol.SaveTableInfo.saveid', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tablename', full_name='apsara.odps.cupid.protocol.SaveTableInfo.tablename', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2335,
  serialized_end=2406,
)


_SUBAPPLICATIONMETA = _descriptor.Descriptor(
  name='SubApplicationMeta',
  full_name='apsara.odps.cupid.protocol.SubApplicationMeta',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='appName', full_name='apsara.odps.cupid.protocol.SubApplicationMeta.appName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='trackingUrl', full_name='apsara.odps.cupid.protocol.SubApplicationMeta.trackingUrl', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2408,
  serialized_end=2466,
)


_APPLICATIONMETA = _descriptor.Descriptor(
  name='ApplicationMeta',
  full_name='apsara.odps.cupid.protocol.ApplicationMeta',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='project', full_name='apsara.odps.cupid.protocol.ApplicationMeta.project', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='instanceId', full_name='apsara.odps.cupid.protocol.ApplicationMeta.instanceId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationId', full_name='apsara.odps.cupid.protocol.ApplicationMeta.applicationId', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationTags', full_name='apsara.odps.cupid.protocol.ApplicationMeta.applicationTags', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='runningMode', full_name='apsara.odps.cupid.protocol.ApplicationMeta.runningMode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationType', full_name='apsara.odps.cupid.protocol.ApplicationMeta.applicationType', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='yarnApplicationState', full_name='apsara.odps.cupid.protocol.ApplicationMeta.yarnApplicationState', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='finalApplicationStatus', full_name='apsara.odps.cupid.protocol.ApplicationMeta.finalApplicationStatus', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='originalTrackingUrl', full_name='apsara.odps.cupid.protocol.ApplicationMeta.originalTrackingUrl', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='trackingUrl', full_name='apsara.odps.cupid.protocol.ApplicationMeta.trackingUrl', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='diagnostics', full_name='apsara.odps.cupid.protocol.ApplicationMeta.diagnostics', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='applicationName', full_name='apsara.odps.cupid.protocol.ApplicationMeta.applicationName', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='startedTime', full_name='apsara.odps.cupid.protocol.ApplicationMeta.startedTime', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='finishedTime', full_name='apsara.odps.cupid.protocol.ApplicationMeta.finishedTime', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='subAppMetas', full_name='apsara.odps.cupid.protocol.ApplicationMeta.subAppMetas', index=14,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2469,
  serialized_end=2887,
)


_TASKSERVICEREQUEST = _descriptor.Descriptor(
  name='TaskServiceRequest',
  full_name='apsara.odps.cupid.protocol.TaskServiceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='methodName', full_name='apsara.odps.cupid.protocol.TaskServiceRequest.methodName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='requestInBytes', full_name='apsara.odps.cupid.protocol.TaskServiceRequest.requestInBytes', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2889,
  serialized_end=2953,
)


_APPLICATIONMETALIST = _descriptor.Descriptor(
  name='ApplicationMetaList',
  full_name='apsara.odps.cupid.protocol.ApplicationMetaList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='applicationMetaList', full_name='apsara.odps.cupid.protocol.ApplicationMetaList.applicationMetaList', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2955,
  serialized_end=3050,
)


_CUPIDTASKPARAM = _descriptor.Descriptor(
  name='CupidTaskParam',
  full_name='apsara.odps.cupid.protocol.CupidTaskParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mcupidtaskoperator', full_name='apsara.odps.cupid.protocol.CupidTaskParam.mcupidtaskoperator', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='jobconf', full_name='apsara.odps.cupid.protocol.CupidTaskParam.jobconf', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='localresource', full_name='apsara.odps.cupid.protocol.CupidTaskParam.localresource', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partitionsizeinfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.partitionsizeinfo', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ddlInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.ddlInfo', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='saveTableInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.saveTableInfo', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='genVolumePanguPathInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.genVolumePanguPathInfo', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ddlMultiTableInfos', full_name='apsara.odps.cupid.protocol.CupidTaskParam.ddlMultiTableInfos', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='multiTablesInputInfos', full_name='apsara.odps.cupid.protocol.CupidTaskParam.multiTablesInputInfos', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='getApplicationMetaInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.getApplicationMetaInfo', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='createApplicationMetaInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.createApplicationMetaInfo', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='listApplicationMetaInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.listApplicationMetaInfo', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='updateApplicationMetaInfo', full_name='apsara.odps.cupid.protocol.CupidTaskParam.updateApplicationMetaInfo', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cupidSetInformation', full_name='apsara.odps.cupid.protocol.CupidTaskParam.cupidSetInformation', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cupidProxyTokenRequest', full_name='apsara.odps.cupid.protocol.CupidTaskParam.cupidProxyTokenRequest', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='taskServiceRequest', full_name='apsara.odps.cupid.protocol.CupidTaskParam.taskServiceRequest', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3053,
  serialized_end=4292,
)


_READY = _descriptor.Descriptor(
  name='Ready',
  full_name='apsara.odps.cupid.protocol.Ready',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4294,
  serialized_end=4301,
)


_RUNNING = _descriptor.Descriptor(
  name='Running',
  full_name='apsara.odps.cupid.protocol.Running',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runningMsg', full_name='apsara.odps.cupid.protocol.Running.runningMsg', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4303,
  serialized_end=4332,
)


_SUCCESS = _descriptor.Descriptor(
  name='Success',
  full_name='apsara.odps.cupid.protocol.Success',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='successMsg', full_name='apsara.odps.cupid.protocol.Success.successMsg', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4334,
  serialized_end=4363,
)


_BIZFAILED = _descriptor.Descriptor(
  name='BizFailed',
  full_name='apsara.odps.cupid.protocol.BizFailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bizFailedMsg', full_name='apsara.odps.cupid.protocol.BizFailed.bizFailedMsg', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4365,
  serialized_end=4398,
)


_CUPIDTASKFAILED = _descriptor.Descriptor(
  name='CupidTaskFailed',
  full_name='apsara.odps.cupid.protocol.CupidTaskFailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cupidTaskFailedMsg', full_name='apsara.odps.cupid.protocol.CupidTaskFailed.cupidTaskFailedMsg', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4400,
  serialized_end=4445,
)


_FAILED = _descriptor.Descriptor(
  name='Failed',
  full_name='apsara.odps.cupid.protocol.Failed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bizFailed', full_name='apsara.odps.cupid.protocol.Failed.bizFailed', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cupidTaskFailed', full_name='apsara.odps.cupid.protocol.Failed.cupidTaskFailed', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4448,
  serialized_end=4584,
)


_CANCELLED = _descriptor.Descriptor(
  name='Cancelled',
  full_name='apsara.odps.cupid.protocol.Cancelled',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4586,
  serialized_end=4597,
)


_WAITING = _descriptor.Descriptor(
  name='Waiting',
  full_name='apsara.odps.cupid.protocol.Waiting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4599,
  serialized_end=4608,
)


_WAITFORRERUN = _descriptor.Descriptor(
  name='WaitForReRun',
  full_name='apsara.odps.cupid.protocol.WaitForReRun',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='waitMsg', full_name='apsara.odps.cupid.protocol.WaitForReRun.waitMsg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4610,
  serialized_end=4641,
)


_CUPIDTASKDETAILRESULTPARAM = _descriptor.Descriptor(
  name='CupidTaskDetailResultParam',
  full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ready', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.ready', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='waiting', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.waiting', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='running', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.running', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='success', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.success', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='failed', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.failed', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cancelled', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.cancelled', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='waitForReRun', full_name='apsara.odps.cupid.protocol.CupidTaskDetailResultParam.waitForReRun', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4644,
  serialized_end=5058,
)


_ODPSLOCALRESOURCEITEM = _descriptor.Descriptor(
  name='OdpsLocalResourceItem',
  full_name='apsara.odps.cupid.protocol.OdpsLocalResourceItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='projectname', full_name='apsara.odps.cupid.protocol.OdpsLocalResourceItem.projectname', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='relativefilepath', full_name='apsara.odps.cupid.protocol.OdpsLocalResourceItem.relativefilepath', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='apsara.odps.cupid.protocol.OdpsLocalResourceItem.type', index=2,
      number=3, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5061,
  serialized_end=5192,
)


_ODPSLOCALRESOURCE = _descriptor.Descriptor(
  name='OdpsLocalResource',
  full_name='apsara.odps.cupid.protocol.OdpsLocalResource',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='localresourceitem', full_name='apsara.odps.cupid.protocol.OdpsLocalResource.localresourceitem', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5194,
  serialized_end=5291,
)

_JOBCONF.fields_by_name['jobconfitem'].message_type = _JOBCONFITEM
_MULTITABLESINPUTINFOS.fields_by_name['multiTablesInputInfoItem'].message_type = _MULTITABLESINPUTINFOITEM
_UPDATEAPPLICATIONMETAINFO.fields_by_name['applicationMeta'].message_type = _APPLICATIONMETA
_GETPARTITIONSIZERESULT.fields_by_name['split_item'].message_type = _SPLITITEM
_COMMITFILELIST.fields_by_name['fileEntries'].message_type = _COMMITFILE
_DDLMULTITABLEINFOITEM.fields_by_name['fileList'].message_type = _COMMITFILELIST
_DDLMULTITABLEINFOS.fields_by_name['ddlMultiTableInfoItems'].message_type = _DDLMULTITABLEINFOITEM
_DDLINFO.fields_by_name['ddlInfoIterms'].message_type = _DDLINFOITERM
_APPLICATIONMETA.fields_by_name['subAppMetas'].message_type = _SUBAPPLICATIONMETA
_APPLICATIONMETALIST.fields_by_name['applicationMetaList'].message_type = _APPLICATIONMETA
_CUPIDTASKPARAM.fields_by_name['mcupidtaskoperator'].message_type = _CUPIDTASKOPERATOR
_CUPIDTASKPARAM.fields_by_name['jobconf'].message_type = _JOBCONF
_CUPIDTASKPARAM.fields_by_name['localresource'].message_type = _ODPSLOCALRESOURCE
_CUPIDTASKPARAM.fields_by_name['partitionsizeinfo'].message_type = _PARTITIONSIZEINFO
_CUPIDTASKPARAM.fields_by_name['ddlInfo'].message_type = _DDLINFO
_CUPIDTASKPARAM.fields_by_name['saveTableInfo'].message_type = _SAVETABLEINFO
_CUPIDTASKPARAM.fields_by_name['genVolumePanguPathInfo'].message_type = _GENVOLUMEPANGUPATHINFO
_CUPIDTASKPARAM.fields_by_name['ddlMultiTableInfos'].message_type = _DDLMULTITABLEINFOS
_CUPIDTASKPARAM.fields_by_name['multiTablesInputInfos'].message_type = _MULTITABLESINPUTINFOS
_CUPIDTASKPARAM.fields_by_name['getApplicationMetaInfo'].message_type = _GETAPPLICATIONMETAINFO
_CUPIDTASKPARAM.fields_by_name['createApplicationMetaInfo'].message_type = _CREATEAPPLICATIONMETAINFO
_CUPIDTASKPARAM.fields_by_name['listApplicationMetaInfo'].message_type = _LISTAPPLICATIONMETAINFO
_CUPIDTASKPARAM.fields_by_name['updateApplicationMetaInfo'].message_type = _UPDATEAPPLICATIONMETAINFO
_CUPIDTASKPARAM.fields_by_name['cupidSetInformation'].message_type = _CUPIDSETINFORMATION
_CUPIDTASKPARAM.fields_by_name['cupidProxyTokenRequest'].message_type = _CUPIDPROXYTOKENREQUEST
_CUPIDTASKPARAM.fields_by_name['taskServiceRequest'].message_type = _TASKSERVICEREQUEST
_FAILED.fields_by_name['bizFailed'].message_type = _BIZFAILED
_FAILED.fields_by_name['cupidTaskFailed'].message_type = _CUPIDTASKFAILED
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['ready'].message_type = _READY
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['waiting'].message_type = _WAITING
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['running'].message_type = _RUNNING
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['success'].message_type = _SUCCESS
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['failed'].message_type = _FAILED
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['cancelled'].message_type = _CANCELLED
_CUPIDTASKDETAILRESULTPARAM.fields_by_name['waitForReRun'].message_type = _WAITFORRERUN
_ODPSLOCALRESOURCEITEM.fields_by_name['type'].enum_type = _LOCALRESOURCETYPE
_ODPSLOCALRESOURCE.fields_by_name['localresourceitem'].message_type = _ODPSLOCALRESOURCEITEM
DESCRIPTOR.message_types_by_name['JobConfItem'] = _JOBCONFITEM
DESCRIPTOR.message_types_by_name['JobConf'] = _JOBCONF
DESCRIPTOR.message_types_by_name['CupidTaskOperator'] = _CUPIDTASKOPERATOR
DESCRIPTOR.message_types_by_name['MultiTablesInputInfos'] = _MULTITABLESINPUTINFOS
DESCRIPTOR.message_types_by_name['MultiTablesInputInfoItem'] = _MULTITABLESINPUTINFOITEM
DESCRIPTOR.message_types_by_name['PartitionSizeInfo'] = _PARTITIONSIZEINFO
DESCRIPTOR.message_types_by_name['CupidSetInformation'] = _CUPIDSETINFORMATION
DESCRIPTOR.message_types_by_name['CupidProxyTokenRequest'] = _CUPIDPROXYTOKENREQUEST
DESCRIPTOR.message_types_by_name['GetApplicationMetaInfo'] = _GETAPPLICATIONMETAINFO
DESCRIPTOR.message_types_by_name['UpdateApplicationMetaInfo'] = _UPDATEAPPLICATIONMETAINFO
DESCRIPTOR.message_types_by_name['ListApplicationMetaInfo'] = _LISTAPPLICATIONMETAINFO
DESCRIPTOR.message_types_by_name['CreateApplicationMetaInfo'] = _CREATEAPPLICATIONMETAINFO
DESCRIPTOR.message_types_by_name['GenVolumePanguPathInfo'] = _GENVOLUMEPANGUPATHINFO
DESCRIPTOR.message_types_by_name['SplitItem'] = _SPLITITEM
DESCRIPTOR.message_types_by_name['GetPartitionSizeResult'] = _GETPARTITIONSIZERESULT
DESCRIPTOR.message_types_by_name['CommitFile'] = _COMMITFILE
DESCRIPTOR.message_types_by_name['CommitFileList'] = _COMMITFILELIST
DESCRIPTOR.message_types_by_name['DDLMultiTableInfoItem'] = _DDLMULTITABLEINFOITEM
DESCRIPTOR.message_types_by_name['DDLMultiTableInfos'] = _DDLMULTITABLEINFOS
DESCRIPTOR.message_types_by_name['DDLInfo'] = _DDLINFO
DESCRIPTOR.message_types_by_name['DDLInfoIterm'] = _DDLINFOITERM
DESCRIPTOR.message_types_by_name['SaveTableInfo'] = _SAVETABLEINFO
DESCRIPTOR.message_types_by_name['SubApplicationMeta'] = _SUBAPPLICATIONMETA
DESCRIPTOR.message_types_by_name['ApplicationMeta'] = _APPLICATIONMETA
DESCRIPTOR.message_types_by_name['TaskServiceRequest'] = _TASKSERVICEREQUEST
DESCRIPTOR.message_types_by_name['ApplicationMetaList'] = _APPLICATIONMETALIST
DESCRIPTOR.message_types_by_name['CupidTaskParam'] = _CUPIDTASKPARAM
DESCRIPTOR.message_types_by_name['Ready'] = _READY
DESCRIPTOR.message_types_by_name['Running'] = _RUNNING
DESCRIPTOR.message_types_by_name['Success'] = _SUCCESS
DESCRIPTOR.message_types_by_name['BizFailed'] = _BIZFAILED
DESCRIPTOR.message_types_by_name['CupidTaskFailed'] = _CUPIDTASKFAILED
DESCRIPTOR.message_types_by_name['Failed'] = _FAILED
DESCRIPTOR.message_types_by_name['Cancelled'] = _CANCELLED
DESCRIPTOR.message_types_by_name['Waiting'] = _WAITING
DESCRIPTOR.message_types_by_name['WaitForReRun'] = _WAITFORRERUN
DESCRIPTOR.message_types_by_name['CupidTaskDetailResultParam'] = _CUPIDTASKDETAILRESULTPARAM
DESCRIPTOR.message_types_by_name['OdpsLocalResourceItem'] = _ODPSLOCALRESOURCEITEM
DESCRIPTOR.message_types_by_name['OdpsLocalResource'] = _ODPSLOCALRESOURCE
DESCRIPTOR.enum_types_by_name['LocalResourceType'] = _LOCALRESOURCETYPE

JobConfItem = _reflection.GeneratedProtocolMessageType('JobConfItem', (_message.Message,), dict(
  DESCRIPTOR = _JOBCONFITEM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.JobConfItem)
  ))
_sym_db.RegisterMessage(JobConfItem)

JobConf = _reflection.GeneratedProtocolMessageType('JobConf', (_message.Message,), dict(
  DESCRIPTOR = _JOBCONF,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.JobConf)
  ))
_sym_db.RegisterMessage(JobConf)

CupidTaskOperator = _reflection.GeneratedProtocolMessageType('CupidTaskOperator', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDTASKOPERATOR,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidTaskOperator)
  ))
_sym_db.RegisterMessage(CupidTaskOperator)

MultiTablesInputInfos = _reflection.GeneratedProtocolMessageType('MultiTablesInputInfos', (_message.Message,), dict(
  DESCRIPTOR = _MULTITABLESINPUTINFOS,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.MultiTablesInputInfos)
  ))
_sym_db.RegisterMessage(MultiTablesInputInfos)

MultiTablesInputInfoItem = _reflection.GeneratedProtocolMessageType('MultiTablesInputInfoItem', (_message.Message,), dict(
  DESCRIPTOR = _MULTITABLESINPUTINFOITEM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.MultiTablesInputInfoItem)
  ))
_sym_db.RegisterMessage(MultiTablesInputInfoItem)

PartitionSizeInfo = _reflection.GeneratedProtocolMessageType('PartitionSizeInfo', (_message.Message,), dict(
  DESCRIPTOR = _PARTITIONSIZEINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.PartitionSizeInfo)
  ))
_sym_db.RegisterMessage(PartitionSizeInfo)

CupidSetInformation = _reflection.GeneratedProtocolMessageType('CupidSetInformation', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDSETINFORMATION,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidSetInformation)
  ))
_sym_db.RegisterMessage(CupidSetInformation)

CupidProxyTokenRequest = _reflection.GeneratedProtocolMessageType('CupidProxyTokenRequest', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDPROXYTOKENREQUEST,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidProxyTokenRequest)
  ))
_sym_db.RegisterMessage(CupidProxyTokenRequest)

GetApplicationMetaInfo = _reflection.GeneratedProtocolMessageType('GetApplicationMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _GETAPPLICATIONMETAINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetApplicationMetaInfo)
  ))
_sym_db.RegisterMessage(GetApplicationMetaInfo)

UpdateApplicationMetaInfo = _reflection.GeneratedProtocolMessageType('UpdateApplicationMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEAPPLICATIONMETAINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.UpdateApplicationMetaInfo)
  ))
_sym_db.RegisterMessage(UpdateApplicationMetaInfo)

ListApplicationMetaInfo = _reflection.GeneratedProtocolMessageType('ListApplicationMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _LISTAPPLICATIONMETAINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ListApplicationMetaInfo)
  ))
_sym_db.RegisterMessage(ListApplicationMetaInfo)

CreateApplicationMetaInfo = _reflection.GeneratedProtocolMessageType('CreateApplicationMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _CREATEAPPLICATIONMETAINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CreateApplicationMetaInfo)
  ))
_sym_db.RegisterMessage(CreateApplicationMetaInfo)

GenVolumePanguPathInfo = _reflection.GeneratedProtocolMessageType('GenVolumePanguPathInfo', (_message.Message,), dict(
  DESCRIPTOR = _GENVOLUMEPANGUPATHINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GenVolumePanguPathInfo)
  ))
_sym_db.RegisterMessage(GenVolumePanguPathInfo)

SplitItem = _reflection.GeneratedProtocolMessageType('SplitItem', (_message.Message,), dict(
  DESCRIPTOR = _SPLITITEM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.SplitItem)
  ))
_sym_db.RegisterMessage(SplitItem)

GetPartitionSizeResult = _reflection.GeneratedProtocolMessageType('GetPartitionSizeResult', (_message.Message,), dict(
  DESCRIPTOR = _GETPARTITIONSIZERESULT,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.GetPartitionSizeResult)
  ))
_sym_db.RegisterMessage(GetPartitionSizeResult)

CommitFile = _reflection.GeneratedProtocolMessageType('CommitFile', (_message.Message,), dict(
  DESCRIPTOR = _COMMITFILE,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitFile)
  ))
_sym_db.RegisterMessage(CommitFile)

CommitFileList = _reflection.GeneratedProtocolMessageType('CommitFileList', (_message.Message,), dict(
  DESCRIPTOR = _COMMITFILELIST,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CommitFileList)
  ))
_sym_db.RegisterMessage(CommitFileList)

DDLMultiTableInfoItem = _reflection.GeneratedProtocolMessageType('DDLMultiTableInfoItem', (_message.Message,), dict(
  DESCRIPTOR = _DDLMULTITABLEINFOITEM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DDLMultiTableInfoItem)
  ))
_sym_db.RegisterMessage(DDLMultiTableInfoItem)

DDLMultiTableInfos = _reflection.GeneratedProtocolMessageType('DDLMultiTableInfos', (_message.Message,), dict(
  DESCRIPTOR = _DDLMULTITABLEINFOS,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DDLMultiTableInfos)
  ))
_sym_db.RegisterMessage(DDLMultiTableInfos)

DDLInfo = _reflection.GeneratedProtocolMessageType('DDLInfo', (_message.Message,), dict(
  DESCRIPTOR = _DDLINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DDLInfo)
  ))
_sym_db.RegisterMessage(DDLInfo)

DDLInfoIterm = _reflection.GeneratedProtocolMessageType('DDLInfoIterm', (_message.Message,), dict(
  DESCRIPTOR = _DDLINFOITERM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.DDLInfoIterm)
  ))
_sym_db.RegisterMessage(DDLInfoIterm)

SaveTableInfo = _reflection.GeneratedProtocolMessageType('SaveTableInfo', (_message.Message,), dict(
  DESCRIPTOR = _SAVETABLEINFO,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.SaveTableInfo)
  ))
_sym_db.RegisterMessage(SaveTableInfo)

SubApplicationMeta = _reflection.GeneratedProtocolMessageType('SubApplicationMeta', (_message.Message,), dict(
  DESCRIPTOR = _SUBAPPLICATIONMETA,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.SubApplicationMeta)
  ))
_sym_db.RegisterMessage(SubApplicationMeta)

ApplicationMeta = _reflection.GeneratedProtocolMessageType('ApplicationMeta', (_message.Message,), dict(
  DESCRIPTOR = _APPLICATIONMETA,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ApplicationMeta)
  ))
_sym_db.RegisterMessage(ApplicationMeta)

TaskServiceRequest = _reflection.GeneratedProtocolMessageType('TaskServiceRequest', (_message.Message,), dict(
  DESCRIPTOR = _TASKSERVICEREQUEST,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.TaskServiceRequest)
  ))
_sym_db.RegisterMessage(TaskServiceRequest)

ApplicationMetaList = _reflection.GeneratedProtocolMessageType('ApplicationMetaList', (_message.Message,), dict(
  DESCRIPTOR = _APPLICATIONMETALIST,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.ApplicationMetaList)
  ))
_sym_db.RegisterMessage(ApplicationMetaList)

CupidTaskParam = _reflection.GeneratedProtocolMessageType('CupidTaskParam', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDTASKPARAM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidTaskParam)
  ))
_sym_db.RegisterMessage(CupidTaskParam)

Ready = _reflection.GeneratedProtocolMessageType('Ready', (_message.Message,), dict(
  DESCRIPTOR = _READY,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Ready)
  ))
_sym_db.RegisterMessage(Ready)

Running = _reflection.GeneratedProtocolMessageType('Running', (_message.Message,), dict(
  DESCRIPTOR = _RUNNING,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Running)
  ))
_sym_db.RegisterMessage(Running)

Success = _reflection.GeneratedProtocolMessageType('Success', (_message.Message,), dict(
  DESCRIPTOR = _SUCCESS,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Success)
  ))
_sym_db.RegisterMessage(Success)

BizFailed = _reflection.GeneratedProtocolMessageType('BizFailed', (_message.Message,), dict(
  DESCRIPTOR = _BIZFAILED,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.BizFailed)
  ))
_sym_db.RegisterMessage(BizFailed)

CupidTaskFailed = _reflection.GeneratedProtocolMessageType('CupidTaskFailed', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDTASKFAILED,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidTaskFailed)
  ))
_sym_db.RegisterMessage(CupidTaskFailed)

Failed = _reflection.GeneratedProtocolMessageType('Failed', (_message.Message,), dict(
  DESCRIPTOR = _FAILED,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Failed)
  ))
_sym_db.RegisterMessage(Failed)

Cancelled = _reflection.GeneratedProtocolMessageType('Cancelled', (_message.Message,), dict(
  DESCRIPTOR = _CANCELLED,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Cancelled)
  ))
_sym_db.RegisterMessage(Cancelled)

Waiting = _reflection.GeneratedProtocolMessageType('Waiting', (_message.Message,), dict(
  DESCRIPTOR = _WAITING,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.Waiting)
  ))
_sym_db.RegisterMessage(Waiting)

WaitForReRun = _reflection.GeneratedProtocolMessageType('WaitForReRun', (_message.Message,), dict(
  DESCRIPTOR = _WAITFORRERUN,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.WaitForReRun)
  ))
_sym_db.RegisterMessage(WaitForReRun)

CupidTaskDetailResultParam = _reflection.GeneratedProtocolMessageType('CupidTaskDetailResultParam', (_message.Message,), dict(
  DESCRIPTOR = _CUPIDTASKDETAILRESULTPARAM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.CupidTaskDetailResultParam)
  ))
_sym_db.RegisterMessage(CupidTaskDetailResultParam)

OdpsLocalResourceItem = _reflection.GeneratedProtocolMessageType('OdpsLocalResourceItem', (_message.Message,), dict(
  DESCRIPTOR = _ODPSLOCALRESOURCEITEM,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.OdpsLocalResourceItem)
  ))
_sym_db.RegisterMessage(OdpsLocalResourceItem)

OdpsLocalResource = _reflection.GeneratedProtocolMessageType('OdpsLocalResource', (_message.Message,), dict(
  DESCRIPTOR = _ODPSLOCALRESOURCE,
  __module__ = 'cupidtaskparam_pb2'
  # @@protoc_insertion_point(class_scope:apsara.odps.cupid.protocol.OdpsLocalResource)
  ))
_sym_db.RegisterMessage(OdpsLocalResource)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('B\024CupidTaskParamProtos'))
# @@protoc_insertion_point(module_scope)
