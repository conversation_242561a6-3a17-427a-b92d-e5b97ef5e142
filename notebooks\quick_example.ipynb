{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# A Quick Example of Using PyOdps\n", "\n", "## prepare odps object, and probe table's schema"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["odps.<PERSON> {  \n", "  remote_ip             string\n", "  remote_user           string\n", "  time                  string\n", "  request               string\n", "  http_code             bigint\n", "  body_size             bigint\n", "  referer               string\n", "  user_agent            string\n", "}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from odps import ODPS\n", "o = ODPS(\"your_access_id\", \"your_access_key\", \"your_project\", \"https://service.odps.aliyun.com/api\")\n", "t = o.get_table(\"sample_access_log\")\n", "t.schema"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## peek first few lines of table"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[u'**************', u'-', u'[08/Nov/2015:07:50:59 +0800]', u'\"GET http://www.tianjinwe.com/ HTTP/1.1\"', 200, 179, u'\"http://www.tianjinwe.com/\"', u'\"Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)\"']\n", "[u'162.252.240.124', u'-', u'[08/Nov/2015:08:15:26 +0800]', u'\"GET http://162.252.240.124:80/gw.php HTTP/1.1\"', 404, 209, u'\"-\"', u'\"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.107 Safari/537.36\"']\n", "[u'183.60.48.25', u'-', u'[08/Nov/2015:08:29:52 +0800]', u'\"GET http://www.baidu.com/ HTTP/1.1\"', 200, 179, u'\"-\"', u'\"-\"']\n", "[u'62.210.88.201', u'-', u'[08/Nov/2015:09:51:14 +0800]', u'\"GET http://51.254.206.142/httptest.php HTTP/1.1\"', 404, 151, u'\"-\"', u'\"-\"']\n", "[u'62.210.88.201', u'-', u'[08/Nov/2015:09:51:41 +0800]', u'\"GET http://www.google.pl/search?q=wp.pl&num=100&start=100 HTTP/1.1\"', 404, 151, u'\"-\"', u'\"-\"']\n"]}], "source": ["for r in t.head(5):\n", "    print r.values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## run a sql, and obtain its result"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[200, 252]\n", "[301, 2]\n", "[304, 73]\n", "[400, 13]\n", "[403, 1]\n", "[404, 1347]\n", "[405, 28]\n", "[408, 1]\n", "[416, 4]\n"]}], "source": ["from odps.models import TableSchema\n", "s = TableSchema.from_lists(['http_code','count'], ['bigint','bigint'])\n", "labels = []\n", "values = []\n", "with o.execute_sql(\"select http_code, count(*) count from sample_access_log group by http_code\").open_reader(s) as reader:\n", "    for r in reader:\n", "        print r.values\n", "        labels.append(r[0])\n", "        values.append(r[1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## plot a pie chart using the sql result above"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAV0AAADvCAYAAABR/Qd9AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nSENXKaUq9AMs6nDhC9HEpQAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f815d24dad0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "plt.pie(values, labels=labels, autopct='%1.1f%%', shadow=True, startangle=140)\n", "\n", "plt.axis('equal')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## export the sql result into a local excel file"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [], "source": ["import xlwt\n", "\n", "book = xlwt.Workbook(encoding=\"utf-8\")\n", "sheet = book.add_sheet(\"from odps\")\n", "\n", "with t.open_reader() as reader:\n", "    y = 0\n", "    for r in reader.read():\n", "        x = 0\n", "        for v in r.values:\n", "            sheet.write(y, x, v)\n", "            x = x + 1\n", "        y = y + 1\n", "\n", "book.save(\"from_odps.xls\")"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python2", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.10"}}, "nbformat": 4, "nbformat_minor": 0}