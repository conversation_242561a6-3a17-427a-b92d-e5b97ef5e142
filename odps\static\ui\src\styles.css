/*
 * Copyright 1999-2017 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
.pyodps-progress-launcher {
    margin-right: 5px;
}

#pyodps-progress-viewer .generated-time {
    color: #ccc;
    padding-right: 10px;
}

#pyodps-progress-viewer .stage {
    width: 100%;
    display: table;
    table-layout: fixed;
    margin-top: 3px;
    margin-bottom: 3px;
}

#pyodps-progress-viewer .stage-name {
    display: table-cell;
    width: 30%;
    overflow: hidden;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

#pyodps-progress-viewer .stage-progress {
    display: table-cell; vertical-align: middle;
}

#pyodps-progress-viewer .progress {
    margin: 0;
}

#pyodps-progress-viewer .progress-bar {
    border: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    width: 0;
}

#pyodps-progress-viewer .progress-legend-panel {
    font-size: smaller;
    margin-top: 8px;
}

#pyodps-progress-viewer .progress-legend-panel span {
    float: left !important;
}

#pyodps-progress-viewer .progress-legend {
    height: 10px;
    width: 30px !important;
    margin-left: 5px;
    margin-right: 5px;
    margin-top: 3px;
}

#pyodps-progress-viewer .progress-bar {
    background-color: #2196F3;
}

#pyodps-progress-viewer .progress-bar-warning {
    background-color: #FF9800;
}

#pyodps-progress-viewer .task-name {
    border-bottom: 1px solid #ccc;
}

.retry-btn {
    cursor: pointer;
    font-size: larger;
    padding-right: 5px;
    padding-top: 4px;
    color: steelblue;
    display: block;
}

.retry-btn:hover {
    color: skyblue;
}

.retry-btn:active {
    color: steelblue;
}

.retry-btn:visited {
    color: steelblue;
}

.show-table-body {
    overflow-x: auto;
}

.df-function-nav {
    margin: 0 !important;
}

.df-function-nav li a {
    padding: 6px 10px;
}

.df-table-pagination {
    margin: 0 !important;
}

.df-agg-selector {
    padding-left: 3px !important;
    padding-right: 0 !important;
}
