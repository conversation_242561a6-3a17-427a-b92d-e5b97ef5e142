# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 18:28+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"

#: ../../source/df-element.rst:4
msgid "列运算"
msgstr "Column operations"

#: ../../source/df-element.rst:6
msgid "from odps.df import DataFrame"
msgstr ""

#: ../../source/df-element.rst:10
msgid ""
"iris = DataFrame(o.get_table('pyodps_iris'))\n"
"lens = DataFrame(o.get_table('pyodps_ml_100k_lens'))"
msgstr ""

#: ../../source/df-element.rst:16
msgid ""
"对于一个Sequence来说，对它加上一个常量、或者执行sin函数的这类操作时，是"
"作用于每个元素上的。接下来会详细说明。"
msgstr ""
"For a Sequence object, operations such as adding a constant or executing "
"the sin function are performed on all elements of the object. For more "
"information, see the following:"

#: ../../source/df-element.rst:19
msgid "NULL相关（isnull，notnull，fillna）"
msgstr "NULL-related functions (isnull, notnull, fillna)"

#: ../../source/df-element.rst:21
msgid ""
"DataFrame API提供了几个和NULL相关的内置函数，比如isnull来判断是否某字段是"
"NULL，notnull则相反，fillna是将NULL填充为用户指定的值。"
msgstr ""
"The DataFrame API provides several NULL-related built-in functions. You "
"can use isnull or notnull to determine whether or not a field is NULL, "
"and use fillna to replace NULL fields with specified values."

#: ../../source/df-element.rst:24
msgid ""
">>> iris.sepallength.isnull().head(5)\n"
"   sepallength\n"
"0        False\n"
"1        False\n"
"2        False\n"
"3        False\n"
"4        False"
msgstr ""

#: ../../source/df-element.rst:35
msgid "逻辑判断（ifelse，switch）"
msgstr "Logic functions (ifelse, switch)"

#: ../../source/df-element.rst:37
msgid ""
"``ifelse``\\ 作用于boolean类型的字段，当条件成立时，返回第0个参数，否则"
"返回第1个参数。"
msgstr ""
"The ``ifelse``\\ function acts on boolean fields, and returns the first "
"parameter if the condition is true, or the second parameter if the "
"condition is false."

#: ../../source/df-element.rst:39
msgid ""
">>> (iris.sepallength > 5).ifelse('gt5', 'lte5').rename('cmp5').head(5)\n"
"   cmp5\n"
"0   gt5\n"
"1  lte5\n"
"2  lte5\n"
"3  lte5\n"
"4  lte5"
msgstr ""

#: ../../source/df-element.rst:50
msgid "switch用于多条件判断的情况。"
msgstr "The switch function is used to handle multiple conditions."

#: ../../source/df-element.rst:52
msgid ""
">>> iris.sepallength.switch(4.9, 'eq4.9', 5.0, 'eq5.0', "
"default='noeq').rename('equalness').head(5)\n"
"   equalness\n"
"0       noeq\n"
"1      eq4.9\n"
"2       noeq\n"
"3       noeq\n"
"4      eq5.0"
msgstr ""

#: ../../source/df-element.rst:62
msgid ""
">>> from odps.df import switch\n"
">>> switch(iris.sepallength == 4.9, 'eq4.9', iris.sepallength == 5.0, "
"'eq5.0', default='noeq').rename('equalness').head(5)\n"
"   equalness\n"
"0       noeq\n"
"1      eq4.9\n"
"2       noeq\n"
"3       noeq\n"
"4      eq5.0"
msgstr ""

#: ../../source/df-element.rst:73
msgid "PyODPS 0.7.8 以上版本支持根据条件修改数据集某一列的一部分值，写法为："
msgstr ""
"In PyODPS versions 0.7.8 and higher, you can change the column values of "
"a dataset based on the following conditions:"

#: ../../source/df-element.rst:75
msgid ""
">>> iris[iris.sepallength > 5, 'cmp5'] = 'gt5'\n"
">>> iris[iris.sepallength <= 5, 'cmp5'] = 'lte5'\n"
">>> iris.head(5)\n"
"   cmp5\n"
"0   gt5\n"
"1  lte5\n"
"2  lte5\n"
"3  lte5\n"
"4  lte5"
msgstr ""

#: ../../source/df-element.rst:88
msgid "数学运算"
msgstr "Mathematical operations"

#: ../../source/df-element.rst:90
msgid "对于数字类型的字段，支持+，-，\\*，/等操作，也支持log、sin等数学计算。"
msgstr ""
"For numeric fields, operations such as addition (+), subtraction (-), "
"multiplication (\\*), and division (/) are supported. In addition, "
"operations such as log, sin are also supported."

#: ../../source/df-element.rst:92
msgid ""
">>> (iris.sepallength * 10).log().head(5)\n"
"   sepallength\n"
"0     3.931826\n"
"1     3.891820\n"
"2     3.850148\n"
"3     3.828641\n"
"4     3.912023"
msgstr ""

#: ../../source/df-element.rst:102
msgid ""
">>> fields = [iris.sepallength,\n"
">>>           (iris.sepallength / 2).rename('sepallength除以2'),\n"
">>>           (iris.sepallength ** 2).rename('sepallength的平方')]\n"
">>> iris[fields].head(5)\n"
"   sepallength  sepallength除以2  sepallength的平方\n"
"0          5.1              2.55             26.01\n"
"1          4.9              2.45             24.01\n"
"2          4.7              2.35             22.09\n"
"3          4.6              2.30             21.16\n"
"4          5.0              2.50             25.00"
msgstr ""
">>> fields = [iris.sepallength,\n"
">>>           (iris.sepallength / 2).rename('sepallength_div_2'),\n"
">>>           (iris.sepallength ** 2).rename('sepallength_square')]\n"
">>> iris[fields].head(5)\n"
"   sepallength  sepallength_div_2  sepallength_square\n"
"0          5.1               2.55               26.01\n"
"1          4.9               2.45               24.01\n"
"2          4.7               2.35               22.09\n"
"3          4.6               2.30               21.16\n"
"4          5.0               2.50               25.00"

#: ../../source/df-element.rst:116
msgid "算术运算支持的操作包括："
msgstr "The supported arithmetic operations include:"

#: ../../source/df-element.rst:119
msgid "算术操作"
msgstr "Arithmetic operation"

#: ../../source/df-element.rst:119 ../../source/df-element.rst:207
#: ../../source/df-element.rst:270 ../../source/df-element.rst:312
#: ../../source/df-element.rst:405 ../../source/df-element.rst:414
msgid "说明"
msgstr "Description"

#: ../../source/df-element.rst:121
msgid "abs"
msgstr ""

#: ../../source/df-element.rst:121
msgid "绝对值"
msgstr "Returns the absolute value of the given number."

#: ../../source/df-element.rst:122
msgid "sqrt"
msgstr ""

#: ../../source/df-element.rst:122
msgid "平方根"
msgstr "Returns the square root of the given number."

#: ../../source/df-element.rst:123
msgid "sin"
msgstr ""

#: ../../source/df-element.rst:124
msgid "sinh"
msgstr ""

#: ../../source/df-element.rst:125
msgid "cos"
msgstr ""

#: ../../source/df-element.rst:126
msgid "cosh"
msgstr ""

#: ../../source/df-element.rst:127
msgid "tan"
msgstr ""

#: ../../source/df-element.rst:128
msgid "tanh"
msgstr ""

#: ../../source/df-element.rst:129
msgid "arccos"
msgstr ""

#: ../../source/df-element.rst:130
msgid "arccosh"
msgstr ""

#: ../../source/df-element.rst:131
msgid "arcsin"
msgstr ""

#: ../../source/df-element.rst:132
msgid "arcsinh"
msgstr ""

#: ../../source/df-element.rst:133
msgid "arctan"
msgstr ""

#: ../../source/df-element.rst:134
msgid "arctanh"
msgstr ""

#: ../../source/df-element.rst:135
msgid "exp"
msgstr ""

#: ../../source/df-element.rst:135
msgid "指数函数"
msgstr "Returns e^x of the given number x."

#: ../../source/df-element.rst:136
msgid "expm1"
msgstr ""

#: ../../source/df-element.rst:136
msgid "指数减1"
msgstr "Returns e^x-1 of the given number x."

#: ../../source/df-element.rst:137
msgid "log"
msgstr ""

#: ../../source/df-element.rst:137
msgid "传入参数表示底是几"
msgstr "A parameter is required as the base."

#: ../../source/df-element.rst:138
msgid "log2"
msgstr ""

#: ../../source/df-element.rst:139
msgid "log10"
msgstr ""

#: ../../source/df-element.rst:140
msgid "log1p"
msgstr ""

#: ../../source/df-element.rst:140
msgid "log(1+x)"
msgstr ""

#: ../../source/df-element.rst:141
msgid "radians"
msgstr ""

#: ../../source/df-element.rst:141
msgid "给定角度计算弧度"
msgstr "Converts radians to degrees."

#: ../../source/df-element.rst:142
msgid "degrees"
msgstr ""

#: ../../source/df-element.rst:142
msgid "给定弧度计算角度"
msgstr "Converts degrees to radians."

#: ../../source/df-element.rst:143
msgid "ceil"
msgstr ""

#: ../../source/df-element.rst:143
msgid "不小于输入值的最小整数"
msgstr "Returns the smallest integer that is no less than the given number."

#: ../../source/df-element.rst:144
msgid "floor"
msgstr ""

#: ../../source/df-element.rst:144
msgid "向下取整，返回比输入值小的整数值"
msgstr "Returns the largest integer that is no greater than the given number."

#: ../../source/df-element.rst:145
msgid "trunc"
msgstr ""

#: ../../source/df-element.rst:145
msgid "将输入值截取到指定小数点位置"
msgstr "Returns a number truncated to the specified decimal place."

#: ../../source/df-element.rst:148
msgid "对于sequence，也支持其于其他sequence或者scalar的比较。"
msgstr "You can compare a Sequence object with another Sequence or Scalar object."

#: ../../source/df-element.rst:150
msgid ""
">>> (iris.sepallength < 5).head(5)\n"
"   sepallength\n"
"0        False\n"
"1         True\n"
"2         True\n"
"3         True\n"
"4        False"
msgstr ""

#: ../../source/df-element.rst:160
msgid ""
"值得主意的是，DataFrame API不支持连续操作，比如\\ ``3 <= iris.sepallength"
" <= 5``\\ ，但是提供了between这个函数来进行是否在某个区间的判断。"
msgstr ""
"Note that the DataFrame API does not support sequential operations, such "
"as \\ ``3 <= iris.sepallength <= 5``\\. But you can use the between "
"function to determine whether a field is within a certain range."

#: ../../source/df-element.rst:163
msgid ""
">>> (iris.sepallength.between(3, 5)).head(5)\n"
"   sepallength\n"
"0        False\n"
"1         True\n"
"2         True\n"
"3         True\n"
"4         True"
msgstr ""

#: ../../source/df-element.rst:173
msgid ""
"默认情况下，between包含两边的区间，如果计算开区间，则需要设inclusive="
"False。"
msgstr ""
"By default, the between function specifies an interval that includes "
"endpoints. To specify an open interval, set inclusive to False."

#: ../../source/df-element.rst:175
msgid ""
">>> (iris.sepallength.between(3, 5, inclusive=False)).head(5)\n"
"   sepallength\n"
"0        False\n"
"1         True\n"
"2         True\n"
"3         True\n"
"4        False"
msgstr ""

#: ../../source/df-element.rst:186
msgid "String 相关操作"
msgstr "String-related operations "

#: ../../source/df-element.rst:188
msgid "DataFrame API提供了一系列针对string类型的Sequence或者Scalar的操作。"
msgstr ""
"The DataFrame API provides a number of string-related operations for "
"Sequence and Scalar objects."

#: ../../source/df-element.rst:190
msgid ""
">>> fields = [\n"
">>>     iris.name.upper().rename('upper_name'),\n"
">>>     iris.name.extract('Iris(.*)', group=1)\n"
">>> ]\n"
">>> iris[fields].head(5)\n"
"    upper_name     name\n"
"0  IRIS-SETOSA  -setosa\n"
"1  IRIS-SETOSA  -setosa\n"
"2  IRIS-SETOSA  -setosa\n"
"3  IRIS-SETOSA  -setosa\n"
"4  IRIS-SETOSA  -setosa"
msgstr ""

#: ../../source/df-element.rst:204
msgid "string相关操作包括："
msgstr "The operations are:"

#: ../../source/df-element.rst:207
msgid "string 操作"
msgstr "Operation"

#: ../../source/df-element.rst:209
msgid "capitalize"
msgstr ""

#: ../../source/df-element.rst:210
msgid "contains"
msgstr ""

#: ../../source/df-element.rst:210
msgid ""
"包含某个字符串，如果 regex 参数为 True，则是包含某个正则表达式，默认为 "
"True"
msgstr ""
"Returns whether the given string contains a substring. The substring is a"
" regular expression if the regex parameter is set to True. The regex "
"parameter is set to True by default."

#: ../../source/df-element.rst:211
msgid "count"
msgstr ""

#: ../../source/df-element.rst:211
msgid "指定字符串出现的次数"
msgstr "Counts the number of occurrences of the specified string."

#: ../../source/df-element.rst:212
msgid "endswith"
msgstr ""

#: ../../source/df-element.rst:212
msgid "以某个字符串结尾"
msgstr "Returns whether the given string ends with the specified string."

#: ../../source/df-element.rst:213
msgid "startswith"
msgstr ""

#: ../../source/df-element.rst:213
msgid "以某个字符串开头"
msgstr "Returns whether the given string starts with the specified string."

#: ../../source/df-element.rst:214
msgid "extract"
msgstr ""

#: ../../source/df-element.rst:214
msgid ""
"抽取出某个正则表达式，如果 group 不指定，则返回满足整个 pattern 的子串；"
"否则，返回第几个 group"
msgstr ""
"Extracts a regular expression, and if the group has not been specified, "
"returns the substrings that satisfy the pattern. If the group has been "
"specified, the specified group is returned."

#: ../../source/df-element.rst:215
msgid "find"
msgstr ""

#: ../../source/df-element.rst:215
msgid "返回第一次出现的子串位置，若不存在则返回-1"
msgstr ""
"Searches from left to right and returns the position of the first "
"occurrence of the specified substring. -1 is returned if no matching has "
"been found."

#: ../../source/df-element.rst:216
msgid "rfind"
msgstr ""

#: ../../source/df-element.rst:216
msgid "从右查找返回子串第一次出现的位置，不存在则返回-1"
msgstr ""
"Searches from right to left and returns the position of the first "
"occurrence of the specified substring. -1 is returned if no matching has "
"been found."

#: ../../source/df-element.rst:217
msgid "replace"
msgstr ""

#: ../../source/df-element.rst:217
msgid "将某个 pattern 的子串全部替换成另一个子串， ``n`` 参数若指定，则替换n次"
msgstr ""
"Replaces the substrings that satisfy the pattern with another substring. "
"If ``n`` has been specified, replace n times."

#: ../../source/df-element.rst:218
msgid "get"
msgstr ""

#: ../../source/df-element.rst:218
msgid "返回某个位置上的字符串"
msgstr "Returns the string at the specified position."

#: ../../source/df-element.rst:219
msgid "len"
msgstr ""

#: ../../source/df-element.rst:219
msgid "返回字符串的长度"
msgstr "Returns the length of the string."

#: ../../source/df-element.rst:220
msgid "ljust"
msgstr ""

#: ../../source/df-element.rst:220
msgid ""
"若未达到指定的 ``width`` 的长度，则在右侧填充 ``fillchar`` 指定的字符串（"
"默认空格）"
msgstr ""
"Pads the string with the character ``fillchar`` on the right until it "
"reaches the specified length ``width``. The space character is used by "
"default."

#: ../../source/df-element.rst:221
msgid "rjust"
msgstr ""

#: ../../source/df-element.rst:221
msgid ""
"若未达到指定的 ``width`` 的长度，则在左侧填充 ``fillchar`` 指定的字符串（"
"默认空格）"
msgstr ""
"Pads the string with the character ``fillchar`` on the left until it "
"reaches the specified length ``width``. The space character is used by "
"default."

#: ../../source/df-element.rst:222
msgid "lower"
msgstr ""

#: ../../source/df-element.rst:222
msgid "变为全部小写"
msgstr "Converts the string to lowercase."

#: ../../source/df-element.rst:223
msgid "upper"
msgstr ""

#: ../../source/df-element.rst:223
msgid "变为全部大写"
msgstr "Converts the string to uppercase."

#: ../../source/df-element.rst:224
msgid "lstrip"
msgstr ""

#: ../../source/df-element.rst:224
msgid "在左侧删除空格（包括空行符）"
msgstr "Remove spaces (including blank lines) on the left of the string."

#: ../../source/df-element.rst:225
msgid "rstrip"
msgstr ""

#: ../../source/df-element.rst:225
msgid "在右侧删除空格（包括空行符）"
msgstr "Remove spaces (including blank lines) on the right of the string."

#: ../../source/df-element.rst:226
msgid "strip"
msgstr ""

#: ../../source/df-element.rst:226
msgid "在左右两侧删除空格（包括空行符）"
msgstr "Remove spaces (including blank lines) on both sides of the string."

#: ../../source/df-element.rst:227
msgid "split"
msgstr ""

#: ../../source/df-element.rst:227
msgid "将字符串按分隔符拆分为若干个字符串（返回 list<string> 类型）"
msgstr "Splits the string at the specified separator and returns a list<0/> type."

#: ../../source/df-element.rst:228
msgid "pad"
msgstr ""

#: ../../source/df-element.rst:228
msgid ""
"在指定的位置（left，right 或者 both）用指定填充字符（用 ``fillchar`` 指定"
"，默认空格）来对齐"
msgstr ""
"Pads the string with the character ``fillchar`` on the specified position"
" which may be left, right or both. The space character is used by "
"default."

#: ../../source/df-element.rst:229
msgid "repeat"
msgstr ""

#: ../../source/df-element.rst:229
msgid "重复指定 ``n`` 次"
msgstr "Repeats n times."

#: ../../source/df-element.rst:230
msgid "slice"
msgstr ""

#: ../../source/df-element.rst:230
msgid "切片操作"
msgstr "Performs slice operations."

#: ../../source/df-element.rst:231
msgid "swapcase"
msgstr ""

#: ../../source/df-element.rst:231
msgid "对调大小写"
msgstr ""
"Converts all the uppercase characters to lowercase and all the lowercase "
"characters to uppercase in the string."

#: ../../source/df-element.rst:232
msgid "title"
msgstr ""

#: ../../source/df-element.rst:232
msgid "同 str.title"
msgstr "The same as str.title."

#: ../../source/df-element.rst:233
msgid "zfill"
msgstr ""

#: ../../source/df-element.rst:233
msgid "长度没达到指定 ``width`` ，则左侧填充0"
msgstr ""
"Pads the string with the character ``0`` on the left until it reaches the"
" specified length ``width``."

#: ../../source/df-element.rst:234
msgid "isalnum"
msgstr ""

#: ../../source/df-element.rst:234
msgid "同 str.isalnum"
msgstr "The same as str.isalnum."

#: ../../source/df-element.rst:235
msgid "isalpha"
msgstr ""

#: ../../source/df-element.rst:235
msgid "同 str.isalpha"
msgstr "The same as str.isalpha."

#: ../../source/df-element.rst:236
msgid "isdigit"
msgstr ""

#: ../../source/df-element.rst:236
msgid "是否都是数字，同 str.isdigit"
msgstr ""
"Returns True if all the characters in the string are digits. This is the "
"same as str.isdigit."

#: ../../source/df-element.rst:237
msgid "isspace"
msgstr ""

#: ../../source/df-element.rst:237
msgid "是否都是空格，同 str.isspace"
msgstr ""
"Returns True if all the characters in the string are spaces. This is the "
"same as str.isspace."

#: ../../source/df-element.rst:238
msgid "islower"
msgstr ""

#: ../../source/df-element.rst:238
msgid "是否都是小写，同 str.islower"
msgstr ""
"Returns True if all the cased characters in the string are lowercase. "
"This is the same as str.islower."

#: ../../source/df-element.rst:239
msgid "isupper"
msgstr ""

#: ../../source/df-element.rst:239
msgid "是否都是大写，同 str.isupper"
msgstr ""
"Returns True if all the cased characters in the string are uppercase. "
"This is the same as str.isupper."

#: ../../source/df-element.rst:240
msgid "istitle"
msgstr ""

#: ../../source/df-element.rst:240
msgid "同 str.istitle"
msgstr "This is the same as str.istitle."

#: ../../source/df-element.rst:241
msgid "isnumeric"
msgstr ""

#: ../../source/df-element.rst:241
msgid "同 str.isnumeric"
msgstr "The same as str.isnumeric."

#: ../../source/df-element.rst:242
msgid "isdecimal"
msgstr ""

#: ../../source/df-element.rst:242
msgid "同 str.isdecimal"
msgstr "This is the same as str.isdecimal."

#: ../../source/df-element.rst:243
msgid "todict"
msgstr ""

#: ../../source/df-element.rst:243
msgid ""
"将字符串按分隔符拆分为一个 dict，传入的两个参数分别为项目分隔符和 Key-"
"Value 分隔符（返回 dict<string, string> 类型）"
msgstr ""
"Splits the string at the specified separator into a dict. Two parameters,"
" the project separator and the Key-Value separator are required. Returns "
"a dict<1/> type."

#: ../../source/df-element.rst:244
msgid "strptime"
msgstr ""

#: ../../source/df-element.rst:244
msgid ""
"按格式化读取成时间，时间格式和Python标准库相同，详细参考 `Python 时间"
"格式化 <https://docs.python.org/2/library/datetime.html#strftime-and-"
"strptime-behavior>`_"
msgstr ""
"Converts the string representing a time to a time type according to the "
"specified format. The time format is the same as specified in the "
"standard Python library. For details, see `Python time formats "
"<https://docs.python.org/2/library/datetime.html#strftime-and-strptime-"
"behavior>`_."

#: ../../source/df-element.rst:248
msgid "时间相关操作"
msgstr "Time-related operations"

#: ../../source/df-element.rst:250
msgid "对于datetime类型Sequence或者Scalar，可以调用时间相关的内置函数。"
msgstr ""
"For datetime type Sequence or Scalar objects, the following time-related "
"built-in functions are provided."

#: ../../source/df-element.rst:252
msgid ""
">>> df = lens[[lens.unix_timestamp.astype('datetime').rename('dt')]]\n"
">>> df[df.dt,\n"
">>>    df.dt.year.rename('year'),\n"
">>>    df.dt.month.rename('month'),\n"
">>>    df.dt.day.rename('day'),\n"
">>>    df.dt.hour.rename('hour')].head(5)\n"
"                    dt  year  month  day  hour\n"
"0  1998-04-08 11:02:00  1998      4    8    11\n"
"1  1998-04-08 10:57:55  1998      4    8    10\n"
"2  1998-04-08 10:45:26  1998      4    8    10\n"
"3  1998-04-08 10:25:52  1998      4    8    10\n"
"4  1998-04-08 10:44:19  1998      4    8    10"
msgstr ""

#: ../../source/df-element.rst:267
msgid "与时间相关的属性包括："
msgstr "Time-related attributes are:"

#: ../../source/df-element.rst:270
msgid "时间相关属性"
msgstr "Time-related attribute"

#: ../../source/df-element.rst:272 ../../source/df-element.rst:314
msgid "year"
msgstr ""

#: ../../source/df-element.rst:273 ../../source/df-element.rst:315
msgid "month"
msgstr ""

#: ../../source/df-element.rst:274 ../../source/df-element.rst:316
msgid "day"
msgstr ""

#: ../../source/df-element.rst:275 ../../source/df-element.rst:317
msgid "hour"
msgstr ""

#: ../../source/df-element.rst:276 ../../source/df-element.rst:318
msgid "minute"
msgstr ""

#: ../../source/df-element.rst:277 ../../source/df-element.rst:319
msgid "second"
msgstr ""

#: ../../source/df-element.rst:278
msgid "weekofyear"
msgstr ""

#: ../../source/df-element.rst:278
msgid "返回日期位于那一年的第几周。周一作为一周的第一天"
msgstr ""
"Returns the week of the year for the specified date. Monday is the the "
"first day of a week."

#: ../../source/df-element.rst:279
msgid "weekday"
msgstr ""

#: ../../source/df-element.rst:279
msgid "返回日期当前周的第几天"
msgstr "Returns the day of the week for the specified date."

#: ../../source/df-element.rst:280
msgid "dayofweek"
msgstr ""

#: ../../source/df-element.rst:280
msgid "同 weekday"
msgstr "The same as weekday."

#: ../../source/df-element.rst:281
msgid "strftime"
msgstr ""

#: ../../source/df-element.rst:281
msgid ""
"格式化时间，时间格式和 Python 标准库相同，详细参考 `Python 时间格式化 <"
"https://docs.python.org/2/library/datetime.html#strftime-and-strptime-"
"behavior>`_"
msgstr ""
"Converts a time to a string type according to the specified format. The "
"time format is the same as in the standard Python library. For more "
"information, see `Python time formats "
"<https://docs.python.org/2/library/datetime.html#strftime-and-strptime-"
"behavior>`_."

#: ../../source/df-element.rst:284
msgid ""
"PyODPS 也支持时间的加减操作，比如可以通过以下方法得到前3天的日期。两个"
"日期列相减得到相差的毫秒数。"
msgstr ""
"PyODPS also supports the addition and subtraction of time. For example, "
"you can retrieve the date 3 days before the current date. Subtracting one"
" date column from another returns the difference in milliseconds."

#: ../../source/df-element.rst:287
msgid ""
">>> df\n"
"                           a                          b\n"
"0 2016-12-06 16:43:12.460001 2016-12-06 17:43:12.460018\n"
"1 2016-12-06 16:43:12.460012 2016-12-06 17:43:12.460021\n"
"2 2016-12-06 16:43:12.460015 2016-12-06 17:43:12.460022\n"
">>> from odps.df import day\n"
">>> df.a - day(3)\n"
"                           a\n"
"0 2016-12-03 16:43:12.460001\n"
"1 2016-12-03 16:43:12.460012\n"
"2 2016-12-03 16:43:12.460015\n"
">>> (df.b - df.a).dtype\n"
"int64\n"
">>> (df.b - df.a).rename('a')\n"
"         a\n"
"0  3600000\n"
"1  3600000\n"
"2  3600000"
msgstr ""

#: ../../source/df-element.rst:309
msgid "支持的时间类型包括："
msgstr "Supported time types include:"

#: ../../source/df-element.rst:312
msgid "属性"
msgstr "Attribute"

#: ../../source/df-element.rst:320
msgid "millisecond"
msgstr ""

#: ../../source/df-element.rst:326
msgid "集合类型相关操作"
msgstr "Collection type related operations"

#: ../../source/df-element.rst:327
msgid ""
"PyODPS 支持的集合类型有 List 和 Dict。这两个类型都可以使用下标获取集合中"
"的某个项目，另有 len 方法，可获得集合的大小。"
msgstr ""
"PyODPS sequences supports List and Dict types. You can use subscripts to "
"retrieve an item from both types. You can also use ``len`` method to "
"retrieve the number of items in each element."

#: ../../source/df-element.rst:329
msgid ""
"同时，两种集合均有 explode 方法，用于展开集合中的内容。对于 List，explode"
" 默认返回一列，当传入参数 pos 时， 将返回两列，其中一列为值在数组中的编号"
"（类似 Python 的 enumerate 函数）。对于 Dict，explode 会返回两列， 分别"
"表示 keys 及 values。explode 中也可以传入列名，作为最后生成的列。"
msgstr ""
"Additionally, List and Dict types support ``explode`` method, which can "
"be used to display the contents of the collection. For List, explode "
"returns one column by default. When the pos parameter has been specified,"
" it returns two columns, with one of them representing the serial number "
"of the value in the array. The explode method is similar to the enumerate"
" function in Python. For Dict, explode returns two columns, which "
"represent the keys and values respectively. You can also pass in a column"
" name to explode to specify the result column name."

#: ../../source/df-element.rst:333
msgid "示例如下："
msgstr "The following are some examples:"

#: ../../source/df-element.rst:335
msgid ""
">>> df\n"
"   id         a                            b\n"
"0   1  [a1, b1]  {'a2': 0, 'b2': 1, 'c2': 2}\n"
"1   2      [c1]           {'d2': 3, 'e2': 4}\n"
">>> df[df.id, df.a[0], df.b['b2']]\n"
"   id   a    b\n"
"0   1  a1    1\n"
"1   2  c1  NaN\n"
">>> df[df.id, df.a.len(), df.b.len()]\n"
"   id  a  b\n"
"0   1  2  3\n"
"1   2  1  2\n"
">>> df.a.explode()\n"
"    a\n"
"0  a1\n"
"1  b1\n"
"2  c1\n"
">>> df.a.explode(pos=True)\n"
"   a_pos   a\n"
"0      0  a1\n"
"1      1  b1\n"
"2      0  c1\n"
">>> # 指定列名\n"
">>> df.a.explode(['pos', 'value'], pos=True)\n"
"   pos value\n"
"0    0    a1\n"
"1    1    b1\n"
"2    0    c1\n"
">>> df.b.explode()\n"
"  b_key  b_value\n"
"0    a2        0\n"
"1    b2        1\n"
"2    c2        2\n"
"3    d2        3\n"
"4    e2        4\n"
">>> # 指定列名\n"
">>> df.b.explode(['key', 'value'])\n"
"  key  value\n"
"0  a2      0\n"
"1  b2      1\n"
"2  c2      2\n"
"3  d2      3\n"
"4  e2      4"
msgstr ""
">>> df\n"
"   id         a                            b\n"
"0   1  [a1, b1]  {'a2': 0, 'b2': 1, 'c2': 2}\n"
"1   2      [c1]           {'d2': 3, 'e2': 4}\n"
">>> df[df.id, df.a[0], df.b['b2']]\n"
"   id   a    b\n"
"0   1  a1    1\n"
"1   2  c1  NaN\n"
">>> df[df.id, df.a.len(), df.b.len()]\n"
"   id  a  b\n"
"0   1  2  3\n"
"1   2  1  2\n"
">>> df.a.explode()\n"
"    a\n"
"0  a1\n"
"1  b1\n"
"2  c1\n"
">>> df.a.explode(pos=True)\n"
"   a_pos   a\n"
"0      0  a1\n"
"1      1  b1\n"
"2      0  c1\n"
">>> # designate column names\n"
">>> df.a.explode(['pos', 'value'], pos=True)\n"
"   pos value\n"
"0    0    a1\n"
"1    1    b1\n"
"2    0    c1\n"
">>> df.b.explode()\n"
"  b_key  b_value\n"
"0    a2        0\n"
"1    b2        1\n"
"2    c2        2\n"
"3    d2        3\n"
"4    e2        4\n"
">>> # designate column names\n"
">>> df.b.explode(['key', 'value'])\n"
"  key  value\n"
"0  a2      0\n"
"1  b2      1\n"
"2  c2      2\n"
"3  d2      3\n"
"4  e2      4"

#: ../../source/df-element.rst:381
msgid ""
"explode 也可以和 :ref:`dflateralview` 结合，以将原有列和 explode 的结果"
"相结合，例子如下："
msgstr "You can also combine explode with the lateral view as follows:"

#: ../../source/df-element.rst:383
msgid ""
">>> df[df.id, df.a.explode()]\n"
"   id   a\n"
"0   1  a1\n"
"1   1  b1\n"
"2   2  c1\n"
">>> df[df.id, df.a.explode(), df.b.explode()]\n"
"   id   a b_key  b_value\n"
"0   1  a1    a2        0\n"
"1   1  a1    b2        1\n"
"2   1  a1    c2        2\n"
"3   1  b1    a2        0\n"
"4   1  b1    b2        1\n"
"5   1  b1    c2        2\n"
"6   2  c1    d2        3\n"
"7   2  c1    e2        4"
msgstr ""

#: ../../source/df-element.rst:402
msgid "除了下标、len 和 explode 两个共有方法以外，List 还支持下列方法："
msgstr ""
"In addition to subscripts, len, and explode, the list type also supports "
"the following methods:"

#: ../../source/df-element.rst:405
msgid "list 操作"
msgstr "List operation"

#: ../../source/df-element.rst:407
msgid "contains(v)"
msgstr ""

#: ../../source/df-element.rst:407
msgid "列表是否包含某个元素"
msgstr "Returns whether the list contains the specified element."

#: ../../source/df-element.rst:408
msgid "sort"
msgstr ""

#: ../../source/df-element.rst:408
msgid "返回排序后的列表（返回值为 List）"
msgstr "Sorts the list and returns a list type."

#: ../../source/df-element.rst:411
msgid "Dict 还支持下列方法："
msgstr "Dict also supports the following methods:"

#: ../../source/df-element.rst:414
msgid "dict 操作"
msgstr "Dict operation"

#: ../../source/df-element.rst:416
msgid "keys"
msgstr ""

#: ../../source/df-element.rst:416
msgid "获取 Dict keys（返回值为 List）"
msgstr "Retrieves Dict keys and returns a list type."

#: ../../source/df-element.rst:417
msgid "values"
msgstr ""

#: ../../source/df-element.rst:417
msgid "获取 Dict values（返回值为 List）"
msgstr "Retrieves Dict values and returns a list type."

#: ../../source/df-element.rst:422
msgid "其他元素操作（isin，notin，cut）"
msgstr "Other operations"

#: ../../source/df-element.rst:424
msgid ""
"``isin``\\ 给出Sequence里的元素是否在某个集合元素里。\\ ``notin``\\ 是"
"相反动作。"
msgstr ""
"``isin``\\ or \\ ``notin``\\ returns whether or not the elements in a "
"Sequence object exist in a collection element."

#: ../../source/df-element.rst:426
msgid ""
">>> iris.sepallength.isin([4.9, 5.1]).rename('sepallength').head(5)\n"
"   sepallength\n"
"0         True\n"
"1         True\n"
"2        False\n"
"3        False\n"
"4        False"
msgstr ""

#: ../../source/df-element.rst:437
msgid "cut提供离散化的操作，可以将Sequence的数据拆成几个区段。"
msgstr "The cut method divides Sequence data into several segments."

#: ../../source/df-element.rst:439
msgid ""
">>> iris.sepallength.cut(range(6), labels=['0-1', '1-2', '2-3', '3-4', "
"'4-5']).rename('sepallength_cut').head(5)\n"
"   sepallength_cut\n"
"0             None\n"
"1              4-5\n"
"2              4-5\n"
"3              4-5\n"
"4              4-5"
msgstr ""

#: ../../source/df-element.rst:449
msgid "``include_under``\\ 和\\ ``include_over``\\ 可以分别包括向下和向上的区间。"
msgstr ""
"``include_under``\\ and \\ ``include_over``\\ specify the lower interval "
"and upper interval to be included respectively."

#: ../../source/df-element.rst:451
msgid ""
">>> labels = ['0-1', '1-2', '2-3', '3-4', '4-5', '5-']\n"
">>> iris.sepallength.cut(range(6), labels=labels, "
"include_over=True).rename('sepallength_cut').head(5)\n"
"   sepallength_cut\n"
"0               5-\n"
"1              4-5\n"
"2              4-5\n"
"3              4-5\n"
"4              4-5"
msgstr ""

#: ../../source/df-element.rst:465
msgid "使用自定义函数"
msgstr "Use custom functions"

#: ../../source/df-element.rst:467
msgid ""
"DataFrame函数支持对Sequence使用map，它会对它的每个元素调用自定义函数。"
"比如："
msgstr ""
"DataFrame allows you to call the map method on a Sequence object so as to"
" call custom functions on all of its elements."

#: ../../source/df-element.rst:469
msgid ""
">>> iris.sepallength.map(lambda x: x + 1).head(5)\n"
"   sepallength\n"
"0          6.1\n"
"1          5.9\n"
"2          5.7\n"
"3          5.6\n"
"4          6.0"
msgstr ""

#: ../../source/df-element.rst:480
msgid ""
"目前，受限于 Python UDF，自定义函数无法支持将 list / dict 类型作为输入或"
"输出。"
msgstr ""
"Custom functions are currently not allowed to use lists or dicts as "
"inputs or outputs because of Python UDF limitations."

#: ../../source/df-element.rst:482
msgid "如果map前后，Sequence的类型发生了变化，则需要显式指定map后的类型。"
msgstr ""
"If the type of Sequence has been changed after calling the map method, "
"you need to explicitly specify the new type."

#: ../../source/df-element.rst:484
msgid ""
">>> iris.sepallength.map(lambda x: 't' + str(x), rtype='string').head(5)\n"
"   sepallength\n"
"0         t5.1\n"
"1         t4.9\n"
"2         t4.7\n"
"3         t4.6\n"
"4         t5.0"
msgstr ""

#: ../../source/df-element.rst:494
msgid ""
"如果在函数中包含闭包，需要注意的是，函数外闭包变量值的变化会引起函数内该"
"变量值的变化。例如，"
msgstr ""
"If a function contains a closure, note that if the value of a closure "
"variable changes outside the function, the value of this variable within "
"the function also changes. For example:"

#: ../../source/df-element.rst:496
msgid ""
">>> dfs = []\n"
">>> for i in range(10):\n"
">>>     dfs.append(df.sepal_length.map(lambda x: x + i))"
msgstr ""

#: ../../source/df-element.rst:502
msgid ""
"结果为 dfs 中每个 SequenceExpr 均为 ``df.sepal_length + 9``。为解决此问题"
"，可以将函数作为另一函数的返回值，或者使用 partial，如"
msgstr ""
"Each SequenceExpr object in dfs is ``df.sepal_length + 9`` now. To solve "
"this problem, you can use the function as the return of another function,"
" or use ``functools.partial``. For example:"

#: ../../source/df-element.rst:505
msgid ""
">>> dfs = []\n"
">>> def get_mapper(i):\n"
">>>     return lambda x: x + i\n"
">>> for i in range(10):\n"
">>>     dfs.append(df.sepal_length.map(get_mapper(i)))"
msgstr ""

#: ../../source/df-element.rst:513
msgid "或"
msgstr "Or"

#: ../../source/df-element.rst:515
msgid ""
">>> import functools\n"
">>> dfs = []\n"
">>> for i in range(10):\n"
">>>     dfs.append(df.sepal_length.map(functools.partial(lambda v, x: x +"
" v, i)))"
msgstr ""

#: ../../source/df-element.rst:522
msgid ""
"map也支持使用现有的UDF函数，传入的参数是str类型（函数名）或者 :ref:`"
"Function对象 <functions>` 。"
msgstr ""
"The map method also supports existing UDFs. You can pass in str type "
"parameters, which represent function names, or :ref:`Function objects "
"<functions>`."

#: ../../source/df-element.rst:524
msgid ""
"map传入Python函数的实现使用了ODPS Python UDF，因此，如果用户所在的Project"
"不支持Python UDF，则map函数无法使用。除此以外，所有 Python UDF 的限制在此"
"都适用。"
msgstr ""
"The implementation of ``map`` depends on MaxCompute Python UDF. If your "
"project does not support Python UDF, you cannot use map. All Python UDF "
"limitations apply."

#: ../../source/df-element.rst:527
msgid ""
"目前，第三方库（包含C）只能使用\\ ``numpy``\\ ，第三方库使用参考 :ref:`"
"使用第三方Python库 <third_party_library>`。"
msgstr ""
"The only builtin third-party library in MaxCompute is ``numpy``. If you "
"need to use other libraries, you need to upload these libraries yourself."
" For more information, see :ref:` Use third-party Python "
"libraries<third_party_library>`."

#: ../../source/df-element.rst:531 ../../source/df-element.rst:600
msgid ""
"由于字节码定义的差异，Python 3 下使用新语言特性（例如 ``yield from`` ）时"
"，代码在使用 Python 2.7 的 ODPS Worker 上执行时会发生错误。因而建议在 "
"Python 3 下使用 MapReduce API 编写生产作业前，先确认相关代码是否能正常 "
"执行。"
msgstr ""
"Due to the differences in bytecode definitions, new features supported by"
" Python 3, such as ``yield from``, may cause errors when executed by "
"MaxCompute Worker of Python 2.7. We recommend that you make sure your "
"code executes normally before writing production code using MapReduce API"
" in Python 3."

#: ../../source/df-element.rst:537
msgid ""
"由于 PyODPS DataFrame 默认 Collection / Sequence 等对象均为分布式对象，故"
"不支持在自定义函数内部引用这些对象。 请考虑改用 :ref:`Join 等方法 <"
"dfmerge>` 引用多个 DataFrame 的数据，或者引用 Collection 作为资源，如下文"
"所述。"
msgstr ""
"PyODPS DataFrame recognizes all collections and sequences as distributed "
"objects, and does not support referencing these objects inside user-"
"defined functions. Please consider using :ref:`methods like join "
"<dfmerge>` to reference data in multiple DataFrames, or referencing "
"collections as resources, which is stated in the next section."

#: ../../source/df-element.rst:543
msgid "引用资源"
msgstr "Reference Resources"

#: ../../source/df-element.rst:545
msgid ""
"自定义函数也能读取ODPS上的资源（表资源或文件资源），或者引用一个"
"collection作为资源。 此时，自定义函数需要写成函数闭包或callable的类。"
msgstr ""
"Custom functions can also read MaxCompute resources, such as table and "
"file resources, or reference Collection objects as resources. To do that,"
" you need to write your functions as a closure or callable class."

#: ../../source/df-element.rst:548
msgid ""
">>> file_resource = o.create_resource('pyodps_iris_file', 'file', "
"file_obj='Iris-setosa')\n"
">>>\n"
">>> iris_names_collection = iris.distinct('name')[:2]\n"
">>> iris_names_collection\n"
"       sepallength\n"
"0      Iris-setosa\n"
"1  Iris-versicolor"
msgstr ""

#: ../../source/df-element.rst:558
msgid ""
">>> def myfunc(resources):  # resources按调用顺序传入\n"
">>>     names = set()\n"
">>>     fileobj = resources[0] # 文件资源是一个file-like的object\n"
">>>     for l in fileobj:\n"
">>>         names.add(l)\n"
">>>     collection = resources[1]\n"
">>>     for r in collection:\n"
">>>         names.add(r.name)  # 这里可以通过字段名或者偏移来取\n"
">>>     def h(x):\n"
">>>         if x in names:\n"
">>>             return True\n"
">>>         else:\n"
">>>             return False\n"
">>>     return h\n"
">>>\n"
">>> df = iris.distinct('name')\n"
">>> df = df[df.name,\n"
">>>         df.name.map(myfunc, resources=[file_resource, "
"iris_names_collection], rtype='boolean').rename('isin')]\n"
">>>\n"
">>> df\n"
"              name   isin\n"
"0      Iris-setosa   True\n"
"1  Iris-versicolor   True\n"
"2   Iris-virginica  False"
msgstr ""
">>> def myfunc(resources):  # resources passed by calling order\n"
">>>     names = set()\n"
">>>     fileobj = resources[0] # file resources are file-like objects\n"
">>>     for l in fileobj:\n"
">>>         names.add(l)\n"
">>>     collection = resources[1]\n"
">>>     for r in collection:\n"
">>>         names.add(r.name)  # values can be obtained via column name "
"or column offset\n"
">>>     def h(x):\n"
">>>         if x in names:\n"
">>>             return True\n"
">>>         else:\n"
">>>             return False\n"
">>>     return h\n"
">>>\n"
">>> df = iris.distinct('name')\n"
">>> df = df[df.name,\n"
">>>         df.name.map(myfunc, resources=[file_resource, "
"iris_names_collection], rtype='boolean').rename('isin')]\n"
">>>\n"
">>> df\n"
"              name   isin\n"
"0      Iris-setosa   True\n"
"1  Iris-versicolor   True\n"
"2   Iris-virginica  False"

#: ../../source/df-element.rst:585
msgid "注：分区表资源在读取时不包含分区字段。"
msgstr ""
"Note: when reading the partitioned tables, partition fields are not "
"included."

#: ../../source/df-element.rst:590
msgid "使用第三方Python库"
msgstr "Use third-party Python libraries"

#: ../../source/df-element.rst:591
msgid ""
"现在用户可以把第三方 Wheel 包作为资源上传到 MaxCompute。在全局或者在立即"
"执行的方法时，指定需要使用的包文件， 即可以在自定义函数中使用第三方库。"
"值得注意的是，第三方库的依赖库也必须指定，否则依然会有导入错误。"
msgstr ""
"You can upload third-party wheel packages as resources to MaxCompute. You"
" need to specify the package files globally or in methods that execute "
"DataFrames immediately. Note that you also need to add dependencies of "
"your third-party libraries, or import could fail."

#: ../../source/df-element.rst:594
msgid ""
"如果你的 MaxCompute 服务支持在执行 SQL 时使用镜像，可以在 execute / "
"persist / to_pandas 方法指定 ``image`` 参数以使用镜像。与此同时，"
"DataFrame 的 execute / persist / to_pandas 方法支持增加 ``libraries`` "
"参数以将资源作为三方包。 PyODPS 提供了 ``pyodps-pack`` 工具，可在安装完 "
"PyODPS 后打包三方包及其依赖。如何制作及使用三方包的说明请参考 :ref:`这里 "
"<pyodps_pack>`。"
msgstr ""
"If your MaxCompute service supports specifying images when executing SQL "
"statements, you may specify ``image`` argument with ``execute``, "
"``persist`` or ``to_pandas`` to use these images. Meanwhile ``libraries``"
" argument can be used with ``execute``, ``persist`` or ``to_pandas`` to "
"specify resources as thirdparty libraries. PyODPS installation provides "
"``pyodps-pack`` tool for packing third-party libraries. You may take a "
"look at :ref:`documents here <pyodps_pack>` to see how to build and use "
"these third-party libraries."

#: ../../source/df-element.rst:605
msgid "使用计数器"
msgstr "Use the counter"

#: ../../source/df-element.rst:607
msgid ""
"from odps.udf import get_execution_context\n"
"\n"
"def h(x):\n"
"    ctx = get_execution_context()\n"
"    counters = ctx.get_counters()\n"
"    counters.get_counter('df', 'add_one').increment(1)\n"
"    return x + 1\n"
"\n"
"df.field.map(h)"
msgstr ""

#: ../../source/df-element.rst:619
msgid "logview 的 JSONSummary 中即可找到计数器值。"
msgstr "You can find the value of the counter in the JSONSummary of LogView."

#: ../../source/df-element.rst:623
msgid "调用ODPS内建或者已定义函数"
msgstr "Call MaxCompute built-in functions or UDFs"

#: ../../source/df-element.rst:625
msgid ""
"要想调用 ODPS 上的内建或者已定义函数，来生成列，我们可以使用 ``func`` "
"接口，该接口默认函数返回值为 String， 可以用 rtype 参数指定返回值。"
msgstr ""
"You can use the ``func`` interface to call MaxCompute built-in functions "
"or UDFs to generate columns. This interface returns a string type by "
"default. You can use the rtype parameter to specify the return type."

#: ../../source/df-element.rst:628
msgid ""
">>> from odps.df import func\n"
">>>\n"
">>> iris[iris.name, func.rand(rtype='float').rename('rand')][:4]\n"
">>> iris[iris.name, func.rand(10, rtype='float').rename('rand')][:4]\n"
">>> # 调用 ODPS 上定义的 UDF，列名无法确定时需要手动指定\n"
">>> iris[iris.name, func.your_udf(iris.sepalwidth, iris.sepallength, "
"rtype='float').rename('new_col')]\n"
">>> # 从其他 Project 调用 UDF，也可通过 name 参数指定列名\n"
">>> iris[iris.name, func.your_udf(iris.sepalwidth, iris.sepallength, "
"rtype='float', project='udf_project',\n"
">>>                               name='new_col')]"
msgstr ""
">>> from odps.df import func\n"
">>>\n"
">>> iris[iris.name, func.rand(rtype='float').rename('rand')][:4]\n"
">>> iris[iris.name, func.rand(10, rtype='float').rename('rand')][:4]\n"
">>> # call UDFs defined on MaxCompute. you need to name your result "
"sequence\n"
">>> # explicitly if the name of returned sequence cannot be determined\n"
">>> iris[iris.name, func.your_udf(iris.sepalwidth, iris.sepallength, "
"rtype='float').rename('new_col')]>>> # call a UDF from another project. "
"sequence can also be renamed via name arg\n"
">>> iris[iris.name, func.your_udf(iris.sepalwidth, iris.sepallength, "
"rtype='float', project='udf_project',\n"
">>>                               name='new_col')]"

#: ../../source/df-element.rst:641
msgid "注意：在使用 Pandas 后端时，不支持执行带有 ``func`` 的表达式。"
msgstr ""
"Note: when executing under pandas backend, executing expressions "
"containing ``func`` is not supported."

