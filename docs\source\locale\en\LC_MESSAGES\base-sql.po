# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-17 12:38+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/base-sql.rst:4
msgid "SQL"
msgstr "SQL"

#: ../../source/base-sql.rst:6
msgid ""
"PyODPS支持ODPS SQL的查询，并可以读取执行的结果。 :meth:`~odps.ODPS."
"execute_sql` / :meth:`~odps.ODPS.execute_sql_interactive` / :meth:`~odps."
"ODPS.run_sql` / :meth:`~odps.ODPS.run_sql_interactive` 方法的返回值是 :"
"ref:`运行实例 <instances>` 。"
msgstr ""
"PyODPS supports MaxCompute SQL queries and provides methods to read SQL "
"results. All :meth:`~odps.ODPS.execute_sql` , "
":meth:`~odps.ODPS.execute_sql_interactive` , :meth:`~odps.ODPS.run_sql` "
"and :meth:`~odps.ODPS.run_sql_interactive` methods return :ref:`instances"
" <instances>` ."

#: ../../source/base-sql.rst:12
msgid ""
"并非所有在 ODPS Console 中可以执行的命令都是 ODPS 可以接受的 SQL 语句。 "
"在调用非 DDL / DML 语句时，请使用其他方法，例如 GRANT / REVOKE 等语句请"
"使用 :meth:`~odps.ODPS.run_security_query` 方法，PAI 命令请使用 :meth:`~"
"odps.ODPS.run_xflow` 或 :meth:`~odps.ODPS.execute_xflow` 方法。"
msgstr ""
"The commands that are executable in the MaxCompute Console may not be "
"executed as SQL statements in MaxCompute. Use other methods to execute "
"non-DDL/DML statements. For example, use "
":meth:`~odps.ODPS.run_security_query` method to execute GRANT/REVOKE "
"statements. Use :meth:`~odps.ODPS.run_xflow` or "
":meth:`~odps.ODPS.execute_xflow` method to execute PAI commands."

#: ../../source/base-sql.rst:20
msgid "执行 SQL"
msgstr "Execute SQL statements"

#: ../../source/base-sql.rst:22
msgid ""
"你可以使用 :meth:`~odps.ODPS.execute_sql` 方法以同步方式执行 SQL。调用时"
"，该方法会阻塞直至 SQL 执行完成，并返回一个 :class:`~odps.models.Instance"
"` 实例。如果 SQL 执行报错，该方法会抛出以 ``odps.errors.ODPSError`` 为"
"基类的错误。"
msgstr ""
"You can use :meth:`~odps.ODPS.execute_sql` to run SQL and wait for "
"finish. The method will block until execution is finished and returns an "
":class:`~odps.models.Instance` object. If execution of SQL statement "
"fails, the method will raise an error based on ``odps.errors.ODPSError`` "
"class."

#: ../../source/base-sql.rst:25
msgid ""
">>> o.execute_sql('select * from dual')  # 同步的方式执行，会阻塞直到SQL"
"执行完成"
msgstr ""
">>> o.execute_sql('select * from dual')  # synchronous way, will block "
"till SQL statement finishes execution\n"

#: ../../source/base-sql.rst:29
msgid ""
"你也可以使用非阻塞方式异步执行 SQL。调用时，该方法在将 SQL 提交到 "
"MaxCompute 后即返回 Instance 实例。你需要使用 :meth:`~odps.models."
"Instance.wait_for_success` 方法等待该 SQL 执行完成。\\ 同样地，如果 "
"instance 出现错误，:meth:`~odps.models.Instance.wait_for_success` 会抛出"
"以 ``odps.errors.ODPSError`` 为基类的错误。"
msgstr ""
"You can also use non-blocking method to run SQL. The method will submit "
"your SQL statement to MaxCompute and return the corresponding Instance "
"object. You need to call :meth:`~odps.models.Instance.wait_for_success` "
"method to wait till execution finishes. Similar to "
":meth:`~odps.ODPS.execute_sql`, "
":meth:`~odps.models.Instance.wait_for_success` also raises errors based "
"on ``odps.errors.ODPSError`` if there are any errors with the SQL "
"instance."

#: ../../source/base-sql.rst:33
msgid ""
">>> instance = o.run_sql('select * from dual')  # 异步的方式执行\n"
">>> print(instance.get_logview_address())  # 获取logview地址\n"
">>> instance.wait_for_success()  # 阻塞直到完成"
msgstr ""
">>> instance = o.run_sql('select * from dual')  # asynchronous way\n"
">>> print(instance.get_logview_address())  # obtain LogView address\n"
">>> instance.wait_for_success()  # block till the statement finishes"

#: ../../source/base-sql.rst:39
msgid ""
"关于如何操作 run_sql / execute_sql 返回的 Instance 实例，可以参考 :ref:`"
"运行实例 <instances>` 。"
msgstr ""
"You can take a look at :ref:`instances <instances>` for more information "
"on instance objects returned by run_sql or execute_sql method."

#: ../../source/base-sql.rst:42
msgid "使用 MCQA 执行 SQL"
msgstr "Execute SQL with MCQA acceleration"

#: ../../source/base-sql.rst:43
msgid ""
"`MCQA <https://help.aliyun.com/document_detail/180701.html>`_ 是 "
"MaxCompute 提供的查询加速功能， 支持使用独立资源池对中小规模数据进行加速"
"。PyODPS 从 ******** 开始支持以下列方式通过 MCQA 执行 SQL ，同时需要 "
"MaxCompute 具备 MCQA 的支持。"
msgstr ""
"`MCQA <https://help.aliyun.com/document_detail/180701.html>`_ is the "
"query acceleration service provided by MaxCompute, which supports "
"accelerating queries on small-sized data sets with standalone resource "
"pools. PyODPS supports methods below to execute SQL with MCQA since "
"********. You need to use these methods under a MaxCompute service with "
"MCQA."

#: ../../source/base-sql.rst:47
msgid ""
"你可以使用 :meth:`~odps.ODPS.execute_sql_interactive` 通过 MCQA 执行 SQL "
"并返回 MCQA Instance。如果 MCQA 无法执行相应的 SQL ，会自动回退到传统模式"
"。此时，函数返回的 Instance 为回退后的 Instance。"
msgstr ""
"You can use :meth:`~odps.ODPS.execute_sql_interactive` to execute SQL "
"with MCQA and return MCQA Instance object. If MCQA does not support your "
"SQL statement, it will fallback to traditional mode and return "
"traditional Instance object after fallback."

#: ../../source/base-sql.rst:50
msgid ">>> o.execute_sql_interactive('select * from dual')"
msgstr ""

#: ../../source/base-sql.rst:54
msgid ""
"如果不希望回退，可以指定参数 ``fallback=False``。也可以指定为回退策略（或"
"回退策略的组合，使用逗号分隔的字符串）。 可用的策略名如下。默认策略为 ``"
"all`` （即 ``generic,unsupported,upgrading,noresource,timeout`` ）。"
msgstr ""
"If you don't want the method to fallback automatically, you can specify "
"``fallback=False``. You can also specify a fallback policy or combination"
" of fallback policies with a comma-separated string. Available policy "
"names are listed below. The default policy is ``all``, which is an alias "
"of a combination of ``generic,unsupported,upgrading,noresource,timeout``."

#: ../../source/base-sql.rst:57
msgid "``generic`` ：指定时，表示发生未知错误时回退到传统模式。"
msgstr ""
"``generic`` : once specified, will fallback to traditional mode when "
"unknown error happens."

#: ../../source/base-sql.rst:58
msgid "``noresource`` ：指定时，表示发生资源不足问题时回退到传统模式。"
msgstr ""
"``noresource`` : once specified, will fallback to traditional mode when "
"resource is not sufficient."

#: ../../source/base-sql.rst:59
msgid "``upgrading`` ：指定时，表示升级期间回退到传统模式。"
msgstr ""
"``upgrading`` : once specified, will fallback to traditional mode when "
"the service is upgrading."

#: ../../source/base-sql.rst:60
msgid "``timeout`` ：指定时，表示执行超时时回退到传统模式。"
msgstr ""
"``timeout`` : once specified, will fallback to traditional mode when "
"execution timed out."

#: ../../source/base-sql.rst:61
msgid "``unsupported`` ：指定时，表示遇到 MCQA 不支持的场景时回退到传统模式。"
msgstr ""
"``unsupported`` : once specified, will fallback to traditional mode when "
"MCQA does not support certain SQL statement."

#: ../../source/base-sql.rst:63
msgid "例如，下面的代码要求在 MCQA 不支持和资源不足时回退："
msgstr ""
"For instance, code below requires fallback when resource is not "
"sufficient or MCQA does not support certain statements:"

#: ../../source/base-sql.rst:65
msgid ""
">>> o.execute_sql_interactive('select * from dual', "
"fallback=\"noresource,unsupported\")"
msgstr ""

#: ../../source/base-sql.rst:69
msgid ""
"你也可以使用 :meth:`~odps.ODPS.run_sql_interactive` 通过 MCQA 异步执行 "
"SQL。类似 :meth:`~odps.ODPS.run_sql`，\\ 该方法会在提交任务后即返回 MCQA "
"Instance，你需要自行等待 Instance 完成。需要注意的是，该方法不会自动回退"
"。当执行失败时，\\ 你需要自行重试或执行 :meth:`~odps.ODPS.execute_sql`。"
msgstr ""
"You can also use :meth:`~odps.ODPS.run_sql_interactive` to run SQL with "
"MCQA. The method returns MCQA Instance once your SQL is submitted to the "
"cluster, and you need to wait till Instance finishes like what is needed "
"for :meth:`~odps.ODPS.run_sql`. Note that this method will NOT fallback "
"automatically when it fails. You need to retry or call "
":meth:`~odps.ODPS.execute_sql` yourself."

#: ../../source/base-sql.rst:73
msgid ""
">>> instance = o.run_sql_interactive('select * from dual')  # 异步的方式"
"执行\n"
">>> print(instance.get_logview_address())  # 获取logview地址\n"
">>> instance.wait_for_success()  # 阻塞直到完成"
msgstr ""
">>> instance = o.run_sql_interactive('select * from dual')  # "
"asynchronous way\n"
">>> print(instance.get_logview_address())  # obtain LogView address\n"
">>> instance.wait_for_success()  # block till the statement finishes"

#: ../../source/base-sql.rst:82
msgid "设置时区"
msgstr "Set timezone"

#: ../../source/base-sql.rst:83
msgid ""
"有时我们希望对于查询出来的时间数据显示为特定时区下的时间，可以通过 ``"
"options.local_timezone`` 设置客户端的时区。"
msgstr ""
"Sometimes we want to display the queried time data with a correct "
"timezone. We can set it via ``options.local_timezone``."

#: ../../source/base-sql.rst:85
msgid "``options.local_timezone`` 可设置为以下三种类型："
msgstr "``options.local_timezone`` accepts the following 3 data types:"

#: ../../source/base-sql.rst:87
msgid "``False``：使用 UTC 时间。"
msgstr "``False``: Use the UTC time."

#: ../../source/base-sql.rst:88
msgid "``True``：使用本地时区（默认设置）。"
msgstr "``True``: Use the local timezone (default)."

#: ../../source/base-sql.rst:89
msgid "时区字符串：使用指定的时区，例如 ``Asia/Shanghai``。"
msgstr "Timezone string: Use the passed timezone, e.g. ``Asia/Shanghai``."

#: ../../source/base-sql.rst:91
msgid "例如，使用 UTC 时间："
msgstr "For example, use UTC time:"

#: ../../source/base-sql.rst:93
msgid ""
">>> from odps import options\n"
">>> options.local_timezone = False"
msgstr ""

#: ../../source/base-sql.rst:98
msgid "使用本地时区："
msgstr "Use local timezone:"

#: ../../source/base-sql.rst:100
msgid ""
">>> from odps import options\n"
">>> options.local_timezone = True"
msgstr ""

#: ../../source/base-sql.rst:105
msgid "使用 ``Asia/Shanghai``："
msgstr "Use ``Asia/shanghai``:"

#: ../../source/base-sql.rst:107
msgid ""
">>> from odps import options\n"
">>> options.local_timezone = \"Asia/Shanghai\""
msgstr ""

#: ../../source/base-sql.rst:114
msgid ""
"设置 ``options.local_timezone`` 后，PyODPS 会根据它的值自动设置 ``odps."
"sql.timezone``。 两者的值不同可能导致服务端和客户端时间不一致，因此不应再"
"手动设置 ``odps.sql.timezone``。"
msgstr ""
"After setting ``options.local_timezone``, PyODPS will set "
"``odps.sql.timezone`` according to it automatically. The difference of "
"them may cause the inconsistency of server time and client time, so "
"setting ``odps.sql.timezone`` manually is not recommended."

#: ../../source/base-sql.rst:118
msgid "设置运行参数"
msgstr "Set runtime parameters"

#: ../../source/base-sql.rst:120
msgid ""
"有时，我们在运行时，需要设置运行时参数，我们可以通过设置 ``hints`` 参数，"
"参数类型是 dict。该参数对 :meth:`~odps.ODPS.execute_sql` / :meth:`~odps."
"ODPS.execute_sql_interactive` / :meth:`~odps.ODPS.run_sql` / :meth:`~odps"
".ODPS.run_sql_interactive` 均有效。"
msgstr ""
"You can use the ``hints`` parameter to set runtime parameters. The "
"parameter is a dict type which is supported for "
":meth:`~odps.ODPS.execute_sql`, "
":meth:`~odps.ODPS.execute_sql_interactive`, :meth:`~odps.ODPS.run_sql` "
"and :meth:`~odps.ODPS.run_sql_interactive`."

#: ../../source/base-sql.rst:124
msgid ""
">>> hints = {'odps.stage.mapper.split.size': 16, "
"'odps.sql.reducer.instances': 1024}\n"
">>> o.execute_sql('select * from pyodps_iris', hints=hints)"
msgstr ""
">>> hints = {'odps.stage.mapper.split.size': 16, "
"'odps.sql.reducer.instances': 1024}\n"
">>> o.execute_sql('select * from pyodps_iris', hints=hints)"

#: ../../source/base-sql.rst:129
msgid ""
"我们可以对于全局配置设置sql.settings后，每次运行时则都会添加相关的运行时"
"参数。"
msgstr ""
"You can set sql.settings globally. The relevant runtime parameters are "
"automatically added during each execution."

#: ../../source/base-sql.rst:131
msgid ""
">>> from odps import options\n"
">>> options.sql.settings = {\n"
">>>     'odps.stage.mapper.split.size': 16,\n"
">>>     'odps.sql.reducer.instances': 1024,\n"
">>> }\n"
">>> o.execute_sql('select * from pyodps_iris')  # 会根据全局配置添加hints"
msgstr ""
">>> from odps import options\n"
">>> options.sql.settings = {\n"
">>>     'odps.stage.mapper.split.size': 16,\n"
">>>     'odps.sql.reducer.instances': 1024,\n"
">>> }\n"
">>> o.execute_sql('select * from pyodps_iris')  # global hints configured"
" in options.sql.settings will be added"

#: ../../source/base-sql.rst:143
msgid "读取 SQL 执行结果"
msgstr "View SQL results"

#: ../../source/base-sql.rst:145
msgid ""
"运行 SQL 的 instance 能够直接执行 :meth:`~odps.models.Instance.open_"
"reader` 的操作，一种情况是SQL返回了结构化的数据。"
msgstr ""
"You can execute the :meth:`~odps.models.Instance.open_reader` method to "
"retrieve SQL execution results. In the following example, structured data"
" is returned. "

#: ../../source/base-sql.rst:147
msgid ""
">>> with o.execute_sql('select * from dual').open_reader() as reader:\n"
">>>     for record in reader:\n"
">>>         # 处理每一个record"
msgstr ""
">>> with o.execute_sql('select * from dual').open_reader() as reader:\n"
">>>     for record in reader:\n"
">>>         # process every record"

#: ../../source/base-sql.rst:153
msgid ""
"另一种情况是 SQL 可能执行的比如 ``desc``，这时通过 ``reader.raw`` 属性取"
"到原始的SQL执行结果。"
msgstr ""
"When commands such as ``desc`` are executed, you can use the "
"``reader.raw`` attribute to get the original execution results. "

#: ../../source/base-sql.rst:155
msgid ""
">>> with o.execute_sql('desc dual').open_reader() as reader:\n"
">>>     print(reader.raw)"
msgstr ""

#: ../../source/base-sql.rst:160
msgid ""
"如果 `options.tunnel.use_instance_tunnel == True`，在调用 open_reader 时"
"，PyODPS 会默认调用 Instance Tunnel， 否则会调用旧的 Result 接口。如果你"
"使用了版本较低的 MaxCompute 服务，或者调用 Instance Tunnel 出现了问题，"
"PyODPS 会给出警告并自动降级到旧的 Result 接口，可根据警告信息判断导致降级"
"的原因。如果 Instance Tunnel 的结果不合预期， 请将该选项设为 `False`。在"
"调用 open_reader 时，也可以使用 ``tunnel`` 参数来指定使用何种结果接口，"
"例如"
msgstr ""
"If `options.tunnel.use_instance_tunnel` is set to `True` when open_reader"
" has been executed, PyODPS calls Instance Tunnel by default. If "
"`options.tunnel.use_instance_tunnel` is not set to `True` when "
"open_reader has been executed, PyODPS calls the old Result interface. If "
"you are using an old version of MaxCompute, or an error occurred when "
"calling Instance Tunnel, PyODPS reports a warning and automatically calls"
" the old Result interface instead. If the result of Instance Tunnel does "
"not meet your expectation, set this option to `False`. When calling "
"open_reader, you can also use the ``tunnel`` parameter to specify which "
"result interface to use. For example:"

#: ../../source/base-sql.rst:165
msgid ""
">>> # 使用 Instance Tunnel\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     for record in reader:\n"
">>>         # 处理每一个record\n"
">>> # 使用 Results 接口\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=False) as"
" reader:\n"
">>>     for record in reader:\n"
">>>         # 处理每一个record"
msgstr ""
">>> # Use Instance Tunnel\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     for record in reader:\n"
">>>         # process every record\n"
">>> # Use Results interface\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=False) as"
" reader:\n"
">>>     for record in reader:\n"
">>>         # process every record"

#: ../../source/base-sql.rst:176
msgid ""
"PyODPS 默认不限制能够从 Instance 读取的数据规模，但 Project Owner 可能在 "
"MaxCompute Project 上增加保护设置以限制对 Instance 结果的读取，此时只能"
"使用受限读取模式读取数据，在此模式下可读取的行数受到 Project 配置限制，"
"通常为 10000 行。如果 PyODPS 检测到读取 Instance 数据被限制，且 ``options"
".tunnel.limit_instance_tunnel`` 未设置，会自动启用受限读取模式。 如果你的"
" Project 被保护，想要手动启用受限读取模式，可以为 ``open_reader`` 方法"
"增加 ``limit=True`` 选项，或者设置 ``options.tunnel.limit_instance_tunnel"
" = True``\\ 。"
msgstr ""
"By default, PyODPS does not limit the size of data that can be read from "
"an Instance. However, project owners might add protection configuration "
"on projects to limit reading from instances. At that time you might only "
"be permitted to read under limit mode which limits number of rows to read"
" given configuration in the project, which is 10000 rows. If PyODPS "
"detects the existence of read limit while "
"``options.tunnel.limit_instance_tunnel`` is not set, limit mode is "
"automatically enabled and number of downloadable records is limited. If "
"your project is protected and want to enable limit mode manually, you can"
" add ``limit=True`` option to ``open_reader``, or set "
"``options.tunnel.limit_instance_tunnel = True``."

#: ../../source/base-sql.rst:182
msgid ""
"在部分环境中，例如 DataWorks，``options.tunnel.limit_instance_tunnel`` "
"可能默认被置为 True。此时，如果需要读取\\ 所有数据，需要为 ``open_reader`"
"` 增加参数 `tunnel=True, limit=False` 。需要注意的是，如果 Project 本身被"
"保护，\\ 这两个参数\\ **不能**\\ 解除保护，MaxCompute 也\\ **不提供**\\ "
"绕开该权限限制读取更多数据的方法。此时应联系 Project Owner 开放相应的读"
"权限。"
msgstr ""
"In some environments, for instance, "
"``options.tunnel.limit_instance_tunnel`` might be set to True for "
"compatibility. In those environments, if you want to read all data, you "
"need to add arguments `tunnel=True, limit=False` for ``open_reader`` "
"method. Note that these two arguments will **NOT** lift read limitation "
"on your project, and MaxCompute does not provide any approach to "
"circumvent this limitation without granting read priviledges explicitly. "
"If you still meet read limitations, please ask your project owner to "
"grant read privileges for you."

#: ../../source/base-sql.rst:187
msgid ""
"如果你所使用的 MaxCompute 只能支持旧 Result 接口，同时你需要读取所有数据"
"，可将 SQL 结果写入另一张表后用读表接口读取 （可能受到 Project 安全设置的"
"限制）。"
msgstr ""
"If the MaxCompute version you are using only supports the old Result "
"interface, and you need to read all data, you can export the SQL results "
"to another table and use these methods to read data. This may be limited "
"by project security settings."

#: ../../source/base-sql.rst:190
msgid "同时，PyODPS 支持直接将运行结果数据读成 pandas DataFrame。"
msgstr "PyODPS also supports reading data as pandas DataFrames."

#: ../../source/base-sql.rst:192
msgid ""
">>> # 直接使用 reader 的 to_pandas 方法\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     # pd_df 类型为 pandas DataFrame\n"
">>>     pd_df = reader.to_pandas()"
msgstr ""
">>> # use to_pandas() method of the reader directly\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     # type of pd_df is pandas DataFrame\n"
">>>     pd_df = reader.to_pandas()"

#: ../../source/base-sql.rst:201
msgid "如果需要使用多核加速读取速度，可以通过 ``n_process`` 指定使用进程数:"
msgstr ""
"If you want to accelerate data reading with multiple cores, you can "
"specify ``n_process`` with number of cores you want to use:"

#: ../../source/base-sql.rst:203
msgid ""
">>> import multiprocessing\n"
">>> n_process = multiprocessing.cpu_count()\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     # n_process 指定成机器核数\n"
">>>     pd_df = reader.to_pandas(n_process=n_process)"
msgstr ""
">>> import multiprocessing\n"
">>> n_process = multiprocessing.cpu_count()\n"
">>> with o.execute_sql('select * from dual').open_reader(tunnel=True) as "
"reader:\n"
">>>     # n_process should be number of processes to use\n"
">>>     pd_df = reader.to_pandas(n_process=n_process)"

#: ../../source/base-sql.rst:213
msgid ""
"从 2024 年年末开始，MaxCompute 服务将支持离线 SQL 任务 ``open_reader`` "
"使用与表类似的 Arrow 接口，MCQA 作业暂不支持。在此之前，使用 ``Instance."
"open_reader(arrow=True)`` 读取数据将报错。"
msgstr ""
"It is expected that since late 2024, MaxCompute will support reading "
"results of offline SQL instances into arrow format with "
"``Instance.open_reader`` like tables. MCQA instances do not support this "
"feature by now. Before that time, reading data with "
"``Instance.open_reader(arrow=True)`` will lead to errors."

#: ../../source/base-sql.rst:216
msgid ""
"从 PyODPS 0.12.0 开始，你也可以直接调用 Instance 上的 :meth:`~odps.models"
".Instance.to_pandas` 方法直接将数据转换为 pandas。你可以指定转换为 pandas"
" 的起始行号和行数，若不指定则读取所有数据。该方法也支持 ``limit`` 参数，"
"具体定义与 ``open_reader`` 方法相同。该方法默认会使用 Arrow 格式读取，并"
"转换为 pandas。如果 Arrow 格式不被支持，将会回退到 Record 接口。"
msgstr ""
"Since PyODPS 0.12.0, you can call :meth:`~odps.models.Instance.to_pandas`"
" method on Instance to read instance results into pandas format. Start "
"row number and row count can be specified with this method, or all data "
"will be read. ``limit`` argument is also supported with the same "
"definition as ``open_reader``. This method will try using arrow format if"
" available and convert the result into pandas. If arrow format is not "
"supported by service, it will fall back into record format."

#: ../../source/base-sql.rst:221
msgid ""
">>> inst = o.execute_sql('select * from dual')\n"
">>> pd_df = inst.to_pandas(start=10, count=20)"
msgstr ""

#: ../../source/base-sql.rst:226
msgid ""
"与表类似，从 PyODPS 0.12.0 开始，你也可以使用 Instance 上的 :meth:`~odps."
"models.Instance.iter_pandas` 方法按多个批次读取 pandas DataFrame，参数与 "
"``Table.iter_pandas`` 类似。"
msgstr ""
"Similar to tables, since PyODPS 0.12.0, you can use "
":meth:`~odps.models.Instance.iter_pandas` method of Instance to read "
"pandas DataFrames in multiple batches. The method share similar arguments"
" with ``Table.iter_pandas``."

#: ../../source/base-sql.rst:229
msgid ""
">>> inst = o.execute_sql('select * from dual')\n"
">>> for batch in inst.iter_pandas(start=0, count=1000, batch_size=100):\n"
">>>     print(batch)"
msgstr ""

#: ../../source/base-sql.rst:236
msgid "设置 alias"
msgstr "Set alias"

#: ../../source/base-sql.rst:238
msgid ""
"有时在运行时，比如某个UDF引用的资源是动态变化的，我们可以alias旧的资源"
"名到新的资源，这样免去了重新删除并重新创建UDF的麻烦。"
msgstr ""
"Some resources referenced by a UDF are dynamically changing at runtime. "
"You can create an alias for the old resource and use it as a new "
"resource."

#: ../../source/base-sql.rst:240
msgid ""
"from odps.models import TableSchema\n"
"\n"
"myfunc = '''\\\n"
"from odps.udf import annotate\n"
"from odps.distcache import get_cache_file\n"
"\n"
"@annotate('bigint->bigint')\n"
"class Example(object):\n"
"    def __init__(self):\n"
"        self.n = int(get_cache_file('test_alias_res1').read())\n"
"\n"
"    def evaluate(self, arg):\n"
"        return arg + self.n\n"
"'''\n"
"res1 = o.create_resource('test_alias_res1', 'file', file_obj='1')\n"
"o.create_resource('test_alias.py', 'py', file_obj=myfunc)\n"
"o.create_function('test_alias_func',\n"
"                  class_type='test_alias.Example',\n"
"                  resources=['test_alias.py', 'test_alias_res1'])\n"
"\n"
"table = o.create_table(\n"
"    'test_table',\n"
"    TableSchema.from_lists(['size'], ['bigint']),\n"
"    if_not_exists=True\n"
")\n"
"\n"
"data = [[1, ], ]\n"
"# 写入一行数据，只包含一个值1\n"
"o.write_table(table, 0, [table.new_record(it) for it in data])\n"
"\n"
"with o.execute_sql(\n"
"    'select test_alias_func(size) from test_table').open_reader() as "
"reader:\n"
"    print(reader[0][0])"
msgstr ""
"from odps.models import TableSchema\n"
"\n"
"myfunc = '''\\\n"
"from odps.udf import annotate\n"
"from odps.distcache import get_cache_file\n"
"\n"
"@annotate('bigint->bigint')\n"
"class Example(object):\n"
"    def __init__(self):\n"
"        self.n = int(get_cache_file('test_alias_res1').read())\n"
"\n"
"    def evaluate(self, arg):\n"
"        return arg + self.n\n"
"'''\n"
"res1 = o.create_resource('test_alias_res1', 'file', file_obj='1')\n"
"o.create_resource('test_alias.py', 'py', file_obj=myfunc)\n"
"o.create_function('test_alias_func',\n"
"                  class_type='test_alias.Example',\n"
"                  resources=['test_alias.py', 'test_alias_res1'])\n"
"\n"
"table = o.create_table(\n"
"    'test_table',\n"
"    TableSchema.from_lists(['size'], ['bigint']),\n"
"    if_not_exists=True\n"
")\n"
"\n"
"data = [[1, ], ]\n"
"# write one row with only one value '1'\n"
"o.write_table(table, 0, [table.new_record(it) for it in data])\n"
"\n"
"with o.execute_sql(\n"
"    'select test_alias_func(size) from test_table').open_reader() as "
"reader:\n"
"    print(reader[0][0])"

#: ../../source/base-sql.rst:276
msgid "2"
msgstr ""

#: ../../source/base-sql.rst:280
msgid ""
"res2 = o.create_resource('test_alias_res2', 'file', file_obj='2')\n"
"# 把内容为1的资源alias成内容为2的资源，我们不需要修改UDF或资源\n"
"with o.execute_sql(\n"
"    'select test_alias_func(size) from test_table',\n"
"    aliases={'test_alias_res1': 'test_alias_res2'}).open_reader() as "
"reader:\n"
"    print(reader[0][0])"
msgstr ""
"res2 = o.create_resource('test_alias_res2', 'file', file_obj='2')\n"
"# When we need to replace resource with value '1' with resource with "
"value '2',\n"
"# the only thing we need to do is to use alias argument. Modifying UDFs "
"or resources is not needed.\n"
"with o.execute_sql(\n"
"    'select test_alias_func(size) from test_table',\n"
"    aliases={'test_alias_res1': 'test_alias_res2'}).open_reader() as "
"reader:\n"
"    print(reader[0][0])"

#: ../../source/base-sql.rst:289
msgid "3"
msgstr ""

#: ../../source/base-sql.rst:295
msgid "在交互式环境执行 SQL"
msgstr "Execute SQL statements in an interactive environment"

#: ../../source/base-sql.rst:297
msgid ""
"在 ipython 和 jupyter 里支持 :ref:`使用 SQL 插件的方式运行 SQL <sqlinter>"
"`，且支持 :ref:`参数化查询 <sqlparam>`， 详情参阅 :ref:`文档 <sqlinter>`"
"。"
msgstr ""
"In ipython and jupyter, you can :ref:`use SQL plugins to execute SQL "
"statements<sqlinter>`. Besides, :ref:`parameterized query<sqlparam>` is "
"also supported. For details, see :ref:`Documentation<sqlinter>`."

#: ../../source/base-sql.rst:303
msgid "设置 biz_id"
msgstr "Set biz_id"

#: ../../source/base-sql.rst:305
msgid ""
"在少数情形下，可能在提交 SQL 时，需要同时提交 biz_id，否则执行会报错。"
"此时，你可以设置全局 options 里的 biz_id。"
msgstr ""
"In a few cases, it may be necessary to submit biz_id when submitting SQL "
"statements. Otherwise an error occurs during execution. You can set the "
"biz_id in options globally."

#: ../../source/base-sql.rst:307
msgid ""
"from odps import options\n"
"\n"
"options.biz_id = 'my_biz_id'\n"
"o.execute_sql('select * from pyodps_iris')"
msgstr ""

