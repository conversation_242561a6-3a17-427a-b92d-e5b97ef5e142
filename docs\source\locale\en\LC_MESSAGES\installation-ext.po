# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2014-2018, The Alibaba Group Holding Ltd.
# This file is distributed under the same license as the PyODPS package.
# <AUTHOR> <EMAIL>, 2018.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyODPS 0.7.16\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-17 11:11+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: ../../source/installation-ext.rst:5
msgid "安装指南"
msgstr "Installation instructions"

#: ../../source/installation-ext.rst:7
msgid ""
"如果能访问外网，推荐使用 pip 安装。较新版本的 Python 通常自带 pip。如果你的 Python 不包含 pip，可以参考 `地址 "
"<https://pip.pypa.io/en/stable/installing/>`_ 安装，推荐使用 `阿里云镜像 "
"<http://mirrors.aliyun.com/help/pypi>`_ 加快下载速度。"
msgstr ""
"We recommend that you use pip to install Python on MaxCompute (PyODPS) "
"when you have access to the Internet. pip is often installed with latest "
"releases of Python. If your Python release does not include pip, you  "
"have to install it manually. For more information about installing pip, "
"see `instructions on installing pip "
"<https://pip.pypa.io/en/stable/installing/>`_ ."

#: ../../source/installation-ext.rst:11
msgid "接着升级 pip 和 setuptools 的版本："
msgstr ""
"Make sure that you upgrade your pip and setuptools to the latest version "
"by the following command."

#: ../../source/installation-ext.rst:13
msgid "pip install -U pip setuptools"
msgstr ""

#: ../../source/installation-ext.rst:17
msgid "此后可以安装 PyODPS："
msgstr "Install PyODPS by using the following code:"

#: ../../source/installation-ext.rst:19
msgid "pip install pyodps"
msgstr ""

#: ../../source/installation-ext.rst:23
msgid ""
"如果安装时出现 `urllib3 v2.0 only supports OpenSSL 1.1.1+` 的报错，需要先安装一个兼容旧版 "
"OpenSSL 的 urllib3 版本"
msgstr ""
"If you meet the error `urllib3 v2.0 only supports OpenSSL 1.1.1+` during"
" installation, you need to install an older urllib3 version which is "
"compatible with older OpenSSL versions."

#: ../../source/installation-ext.rst:26
msgid "pip install urllib3\\<2.0"
msgstr ""

#: ../../source/installation-ext.rst:30
msgid "此后再安装 PyODPS。"
msgstr "Then PyODPS can be installed as expected."

#: ../../source/installation-ext.rst:32
msgid "检查安装完成："
msgstr "Check whether the installation has been completed:"

#: ../../source/installation-ext.rst:34
msgid "python -c \"from odps import ODPS\""
msgstr ""

#: ../../source/installation-ext.rst:38
msgid "如果使用的python不是系统默认的python版本，安装完pip则可以："
msgstr ""
"If your version of Python is not the default version, run the following "
"code after installing pip:"

#: ../../source/installation-ext.rst:40
msgid "/home/<USER>/bin/python2.7 -m pip install -U pip setuptools"
msgstr ""

#: ../../source/installation-ext.rst:44
msgid "其余步骤类似。"
msgstr "Repeat these steps to install your version of Python."

